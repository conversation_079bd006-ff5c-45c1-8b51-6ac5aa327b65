package com.zzz.component.bamboo.request;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class QueryProductPageRequest {

    //页码 必传
    private Integer page;
    //页大小，最大值：100 必传
    private Integer limit;

    //是否同时查询库存 0 否 1 是，默认 0
    private Integer queryStock;
    //是否同时查询牌价 0 否 1 是，默认 0
    private Integer querySettlePrice;

    //客户编码，查询牌价必须
    private String customerCode;

    private String currencyCode;

    //商品编码列表
    private List<String> codes;

}
