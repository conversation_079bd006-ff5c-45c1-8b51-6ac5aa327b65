<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.TbTagsTaggingsMapper">

    <select id="selectListBySiteId" resultType="com.zzz.entity.TbTags">
        SELECT
            tg.*
        FROM
            tb_tags_taggings tt
                LEFT JOIN tb_tags tg ON tg.id = tt.tag_id
        WHERE
            tt.site_id = #{siteId};
    </select>
</mapper>
