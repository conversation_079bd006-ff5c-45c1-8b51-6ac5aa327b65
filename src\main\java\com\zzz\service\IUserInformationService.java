package com.zzz.service;

import com.zzz.dto.UserInformationDto;
import com.zzz.entity.UserInformation;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;

/**
 * <p>
 * 用户实名信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface IUserInformationService extends IService<UserInformation> {

    UserInformation getRealName(String userName, String userCard);

    UserInformationDto getRealNameInfo();

    Integer addRealNameInfo(String realname, String idcard,String phone);

    UserInformation getRealNameByUid(Integer userId);
}
