package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 币种
    * </p>
*
* <AUTHOR>
* @since 2024-06-25
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value="Tbcurrency对象", description="币种")
    public class Tbcurrency implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "自增，唯一")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "网点ID")
    private Integer branchId;

            @ApiModelProperty(value = "币种编码")
    private String currency;

            @ApiModelProperty(value = "币种名称")
    private String curname;

            @ApiModelProperty(value = "图标地址")
    private String url;

            @ApiModelProperty(value = "国家或地区")
    private String country;

            @ApiModelProperty(value = "通济隆价格")
    private BigDecimal actualPrice;

            @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

            @ApiModelProperty(value = "状态；1启用、0停用")
    private Integer status;

            @ApiModelProperty(value = "最小单位")
    private Integer unit;

            @ApiModelProperty(value = "1代表热门币种")
    private Integer popular;

            @ApiModelProperty(value = "权重，数字越大越靠前")
    private Integer weight;

            @ApiModelProperty(value = "创建人，导入人")
    private String createby;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

            @ApiModelProperty(value = "数据来源。0max，1 bamboo")
    private Integer dataSource;

            @ApiModelProperty(value = "bamboo数据同步时间")
    private LocalDateTime sycnTime;

            @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "外部币种编码")
    private String outCurCode;


    @ApiModelProperty(value = "外部币种编码")
    private String isDel;
}
