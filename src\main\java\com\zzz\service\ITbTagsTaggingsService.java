package com.zzz.service;

import com.zzz.entity.TbTags;
import com.zzz.entity.TbTagsTaggings;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 网点绑定标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbTagsTaggingsService extends IService<TbTagsTaggings> {

    List<TbTags> selectListBySiteId(Integer id);

    List<TbTagsTaggings> getSitesBytagId(String tagId);
}
