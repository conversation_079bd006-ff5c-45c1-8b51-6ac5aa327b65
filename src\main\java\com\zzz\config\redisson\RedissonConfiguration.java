package com.zzz.config.redisson;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.*;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties.Cluster;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties.Sentinel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConfiguration;

import java.util.List;

/**
 * redisson 配置类
 */
@Slf4j
@Configuration
@ConditionalOnClass(name = {"org.redisson.Redisson"})
@AutoConfigureAfter(RedisConfiguration.class)
public class RedissonConfiguration {

    private final RedisProperties redisProperties;

    public RedissonConfiguration(RedisProperties redisProperties) {
        this.redisProperties = redisProperties;
    }

    private String getSchema() {
        return redisProperties.isSsl() ? "rediss://" : "redis://";
    }

    @Bean
    @Primary
    public RedissonClient redissonClient() {
        final Config config = new Config();

        // Sentinel mode
        if (redisProperties.getSentinel() != null) {
            log.info("------------Redisson Sentinel模式bean加载完成");
            Sentinel sentinel = redisProperties.getSentinel();
            SentinelServersConfig serversConfig = config.useSentinelServers();
            List<String> nodes = sentinel.getNodes();
            String[] sentinelNodes = new String[nodes.size()];
            for (int i = 0; i < sentinelNodes.length; i++) {
                sentinelNodes[i] = getSchema() + nodes.get(i);
            }
            serversConfig.addSentinelAddress(sentinelNodes);
            serversConfig.setMasterName(sentinel.getMaster());
            serversConfig.setReadMode(ReadMode.SLAVE);
            serversConfig.setDatabase(redisProperties.getDatabase());
            serversConfig.setTimeout((int) redisProperties.getTimeout().toMillis());
            if (!StrUtil.isEmpty(redisProperties.getPassword())) {
                serversConfig.setPassword(redisProperties.getPassword());
            }
            return Redisson.create(config);
        }

        // Cluster mode
        if (redisProperties.getCluster() != null) {
            log.info("------------Redisson Cluster模式bean加载完成");
            Cluster cluster = redisProperties.getCluster();
            ClusterServersConfig serversConfig = config.useClusterServers();
            List<String> sentinelNodes = cluster.getNodes();
            serversConfig.addNodeAddress(sentinelNodes.toArray(new String[sentinelNodes.size()]));
            if (!StrUtil.isEmpty(redisProperties.getPassword())) {
                serversConfig.setPassword(redisProperties.getPassword());
            }
            return Redisson.create(config);
        }

        // Single server mode
        if (redisProperties.getHost() != null) {
            log.info("------------Redisson Single模式bean加载完成");
            SingleServerConfig serverConfig = config.useSingleServer();
            serverConfig.setAddress(getSchema() + redisProperties.getHost() + ":" + redisProperties.getPort());
            serverConfig.setDatabase(redisProperties.getDatabase());
            if (!StrUtil.isEmpty(redisProperties.getPassword())) {
                serverConfig.setPassword(redisProperties.getPassword());
            }
            return Redisson.create(config);
        }

        // Incomplete parameter configuration
        throw new IllegalArgumentException("Redis parameter configuration is incomplete");
    }
}

