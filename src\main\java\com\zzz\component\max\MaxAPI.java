package com.zzz.component.max;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zzz.component.HttpUtils;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.request.CheckUserRequest;
import com.zzz.component.max.request.CreateOrderRequest;
import com.zzz.component.max.request.LockCoinStockRequest;
import com.zzz.component.max.request.MaxCurnumRequest;
import com.zzz.vo.QueryPersonalMaxPurchaseLimitVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.TreeMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-13
 **/
@Slf4j
@Component
public class MaxAPI {

    @Resource
    private ApiUrlConstMax apiUrlConstMax;

    //同步网点数据
    public ResponseData siteList(){
        String bodyString = "{\"page\":1,\"size\":100}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getSiteListUrl(), bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    //同步网点数据
    public ResponseData currencyList(String sitecode){
        String bodyString = "{\"sitecode\":\""+sitecode+"\"}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCurrencyListUrl(), bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    //同步零钱包数据
    public  ResponseData coinList(String sitecode){
        String bodyString = "{\"sitecode\":\""+sitecode+"\"}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCoinListUrl(), bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    public  ResponseData getCoinDetail(String sitecode,String currency){
        String bodyString = "{\"currency\":\""+currency+"\",\"sitecode\":\""+sitecode+"\"}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCoinDetailUrl(), bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    //创建订单
    public  ResponseData createOrder(CreateOrderRequest request){
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));

        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCreateOrderUrl(), JSONUtil.toJsonStr(sortedMap));
//        if(!responseData.getCode().equals("200")){
//            return null;
//        }
        return responseData;
    }

    //撤销订单
    public  ResponseData cancelOrder(String orderno){
        String bodyString = "{\"orderno\":\""+orderno+"\"}";
        ResponseData responseData = HttpUtils.doGet(apiUrlConstMax.getCannelOrderUrl()+"?orderno="+orderno, bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }
    //提取水单
    public  ResponseData extractOrder(String orderno){
        String bodyString = "{\"orderno\":\""+orderno+"\"}";
        ResponseData responseData = HttpUtils.doGet(apiUrlConstMax.getExtractOrderUrl()+"?orderno="+orderno, bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    //查询订单详情
    public  ResponseData queryOrder(String orderno){
        String bodyString = "{\"orderno\":\""+orderno+"\"}";
        ResponseData responseData = HttpUtils.doGet(apiUrlConstMax.getQueryOrderUrl()+"?orderno="+orderno, bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    // 查询网点零钱包最大可购买数量
    public ResponseData querySiteCoinBuyNum(String coincode, String sitecode) {
        String bodyString = "{\"coincode\":\"" + coincode + "\",\"sitecode\":\"" + sitecode + "\"}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getSiteCoinBuyNumUrl() + "?coincode=" + coincode + "&sitecode=" + sitecode, bodyString);
        if (!responseData.getCode().equals("200")) {
            return null;
        }
        return responseData;
    }


    //查询币种零钱包库存
    public  ResponseData queryCoinDetail(String currency,String sitecode){
        String bodyString = "{\"currency\":\""+currency+"\",\"sitecode\":\""+sitecode+"\"}";
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getQueryCoinDetailUrl()+"?currency="+currency+"&sitecode="+sitecode, bodyString);
        if(!responseData.getCode().equals("200")){
            return null;
        }
        return responseData;
    }

    /**
     * 锁 零钱包 库存
     */
    public  ResponseData lockCoinStock(LockCoinStockRequest request){
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getLockCoinStockUrl(), JSONUtil.toJsonStr(sortedMap));
        if(!responseData.getCode().equals("200")){
            return responseData;
        }
        return responseData;
    }

    /**
     * 解锁 零钱包 库存
     */
    public  ResponseData unLockCoinStock(LockCoinStockRequest request){
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getUnlockCoinStockUrl(), JSONUtil.toJsonStr(sortedMap));
        if(!responseData.getCode().equals("200")){
            return responseData;
        }
        return responseData;
    }

    /**
     * 用户交易信息校验
     * @param request
     * @return
     */
    public ResponseData checkUser(CheckUserRequest request){
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCheckUserUrl(), JSONUtil.toJsonStr(sortedMap));
        if(!responseData.getCode().equals("200")){
            return responseData;
        }
        return responseData;
    }

    /**
     * 用户币种可兑换最大数量
     */
    public ResponseData maxCurnum(MaxCurnumRequest request) {
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getMaxCurnum(), JSONUtil.toJsonStr(sortedMap));
        if (!"200".equals(responseData.getCode())) {
            return responseData;
        }
        return responseData;
    }


    /**
     * 查询个人最大可购买限额
     * 
     * @param  request 请求实体对象VO
     * @return         个人最大可购买限额
     */
    public ResponseData queryPersonalMaxPurchaseLimit(QueryPersonalMaxPurchaseLimitVo request) {
        Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.queryPersonalMaxPurchaseLimit(), JSONUtil.toJsonStr(sortedMap));
        if (!responseData.getCode().equals("200")) {
            return responseData;
        }
        return responseData;
    }
    
    private static Map<String, Object> convertToSortedMap(JSONObject jsonObject) {

        Map<String, Object> map = new TreeMap<>();

        for (String key: jsonObject.keySet()) {
            map.put(key, jsonObject.get(key));
        }
        return map;
    }


    public static void main(String[] args) {
//        System.out.println(queryCoinDetail("USD","9915"));

        // CreateOrderRequest request = new CreateOrderRequest();
        // request.setPersonName("高炎家");
        // request.setDocno("******************");
        // request.setLinktel("19535569112");
        // request.setCurrency("USD");
        // request.setCardamt(new BigDecimal("1420.82"));
        // request.setUsbAmt(new BigDecimal(200));
        // request.setBuyexchgtype("3222");
        // request.setSitecode("9915");
        // request.setCoinno("20241020004");
        // request.setSummary("W");
        // request.setExtracCode("6532548754");
        // request.setExtracTime("2024-10-20");
        // request.setDisAmount(new BigDecimal("3"));
        // Map<String, Object> sortedMap = convertToSortedMap(JSONUtil.parseObj(request));
        // ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.getCreateOrderUrl(), JSONUtil.toJsonStr(sortedMap));
        // System.out.println(JSONUtil.toJsonStr(responseData));

        //创建水单
//        ResponseData responseData = HttpUtils.doPost(apiUrlConstMax.CREATE_ORDER, "{\"buyexchgtype\":\"3222\",\"cardamt\":1423.82,\"currency\":\"USD\",\"docno\":\"******************\",\"extracCode\":\"123456\",\"extracTime\":\"2024-10-20\",\"linktel\":\"19535569112\",\"personName\":\"高炎家\",\"sitecode\":\"9915\",\"summary\":\"W\",\"usbAmt\":200}");
//        System.out.println(responseData);
        //撤销水单
//        String orderno = "E0604K000018";
//        String bodyString = "{\"orderno\":\""+orderno+"\"}";
//        ResponseData responseData3 = HttpUtils.doGet(apiUrlConstMax.CANNEL_ORDER+"?orderno="+orderno, bodyString);
//        System.out.println(responseData3);
        //水单提取  待处理

//        String idcardb = "https://hongshanshu-test-public.s3.cn-north-1.amazonaws.com.cn/2024-09-11/i45cfptx6ssr.png";
//        String idcarda = "https://hongshanshu-test-public.s3.cn-north-1.amazonaws.com.cn/2024-09-11/bdykoi5nt6l4.png";
//        String signurl = "https://hongshanshu-test-public.s3.cn-north-1.amazonaws.com.cn/2024-10-12/6tzffkycq0t0.png";
//        String orderno = "E0604K000018";
//        String bodyString = "{\"idcarda\":\""+idcarda+"\",\"idcardb\":\""+idcardb+"\",\"orderno\":\""+orderno+"\",\"signurl\":\""+signurl+"\"}";
//        ResponseData responseData4 = HttpUtils.doPost(apiUrlConstMax.EXTRACT_ORDER+"?idcarda="+idcarda+"&idcardb="+idcardb+"&orderno="+orderno+"&signurl="+signurl, bodyString);
//        System.out.println(responseData4);

//        LockCoinStockRequest request = new LockCoinStockRequest();
//        request.setSitecode("990601");
//        request.setCurcode("USD");
//        request.setOutno("6326204629");
//        request.setLockcount(9);
////        ResponseData lockCoinStock = lockCoinStock(request);
////        System.out.println(lockCoinStock);
//
//        ResponseData unLockCoinStock = unLockCoinStock(request);
//        System.out.println(unLockCoinStock);

//        CreateOrderRequest request = new CreateOrderRequest();
//        request.setBuyexchgtype("1");
//        request.setCardamt(new BigDecimal(50.46));
//        request.setDocno("*****************");
//        request.setLinktel("13652549875");
//        request.setPersonName("赵六");
//        request.setSitecode("990601");
//        request.setUsbAmt(new BigDecimal(1000.00));
//        request.setCoinno("");
//        ResponseData order = createOrder(request);
//        System.out.println(order.getData());

//        ResponseData responseData = extractOrder("E2024JULY595354");
//        System.out.println(responseData);

//        ResponseData responseData = cancelOrder("E2024JULY595354");
//        System.out.println(responseData);

//        ResponseData responseData = queryCoinDetail("USD", "990601");
//        System.out.println(responseData);


//        System.out.println(siteList());
//        System.out.println(coinList("991502"));
//        System.out.println(getCoinDetail("991502","USD"));
//        System.out.println(currencyList("991501"));
    }

}
