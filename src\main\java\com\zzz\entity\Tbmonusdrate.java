package com.zzz.entity;

    import java.math.BigDecimal;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 美元折算价维护表，每个月，每个币种，只有一个美元折算价。
    * </p>
*
* <AUTHOR>
* @since 2024-09-17
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value="Tbmonusdrate对象", description="美元折算价维护表，每个月，每个币种，只有一个美元折算价。")
    public class Tbmonusdrate implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "美元折算价月份-主键1")
    private String month;

            @ApiModelProperty(value = "币种-主键2")
    private String currency;

            @ApiModelProperty(value = "美元折算价汇率,此汇率同步入库时，乘以100；后期使用，最终结果要除于100")
    private BigDecimal statrate;


}
