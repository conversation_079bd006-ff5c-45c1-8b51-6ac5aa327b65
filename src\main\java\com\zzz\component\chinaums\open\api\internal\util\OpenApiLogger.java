package com.zzz.component.chinaums.open.api.internal.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/2
 * Time: 10:21
 * 所属模块：
 * 功能说明：
 */
public class OpenApiLogger {
    private static final Log blog = LogFactory.getLog("sdk.biz.err");
    private static final Log clog = LogFactory.getLog("sdk.biz.info");
    private static boolean needEnableLogger = true;

    public static void logInfo(String rsp){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        df.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        StringBuilder sb = new StringBuilder();
        sb.append(df.format(new Date()));
        sb.append("#");
        sb.append(rsp);
        clog.info(sb.toString());
    }

    public static void logError(String rsp)
    {
        if (!needEnableLogger) {
            return;
        }
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        df.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        StringBuilder sb = new StringBuilder();
        sb.append(df.format(new Date()));
        sb.append("###");
        sb.append(rsp);
        blog.error(sb.toString());
    }

    public static void logError(Throwable t)
    {
        if (!needEnableLogger) {
            return;
        }
        blog.error(t);
    }

}
