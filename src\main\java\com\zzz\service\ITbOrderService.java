package com.zzz.service;

import com.zzz.dto.OrderDto;
import com.zzz.dto.OrderPageDto;
import com.zzz.dto.PersonalMaxPurchaseLimitDto;
import com.zzz.emuns.OrderPayTypeEnum;
import com.zzz.entity.TbOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.mq.OrderMessage;
import com.zzz.response.JSONResult;
import com.zzz.vo.ContinuePayOrderVo;
import com.zzz.vo.CreateOrderVo;
import com.zzz.vo.OrderPageVo;
import com.zzz.vo.PayOrderVo;
import com.zzz.vo.QueryPersonalMaxPurchaseLimitVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbOrderService extends IService<TbOrder> {

    JSONResult<OrderPageDto> getOrderPage(OrderPageVo orderPageVo);

    JSONResult<OrderDto> getOrderDetailByNo(String orderno);

    JSONResult createOrder(CreateOrderVo createOrderVo);

    JSONResult payOrder(PayOrderVo payOrderVo);

    JSONResult continuePayOrder(ContinuePayOrderVo continuePayOrderVo);

    JSONResult cancelOrder(String orderno);

    JSONResult delOrder(String orderno);

    JSONResult refundOrder(String orderno);

    boolean closeOrder(OrderMessage orderMessage);

    boolean callback(OrderPayTypeEnum orderPayTypeEnum, String plainBody);

    /**
     * 外管申报鉴权
     *
     * @param orderNo 订单号
     */
    void applyPayCheck(String orderNo);

    /**
     * 查询个人最大可购买限额
     * 
     * @param  request 请求实体对象VO
     * @return         查询个人最大可购买限额
     */
    PersonalMaxPurchaseLimitDto queryPersonalMaxPurchaseLimit(QueryPersonalMaxPurchaseLimitVo request);
}
