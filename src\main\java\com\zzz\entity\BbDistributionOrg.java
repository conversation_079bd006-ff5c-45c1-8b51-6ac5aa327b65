package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 客户表（Bamboo）
    * </p>
*
* <AUTHOR>
* @since 2024-07-12
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("bb_distribution_org")
    @ApiModel(value="BbDistributionOrg对象", description="客户表（Bamboo）")
    public class BbDistributionOrg implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String orgCode;

    private String orgName;

    private String merchantName;

    private String currencyCode;

    private String warehouseCode;

    private Integer deliveryCycleDays;

    private String remark;

    private BigDecimal cancelOrderFee;

    private Integer status;

    private String enterpriseName;

    private Integer businessLicenceNo;

    private String businessLicenceUrl;

    private String businessScope;

    private LocalDateTime businessTimeStart;

    private LocalDateTime businessTimeEnd;

    private String registeredProvinceCode;

    private String registeredProvinceName;

    private String registeredCityCode;

    private String registeredCityName;

    private String registeredRegionCode;

    private String registeredRegionName;

    private String registeredDetailAddress;


}
