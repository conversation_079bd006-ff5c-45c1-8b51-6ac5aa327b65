package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：微信授权 请求入参
 * @ Version:     1.0
 */
@Data
@ApiModel("微信授权参数")
public class WxAuthVo implements Serializable {

    @ApiModelProperty(name = "jsCode", value = "登录时获取的 code，可通过wx.login获取", required = true, example = "0e1hcK000waxjS14Tn400ho09t4hcK0C")
    @NotBlank
    private String jsCode;
}
