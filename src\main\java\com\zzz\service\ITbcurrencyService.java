package com.zzz.service;

import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.dto.CurrencyDto;
import com.zzz.dto.CurrencyPageDto;
import com.zzz.entity.TbCurrencyBase;
import com.zzz.entity.Tbcurrency;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.vo.CurrencyPageVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 币种 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbcurrencyService extends IService<Tbcurrency> {

    List<CurrencyDto> getCurrencyHot();

    CurrencyPageDto getCurrencyPage(CurrencyPageVo currencyPageVo);

    CurrencyDto getCurrencyByCur(String id,Integer branchId);

    List<Tbcurrency> getCurrencyIdsByCurName(String currency);

    CurrencyDto getCurrencyById(Integer curId);

    void addCurrencyInfo(Integer id, List<ProductResultResponse> productList, Map<String, TbCurrencyBase> baseCurMap);


    List<CurrencyDto> getCurrencyListById(String bId);
}
