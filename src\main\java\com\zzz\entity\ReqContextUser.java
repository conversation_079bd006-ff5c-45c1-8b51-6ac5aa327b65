package com.zzz.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 15:45 2023/3/24
 * @ Description：登录的用户信息对象
 * @ Version:     1.0
 */
@Data
@Accessors(chain = true)
public class ReqContextUser implements Serializable {

    @ApiModelProperty(value = "用户id")
    private Integer id;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "手机号是否验证 0 否 1 是")
    private Integer valid;

    public static ReqContextUser create() {
        return new ReqContextUser();
    }
}
