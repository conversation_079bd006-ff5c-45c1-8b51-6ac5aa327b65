package com.zzz.websocket.controller;

import com.zzz.websocket.context.ReqWebSocket;
import com.zzz.websocket.context.ResWebSocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;


/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 14:14 2024/4/11
 * @ Description：
 * @ Version:     1.0
 */
@Slf4j
@Controller
public class SocketController {

    /**
     * 测试
     *
     * @param req {"id":"","token":"","module":"SocketController","method":"test","data":{}}
     * @return
     */
    public ResWebSocket test(ReqWebSocket req) {
        log.info("--------test方法入参：{}", req);
        return ResWebSocket.create().setModule(req.getModule()).setMethod(req.getMethod()).setData("ok");
    }
}
