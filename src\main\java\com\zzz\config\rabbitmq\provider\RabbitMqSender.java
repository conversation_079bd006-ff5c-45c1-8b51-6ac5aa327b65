//package com.zzz.config.rabbitmq.provider;
//
//import com.zzz.config.rabbitmq.EnumsRabbitQueue;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.Map;
//
///**
// * 异步消息产生发送
// *
// * <AUTHOR>
// */
//
//@Slf4j
//@Component
//public class RabbitMqSender {
//
//    @Autowired
//    private RabbitTemplate rabbitTemplate;
//
//    /**
//     * Rabbit 延时队列HEADER
//     */
//    public static final String DELAYED_HEADER = "x-delay";
//
//
//    /**
//     * 发送普通消息
//     *
//     * @param enumsRabbitQueue
//     * @param param
//     */
//    public void sendMessage(EnumsRabbitQueue enumsRabbitQueue, Map<String, Object> param) {
//        rabbitTemplate.convertAndSend(enumsRabbitQueue.getExchange(), enumsRabbitQueue.getDelayKey(), param);
//        log.info("\n【发送普通消息请求队列名称】：{}\n【发送数据】：{}\n【发送时间】：{}", new Object[]{
//                enumsRabbitQueue.getName(), param, LocalDateTime.now()
//        });
//    }
//
//    /**
//     * 发送延时消息
//     *
//     * @param enumsRabbitQueue
//     * @param param
//     */
//    public void sendDelayedMessage(EnumsRabbitQueue enumsRabbitQueue, Map<String, Object> param) {
//        /* 确认的回调 确认消息是否到达 Broker 服务器 其实就是是否到达交换器
//         * 如果发送时候指定的交换器不存在 ack 就是 false 代表消息不可达
//         */
////        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
////            log.info("correlationData：{} , ack:{}", correlationData.getId(), ack);
////            if (!ack) {
////                System.out.println("进行对应的消息补偿机制");
////            } else {
////                System.out.println("hahaha嘎嘎哈哈哈哈哈哈");
////            }
////        });
//        // 在实际中ID 应该是全局唯一 能够唯一标识消息 消息不可达的时候触发ConfirmCallback回调方法时可以获取该值，进行对应的错误处理
////        CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
//        rabbitTemplate.convertAndSend(enumsRabbitQueue.getExchange(), enumsRabbitQueue.getDelayKey(), param,
//                message -> {
//                    message.getMessageProperties().setHeader(DELAYED_HEADER, enumsRabbitQueue.getTtl());
//                    return message;
//                }
//        );
//        log.info("\n【发送延迟消息请求队列名称】：{}\n【发送数据】：{}\n【发送时间】：{}", new Object[]{
//                enumsRabbitQueue.getName(), param, LocalDateTime.now()
//        });
//    }
//
//}
