package com.zzz.component.bamboo.response;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-12
 **/
@Data
public class OrgBranchInfo {

    /**
     * branchCode	String		网点编码
     * branchName	String		网点名称
     * provinceCode	String		省code
     * provinceName	String		省name
     * cityCode	String		市code
     * cityName	String		市name
     * regionCode	String		区code
     * regionName	String		区name
     * detailAddress	String		详细地址
     * contactPerson	String		联系人姓名
     * contactEmail	String		联系邮箱
     * contactTelephone	String		联系方式
     * remark	String		备注
     */
    private String branchCode;
    private String branchName;
    private String provinceCode;
    private String provinceName;
    private String cityCode;
    private String cityName;
    private String regionCode;
    private String regionName;
    private String detailAddress;
    private String contactPerson;
    private String contactEmail;
    private String contactTelephone;
    private String remark;
}
