package com.zzz.controller.v1;

import com.zzz.dto.PersonalMaxPurchaseLimitDto;
import com.zzz.vo.QueryPersonalMaxPurchaseLimitVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zzz.constants.PaymentConstants;
import com.zzz.dto.OrderDto;
import com.zzz.dto.OrderPageDto;
import com.zzz.entity.TbOrder;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbOrderService;
import com.zzz.service.payment.PaymentStrategy;
import com.zzz.service.payment.PaymentStrategyFactory;
import com.zzz.vo.ContinuePayOrderVo;
import com.zzz.vo.CreateOrderVo;
import com.zzz.vo.OrderPageVo;
import com.zzz.vo.PayOrderVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Api(tags = "订单管理")
@Slf4j
@RestController
@RequestMapping("/api/v1/order")
public class OrderController {

    @Autowired
    private ITbOrderService tbOrderService;

    @Autowired
    private PaymentStrategyFactory paymentStrategyFactory;

    /**
     * 用户订单列表
     */
    @ApiOperation("获取用户订单列表")
    @PostMapping("/get_order_page")
    public JSONResult<OrderPageDto> getOrderPage(@RequestBody OrderPageVo orderPageVo) {
        return tbOrderService.getOrderPage(orderPageVo);
    }

    /**
     * 订单详情
     */
    @ApiOperation("获取订单详情（订单编号）")
    @GetMapping("/detail")
    public JSONResult<OrderDto> getOrderDetailByNo(@RequestParam("orderNo") String orderNo) {
        return tbOrderService.getOrderDetailByNo(orderNo);
    }

    /**
     * 外管申报鉴权
     */
    @ApiOperation("外管申报鉴权")
    @GetMapping("/apply/pay-check")
    public JSONResult applyPayCheck(@RequestParam("orderNo") String orderNo) {
        tbOrderService.applyPayCheck(orderNo);
        return JSONResult.ok();
    }

    /**
     * 用户下单
     */
    @ApiOperation("预下单")
    @PostMapping("/create")
    // 后续采用 toke令牌 防重 TODO
//    @RepeatSubmit(limitType = RepeatSubmit.Type.TOKEN)
    public JSONResult createOrder(@RequestBody CreateOrderVo createOrderVo) {
        return tbOrderService.createOrder(createOrderVo);
    }

    /**
     * 第三方支付
     */
    @ApiOperation(value = "支付订单(获取支付参数)", notes = "根据支付类型返回对应的支付参数，支持微信支付、支付宝支付和云闪付")
    @PostMapping("/pay")
    public JSONResult payOrder(@RequestBody PayOrderVo payOrderVo) {
        return tbOrderService.payOrder(payOrderVo);
    }

    /**
     * 继续支付
     */
    @ApiOperation(value = "重新支付", notes = "对未支付的订单重新发起支付，返回支付参数")
    @PostMapping("/continue")
    public JSONResult continuePayOrder(@RequestBody ContinuePayOrderVo continuePayOrderVo) {
        return tbOrderService.continuePayOrder(continuePayOrderVo);
    }

    /**
     * 取消订单
     */
    @ApiOperation(value = "取消订单", notes = "待支付和已支付完成才可以取消订单，有优惠券将同步取消")
    @GetMapping("/cancel")
    public JSONResult cancelOrder(@RequestParam("orderNo") String orderNo) {
        return tbOrderService.cancelOrder(orderNo);
    }

    /**
     * 删除订单
     */
    @ApiOperation(value = "删除订单", notes = "只有状态为：4已提取 6已退款 7已取消，才可删除")
    @DeleteMapping("/del")
    public JSONResult delOrder(@RequestParam("orderno") String orderno) {
        return tbOrderService.delOrder(orderno);
    }

    /**
     * 申请退款
     */
    @ApiOperation(value = "申请退款", notes = "支付成功订单，取消后才可以申请退款")
    @GetMapping("/refund")
    public JSONResult refundOrder(@RequestParam("orderno") String orderno) {
        return tbOrderService.refundOrder(orderno);
    }

    /**
     * 查询支付状态
     * @param orderNo 订单号
     * @return 支付状态
     */
    @ApiOperation(value = "查询支付状态", notes = "查询订单的支付状态")
    @GetMapping("/payment/query")
    public JSONResult<Boolean> queryPaymentStatus(@RequestParam("orderNo") String orderNo) {
        // 查询订单
        TbOrder order = tbOrderService.getOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TbOrder>()
                        .eq(TbOrder::getOrderNumber, orderNo)
        );
        if (order == null) {
            return JSONResult.error("订单不存在");
        }
        // 获取支付类型
        String payType = order.getPayType();
        if (payType == null || payType.isEmpty()) {
            payType = PaymentConstants.PaymentType.WECHAT;
        }
        // 获取支付策略
        PaymentStrategy paymentStrategy = paymentStrategyFactory.getStrategy(payType);
        if (paymentStrategy == null) {
            return JSONResult.error("不支持的支付类型");
        }
        // 查询支付状态
        boolean status = paymentStrategy.queryPaymentStatus(orderNo, order.getOutOrderNo());
        return JSONResult.ok(status);
    }

    /**
     * 查询个人最大可购买限额
     * 
     * @param  request 请求实体对象VO
     * @return         查询个人最大可购买限额
     */
    @PostMapping("/query_personal_max_purchase_limit")
    @ApiOperation("查询个人最大可购买限额")
    public JSONResult<PersonalMaxPurchaseLimitDto> queryPersonalMaxPurchaseLimit(
            @RequestBody QueryPersonalMaxPurchaseLimitVo request) {
        return JSONResult.ok(tbOrderService.queryPersonalMaxPurchaseLimit(request));
    }
}
