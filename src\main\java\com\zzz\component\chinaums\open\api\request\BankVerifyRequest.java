package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.response.BankVerifyResponse;
import com.zzz.component.chinaums.open.api.annotation.ApiField;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/2
 * Time: 14:17
 * 所属模块：
 * 功能说明：银行卡验证
 */
public class BankVerifyRequest implements OpenApiRequest<BankVerifyResponse> {

    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;

    public Class<BankVerifyResponse> responseClass() {
        return BankVerifyResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "银行卡实名认证";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/bankcard/verify";
    }

    public boolean needToken() {
        return true;
    }
    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
