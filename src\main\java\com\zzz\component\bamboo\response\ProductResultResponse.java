package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *  商品查询 响应数据
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class ProductResultResponse {


    private BigDecimal freezeStockNum;
    private BigDecimal availableStockNum;
    private BigDecimal realStockNum;
    private BigDecimal virtualAvailableStockNum;
    private Long merchantId;
    private String merchantCode;
    private String merchantName;
    private Long storeId;
    private String storeName;
    private String storeCode;
    private Long warehouseId;
    private String warehouseCode;
    private String warehouseName;
    private Long id;
    private Long mpId;
    private String chineseName;
    private String code;
    private Integer type;
    private String typeStr;
    private String channelCode;
    private Long categoryId;
    private String categoryName;
    private String fullIdPath;
    private Integer typeOfProduct;
    private Integer canSale;
    private String mainUnitName;
    private String mainPictureUrl;
    private Integer frontCanSale;
    private String currencyCode;
    private String currencyFaceValue;
    private String currencyFaceAmount;
    private BigDecimal settlePrice;
    private String settleCurrencyCode;
    private String settleCurrencyName;
}
