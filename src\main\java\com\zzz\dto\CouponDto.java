package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("优惠券信息实体")
public class CouponDto {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "金额")
    private BigDecimal couponPrice;

    @ApiModelProperty(value = "类型 1折扣 2满减 ")
    private String couponType;

    @ApiModelProperty(value = "满减金额")
    private BigDecimal cutPrice;

//    @ApiModelProperty(value = "开始时间")
//    private LocalDateTime startTime;
//
//    @ApiModelProperty(value = "结束时间")
//    private LocalDateTime endTime;


    @ApiModelProperty(value = "可领券用户类型")
    private String couponCustomerType;

    @ApiModelProperty(value = "限领数量")
    private String numberLimit;

    @ApiModelProperty(value = "优惠卷说明")
    private String remarks;
}
