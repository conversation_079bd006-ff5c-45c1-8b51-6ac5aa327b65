package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：微信授权 响应
 * @ Version:     1.0
 */
@Data
@Accessors(chain = true)
@ApiModel("微信授权成功返回实体")
public class WxAuthDto implements Serializable {

    @ApiModelProperty(name = "sessionKey", value = "微信sessionKey", required = true,example = "0dOsiMysyzboEsie7LGTlQ==")
    private String sessionKey;

    @ApiModelProperty(name = "openId", value = "微信用户openId", required = true,example = "oAnzM5Mjv0CfGBBas3Z5Eq8-urYs")
    private String openId;

    public static WxAuthDto create() {
        return new WxAuthDto();
    }
}
