package com.zzz.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 * @since 2024-07-17
 **/
@Data
public class CoinChildDto {


    /**
     * 主键，唯一
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 零钱包ID
     */
    @ApiModelProperty("零钱包ID")
    private Integer coinId;

    /**
     * 面额
     */
    @ApiModelProperty("面额")
    private String aclValue;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer num;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * '创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createdate;
}
