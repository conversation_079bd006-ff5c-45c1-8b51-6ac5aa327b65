//package com.zzz.config.rabbitmq;
//
//import com.rabbitmq.client.Channel;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.Map;
//
///**
// * @ Author     ：zqw.
// * @ Date       ：Created in 14:49 2022/11/15
// * @ Description：异步消息接收消费
// * @ Version:     1.0
// */
//@Slf4j
//@Component
//public class TestCus {
//
//    /**
//     * 接收测试消息
//     *
//     * @param param {"phone":"手机号","dispatchCode":"验证码"}
//     */
//    @RabbitListener(queues = RabbitConstant.TEST_MESSAGE_QUEUE_NAME)
//    public void delayedSendMessage(Map<String, Object> param, Message message, Channel channel) {
//        log.info("\n【接收接收消息发送延迟请求】：{}\n【接收数据】：{}\n【接收时间】：{}", new Object[]{
//                EnumsRabbitQueue.TEST_QUEUE.getName(), param, LocalDateTime.now()
//        });
//        try {
//            log.info("----mq查询到的信息：{}", "1");
//        } catch (Exception e) {
//            log.info("重发mq消息:{},失败信息：{}", new String(message.getBody()), e.getMessage());
//        }
//    }
//}
