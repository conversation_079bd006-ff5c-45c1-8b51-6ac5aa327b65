package com.zzz.emuns;

/**
 * 订单所属渠道枚举
 * <AUTHOR> to 2024/7/18
 */
public enum ChannelTypeEnum {
    /**
     * 订单所属渠道。0：max ,1：bamboo
     */
    MAX(0, "max"),
    BAMBOO(1, "bamboo"),

    ;

    private final Integer value;
    private final String label;

    ChannelTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据指定value解析成枚举常量
     */
    public static ChannelTypeEnum resolve(Integer value, ChannelTypeEnum dv) {
        for (ChannelTypeEnum s : values()) {
            if (s.value.equals(value)) {
                return s;
            }
        }
        return dv;
    }

    public static ChannelTypeEnum resolve(Integer value) {
        return resolve(value, null);
    }

    /**
     * 判断 value 值是否是枚举里的值
     */
    public boolean matches(Integer value) {
        return this.value.equals(value);
    }

    public Integer value() {
        return value;
    }

    public String label() {
        return label;
    }
}
