package com.zzz.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zzz.constants.ProjectConstant;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.User;
import com.zzz.exception.ServiceException;
import com.zzz.service.IUserService;
import com.zzz.util.UserContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 17:55 2023/3/23
 * @ Description：统一请求拦截器
 * @ Version:     1.0
 */
@Slf4j
@Component
public class ValidRequestInterceptor implements HandlerInterceptor {

    @Autowired
    private IUserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        log.info("---------------------------service进入前置拦截");
        String token = request.getHeader(ProjectConstant.TOKEN);
        String id = request.getHeader(ProjectConstant.ID);
        String path = request.getRequestURI();
        log.info("----------path:{}", path);
        //免登录 路径
        ArrayList<String> noLoginPath = CollUtil.newArrayList("/api/v1/auth/loginPhone", "/api/v1/auth/wx",
                "/api/v1/auth/wx/phone", "/api/v1/common/sms/send", "/api/v1/auth/test1",
                "/api/v1/common/get_application_book",
                "/api/v1/common/get_service",
                "/api/v1/common/get_stealth",
                "/api/v1/common/get_exchange_notice",
                "/api/v1/common/get_banner",
                "/api/v1/common/get_urgent_fee",
                "/api/v1/common/get_purpose_list",

                "/api/v1/coupon/get_coupon_page",
                "/api/v1/branch/get_branch_page",
                "/api/v1/branch/get_tags_list",
                "/api/v1/branch/get_branch",


                "/api/v1/banner/get_banner_list",

                "/api/v1/currency/get_hot",
                "/api/v1/currency/get_currency_page",

                "/api/v1/branch/get_urgent_list_by_branchid",
                "/api/v1/branch/get_urgentfee_detail",

                "/api/v1/coin/get_coin_purse",

                "/api/v1/auth/loginH5",

                "/api/v1/sync/bamboo/customer",
                "/api/v1/sync/bamboo/product",

                "/api/v1/auth/alipay/loginPhone",
                "/api/v1/auth/alipay",
                "/api/v1/auth/alipay/phone"

                );
        /**
         * 登录流程处理
         */
        //需要登录的 path
        if (!noLoginPath.contains(path)) {
            if (StrUtil.isEmpty(token) || StrUtil.isEmpty(id)) throw new ServiceException(CommonErrorEnum.USER_NO_LOGIN_ERROR);
            StpUtil.checkLogin();
            //获取用户其他信息
            ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
            if (!reqContextUser.getId().equals(Integer.valueOf(id))) throw new ServiceException(CommonErrorEnum.USER_LOGIN_ERROR);
            //判断用户是否验证手机号
            List<String> authList = Arrays.asList("/api/v1/auth/validPhone","/api/v1/auth/alipay/validPhone");
            if (!authList.contains(path)) {
                //如果没有验证手机号码 给前端一个固定的 错误码 跳转验证页面
                if (!reqContextUser.getValid().equals(1))
                    throw new ServiceException(CommonErrorEnum.NON_VALID_PHONE.getErrorCode(), CommonErrorEnum.NON_VALID_PHONE.getErrorMsg());
            }
            //把用户信息存入线程中
            UserContextUtil.set(reqContextUser);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        log.info("---------------------------service进入后置拦截");
        UserContextUtil.remove();
    }
}
