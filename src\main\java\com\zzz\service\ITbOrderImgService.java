package com.zzz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.entity.TbOrderImg;

/**
 * <p>
 * 订单图片表 服务类
 * </p>
 * <AUTHOR>
 * @since 2024-10-13
 */
public interface ITbOrderImgService extends IService<TbOrderImg> {

    /**
     * 新增/修改max水单地址
     * @param icedownloadurl 水单下载地址，二维码
     * @param iceimgurl      完整水单图片地址
     * @param icepdfurl      PDF存放地址
     * <AUTHOR> to 2024/10/13
     */
    Integer addOrUpdateMaxIce(String orderNumber, String icedownloadurl, String iceimgurl, String icepdfurl);
}
