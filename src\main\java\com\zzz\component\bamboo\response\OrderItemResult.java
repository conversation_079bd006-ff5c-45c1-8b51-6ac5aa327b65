package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class OrderItemResult {

    private String productCname;
    private String code;
    private String unit;
    private BigDecimal productItemNum;
    private BigDecimal productItemAmount;
    private String currencyCode;
    private String currencyFaceValue;
    private BigDecimal currencyFaceAmount;
    private String warehouseCode;
    private String warehouseName;
    private String itemStatusStr;
}
