<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.TbCoinBaseMapper">

    <select id="list" resultType="com.zzz.dto.CoinBaseDto">
        SELECT tcp.id,
        tcp.bizcode,
        tcp.currency,
        tcp.name,
        tcp.price,
        tcp.par_value,
        tcp.remarks,
        tcp.create_time,
        tcp.state,
        tcb.cur_code,
        tcb.cur_name
        FROM `tb_coin_base` tcp
        LEFT JOIN tb_currency_base tcb ON tcp.currency = tcb.cur_code
        <where>
            tcp.is_del = '0' and tcp.state = '0'
            <if test="currency != null and currency != ''">
                AND tcp.currency = #{currency}
            </if>
        </where>
    </select>
</mapper>
