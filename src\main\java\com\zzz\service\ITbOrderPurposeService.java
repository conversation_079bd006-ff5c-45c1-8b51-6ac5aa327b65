package com.zzz.service;

import com.zzz.dto.PurposeDto;
import com.zzz.entity.TbOrderPurpose;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用途 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbOrderPurposeService extends IService<TbOrderPurpose> {

    List<PurposeDto> getPurposeList();

    TbOrderPurpose getPurposeById(Integer purpose);
}
