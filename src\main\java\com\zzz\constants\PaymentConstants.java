package com.zzz.constants;

/**
 * 支付相关常量
 * 定义支付类型、支付机构、交易类型等常量
 */
public class PaymentConstants {

    /**
     * 支付类型常量
     */
    public static class PaymentType {
        /**
         * 微信支付
         */
        public static final String WECHAT = "WECHAT";

        /**
         * 支付宝支付
         */
        public static final String ALIPAY = "ALIPAY";

        /**
         * 银联支付（云闪付）
         */
        public static final String UNIONPAY = "UNIONPAY";
    }

    /**
     * 支付机构ID常量
     */
    public static class InstMid {
        /**
         * 微信小程序支付
         */
        public static final String WECHAT_MINI = "WECHATPAY2_MINIAPP";

        /**
         * 支付宝小程序支付
         */
        public static final String ALIPAY_MINI = "ALIPAY_MINIAPP";

        /**
         * 银联小程序支付
         */
        public static final String UNIONPAY_MINI = "UNIONPAY_MINIAPP";
    }

    /**
     * 交易类型常量
     */
    public static class TradeType {
        /**
         * 小程序支付
         */
        public static final String MINI = "MINI";

        /**
         * 扫码支付
         */
        public static final String NATIVE = "NATIVE";

        /**
         * APP支付
         */
        public static final String APP = "APP";

        /**
         * H5支付
         */
        public static final String H5 = "H5";

        /**
         * 公众号支付
         */
        public static final String JSAPI = "JSAPI";
    }

    /**
     * 支付状态常量
     */
    public static class PayStatus {
        /**
         * 支付成功
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 支付中
         */
        public static final String PAYING = "PAYING";

        /**
         * 支付失败
         */
        public static final String FAIL = "FAIL";

        /**
         * 已关闭
         */
        public static final String CLOSED = "CLOSED";

        /**
         * 已退款
         */
        public static final String REFUNDED = "REFUNDED";

        /**
         * 部分退款
         */
        public static final String PARTIAL_REFUNDED = "PARTIAL_REFUNDED";
    }

    /**
     * 退款状态常量
     */
    public static class RefundStatus {
        /**
         * 退款成功
         */
        public static final String SUCCESS = "SUCCESS";

        /**
         * 退款处理中
         */
        public static final String PROCESSING = "PROCESSING";

        /**
         * 退款失败
         */
        public static final String FAIL = "FAIL";
    }
}
