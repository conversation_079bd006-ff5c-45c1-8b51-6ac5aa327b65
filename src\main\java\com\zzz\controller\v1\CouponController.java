package com.zzz.controller.v1;

import com.zzz.dto.CouponPageDto;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbCouponMrgService;
import com.zzz.vo.CouponPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Api(tags = "优惠券")
@Slf4j
@RestController
@RequestMapping("/api/v1/coupon")
public class CouponController {

    @Autowired
    private ITbCouponMrgService iTbCouponMrgService;

    /**
     * 系统优惠券列表
     */
    @ApiOperation("系统优惠券列表(分页)")
    @PostMapping("/get_coupon_page")
    public JSONResult<CouponPageDto> getCouponPage(@RequestBody CouponPageVo couponPageVo){
        return JSONResult.ok(iTbCouponMrgService.getCouponPage(couponPageVo));
    }

}
