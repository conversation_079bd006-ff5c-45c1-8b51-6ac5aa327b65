package com.zzz.exception;

import com.zzz.emuns.CommonErrorEnum;
import org.springframework.http.HttpStatus;

/**
 * 通用业务异常
 */
public class ServiceException extends RuntimeException {

    private Integer errorCode;

    private String message;

    public ServiceException(String message) {
        this.errorCode = HttpStatus.INTERNAL_SERVER_ERROR.value();
        this.message = message;
    }

    public ServiceException(Integer errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    public ServiceException(CommonErrorEnum commonErrorEnum) {
        this.errorCode = commonErrorEnum.getErrorCode();
        this.message = commonErrorEnum.getErrorMsg();
    }

    public String getMessage() {
        return message;
    }

    public Integer getErrorCode() {
        return errorCode;
    }
}