package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-08-10
 **/
@Data
@Accessors(chain = true)
@ApiModel("支付宝授权成功返回实体")
public class AlipayAuthDto {

    @ApiModelProperty(name = "token", value = "用户唯一token", required = true,example = "0dOsiMysyzboEsie7LGTlQ==")
    private String token;

    @ApiModelProperty(name = "openid", value = "支付宝用户openid", required = true,example = "oAnzM5Mjv0CfGBBas3Z5Eq8-urYs")
    private String openid;

    public static AlipayAuthDto create() {
        return new AlipayAuthDto();
    }
}
