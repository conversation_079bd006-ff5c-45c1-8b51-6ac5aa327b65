package com.zzz.util.sms;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.ClientException;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.zzz.constants.PattenConstant;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.RedisKeyEnum;
import com.zzz.entity.CommonInfo;
import com.zzz.exception.ServiceException;
import com.zzz.service.ICommonInfoService;
import com.zzz.util.RedisUtil;
import com.zzz.util.environment.EnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 11:39 2022/12/12
 * @ Description：发送短信util
 * @ Version:     1.0
 */
@Slf4j
@Component
public class SmsUtil {

    @Autowired
    private RedisUtil redisUtil;

        @Autowired
    private ICommonInfoService commonInfoService;

    /**
     * 发送短信
     *
     * @param phone        手机号码
     * @param typeConstant 短信类型
     * @return 非正式环境返回 code 反正返回null
     */
    public String sendSms(String phone, String typeConstant) {
        log.info("-------------进入发送手机短信方法------------------");
        boolean match = ReUtil.isMatch(PattenConstant.PHONE, phone);
        if (!match) throw new ServiceException("手机号码非法");
        //手机号码发送间隔限制 120s之内不能重复发送 TODO 正式环境 开启 限制
        long expire = redisUtil.getExpire(RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone);
        if (expire >= 60 * 3) throw new ServiceException("操作频繁，请稍后重试!");
        //随机生成6位数 数字验证码t
        String code = RandomUtil.randomNumbers(6);
                log.info("-------------发送短信,环境{},手机号码{},验证码{},类型{}",
        EnvironmentUtil.getActiveProfile(), phone, code, typeConstant);
                String result=null;
        if (EnvironmentUtil.isDev()) {
            code = "654321";
            //非正式环境，模拟发送短信
            //存入redis
            redisUtil.set(RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone,
                    code, RedisKeyEnum.PHONE_VALID.getExpireTime());
            //根据短信通道发送
            log.info("-------------非正式环境短信发送成功------------------");
            return code;
        } else {
            //正式环境，调用对应的sdk
            redisUtil.set(RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone,
                    code, RedisKeyEnum.PHONE_VALID.getExpireTime());
            //根据短信通道发送
            result = sendMsgAlibaba(phone, typeConstant, code);
            log.info("-------------正式环境短信发送成功------------------");
            return result;
        }
    }

    /**
     * 校验短信
     *
     * @param phone        手机号码
     * @param code         短信验证码
     * @param typeConstant 短信类型
     * @param flag         是否删除key
     */
    public void validSms(String phone, String code, String typeConstant, boolean flag) {
        log.info("-------------进入校验手机短信方法------------------");
        String key = RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone;
        log.info("-------------校验短信,环境{},手机号码{},验证码{},类型{},缓存路径{}",
                EnvironmentUtil.getActiveProfile(), phone, code, typeConstant, key);
        //校验短信
        Object codeObj = redisUtil.get(key);
        //验证码失效
        if (codeObj == null) throw new ServiceException("验证码失效!");
        //验证码错误
        String codeString = codeObj.toString();
        if (!codeString.equals(code)) throw new ServiceException("验证码错误!");
        //删除key
        if (flag) redisUtil.del(RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone);
    }

    /**
     * 删除缓存的验证码
     *
     * @param phone        手机号码
     * @param typeConstant 短信类型
     */
    public void delSmsCache(String phone, String typeConstant) {
        redisUtil.del(RedisKeyEnum.PHONE_VALID.getKey() + typeConstant + phone);
    }


    private String sendMsgAlibaba(String phone, String state, String code) {
        List<CommonInfo> list = commonInfoService.getBaseInfoByCode(CommonEnum.ALI_CONFIG_P);
        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));

        List<CommonInfo> smsList = commonInfoService.getBaseInfoByCode(CommonEnum.ALI_SMS_TEMPLATE_P);
        Map<String, String> smsMap = smsList.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));

        //阿里云短信accessKeyId
        String accessKeyId = WxParamMap.get(CommonEnum.ALI_KEY.name());
        //阿里云短信accessSecret
        String accessSecret = WxParamMap.get(CommonEnum.ALI_ACCESS_SECRET.name());
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessSecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", "cn-hangzhou");
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", "通济隆外币兑换");
        String value;
        switch (state) {
            case "register":
                value = smsMap.get(CommonEnum.ALI_OPEN_SMS.name());
                break;
            case "success":
                value = smsMap.get(CommonEnum.ALI_SMS3.name());
                break;
            case "orderRefund":
                value = smsMap.get(CommonEnum.ALI_SMS2.name());
                break;
            case "code":
                value = smsMap.get(CommonEnum.ALI_SMS1.name());
                break;
            case "loginh5":
                value = smsMap.get(CommonEnum.ALI_SMS1.name());
                break;
            default:
                value = smsMap.get(CommonEnum.ALI_SMS1.name());
                break;
        }
        request.putQueryParameter("TemplateCode", value);
        if (!"cancel".equals(state)) {
            request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        }

        try {
            CommonResponse response = client.getCommonResponse(request);
            System.out.println(response.getData());
            String data = response.getData();
            JSONObject jsonObject = JSON.parseObject(data);
            if ("OK".equals(jsonObject.get("Code"))) {

                return "success";
            } else {
                if (jsonObject.get("Message").toString().contains("分钟")) {
                    return "短信发送过于频繁，请一分钟后再试！";
                } else if (jsonObject.get("Message").toString().contains("小时")) {
                    return "短信发送过于频繁，请一小时后再试！";
                } else if (jsonObject.get("Message").toString().contains("天")) {
                    return "短信发送过于频繁，请明天再试！";
                }
                log.info(jsonObject.get("Message").toString());
                return "短信发送失败！";
            }
        } catch (ClientException | com.aliyuncs.exceptions.ClientException e) {
            e.printStackTrace();
        }
        return "验证码发送失败";
    }
}
