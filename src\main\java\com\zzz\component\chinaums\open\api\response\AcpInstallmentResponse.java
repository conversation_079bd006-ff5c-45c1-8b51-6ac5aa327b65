package com.zzz.component.chinaums.open.api.response;

import com.zzz.component.chinaums.open.api.OpenApiResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/12/12
 * Time: 14:51
 * To change this template use File | Settings | File Templates.
 */
public class AcpInstallmentResponse  extends OpenApiResponse {
    /**
     * 错误代码
     */
    private String errCode;
    /**
     * 错误信息
     */
    private String errInfo;
    /**
     * 交易时间 HHmmss
     */
    private String transactionTime;
    /**
     * 交易日期 MMdd
     */
    private String transactionDate;
    /**
     * 结算日期 MMdd
     */
    private String settlementDate;
    /**
     * 检索参考号
     */
    private String retrievalRefNum;

    @Override
    public String getErrCode() {
        return errCode;
    }

    @Override
    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    @Override
    public String getErrInfo() {
        return errInfo;
    }

    @Override
    public void setErrInfo(String errInfo) {
        this.errInfo = errInfo;
    }

    public String getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(String transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    public String getRetrievalRefNum() {
        return retrievalRefNum;
    }

    public void setRetrievalRefNum(String retrievalRefNum) {
        this.retrievalRefNum = retrievalRefNum;
    }
}
