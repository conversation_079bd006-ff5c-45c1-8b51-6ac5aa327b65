package com.zzz.util;

import com.zzz.entity.ReqContextUser;
import lombok.extern.slf4j.Slf4j;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 15:43 2023/3/24
 * @ Description：获取登录用户的信息
 * @ Version:     1.0
 */
@Slf4j
public class UserContextUtil {

    /**
     * 登录的用户信息存入当前线程
     */
    private static final ThreadLocal<ReqContextUser> user = new ThreadLocal();

    /**
     * 把登录的用户信息储存至当前线程
     *
     * @param userInfo
     */
    public static void set(ReqContextUser userInfo) {
        user.set(userInfo);
    }

    /**
     * 获取当前登录的用户信息
     *
     * @return UserInfo对象
     */
    public static ReqContextUser get() {
        return user.get();
    }

    /**
     * 把登录的用户信息移除
     */
    public static void remove() {
        user.remove();
    }
}
