package com.zzz.component.max;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>
 *  max 系统接口地址
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-13
 **/
@Component
@Data
public class ApiUrlConstMax {

    @Value("${api.maxBaseUrl}")
    private String maxBaseUrl;

    public String getMaxBaseUrl() {
        return maxBaseUrl;
    }

    // 网点列表
    public String getSiteListUrl() {
        return maxBaseUrl + "/api/v1/openapi/query_site_page";
    }

    // 同步币种基础数据
    public String getCurrencyListUrl() {
        return maxBaseUrl + "/api/v1/openapi/query_currnecy";
    }

    // 零钱包数据
    public String getCoinListUrl() {
        return maxBaseUrl + "/api/v1/openapi/query_coin";
    }

    // 单个零钱包查询 库存
    public String getCoinDetailUrl() {
        return maxBaseUrl + "/api/v1/openapi/get_coin_by_id";
    }

    // 创建订单
    public String getCreateOrderUrl() {
        return maxBaseUrl + "/api/v1/openapi/create";
    }

    // 撤销订单
    public String getCannelOrderUrl() {
        return maxBaseUrl + "/api/v1/openapi/cancel";
    }

    // 提取订单
    public String getExtractOrderUrl() {
        return maxBaseUrl + "/api/v1/openapi/extract";
    }

    // 查询订单详情
    public String getQueryOrderUrl() {
        return maxBaseUrl + "/api/v1/openapi/get_order_by_orderno";
    }

    // 查询订单详情
    public String getQueryCoinDetailUrl() {
        return maxBaseUrl + "/api/v1/openapi/get_coin_by_id";
    }

    // 网点零钱包最大可购买数量
    public String getSiteCoinBuyNumUrl() {
        return maxBaseUrl + "/api/v1/openapi/get_coin_by_coincode";
    }

    // 锁定库存
    public String getLockCoinStockUrl() {
        return maxBaseUrl + "/api/v1/openapi/coin/lock";
    }

    // 解锁库存
    public String getUnlockCoinStockUrl() {
        return maxBaseUrl + "/api/v1/openapi/coin/unlock";
    }

    // 用户交易信息校验
    public String getCheckUserUrl() {
        return maxBaseUrl + "/api/v1/openapi/check_user";
    }

    // 用户币种可兑换最大数量
    public String getMaxCurnum() {
        return maxBaseUrl + "/api/v1/openapi/get_max_curnum";
    }

    /**
     * 查询个人最大可购买限额
     * 
     * @return 个人最大可购买限额
     */
    public String queryPersonalMaxPurchaseLimit() {
        return maxBaseUrl + "/api/v1/openapi/query_personal_max_purchase_limit";
    }
    //主域名
//    private static final String URL ="http://140.179.189.106:8102";
//    private static final String URL ="http://127.0.0.1:8102";
//    private static final String URL ="http://172.22.157.147:8102";
//     private static final String URL ="https://10.0.0.121:18102";

    // 网点列表
    // public static final String SITE_LIST = URL + "/api/v1/openapi/query_site_page";
    //
    // //同步币种基础数据
    // public static final String CURRENCY_LIST = URL + "/api/v1/openapi/query_currnecy";
    //
    // //零钱包数据
    // public static final String COIN_LIST = URL + "/api/v1/openapi/query_coin";
    //
    // //单个零钱包查询 库存
    // public static final String COIN_DETAIL = URL + "/api/v1/openapi/get_coin_by_id";
    //
    // //创建订单
    // public static final String CREATE_ORDER = URL + "/api/v1/openapi/create";
    //
    // //撤销订单
    // public static final String CANNEL_ORDER = URL + "/api/v1/openapi/cancel";
    //
    // //提取订单
    // public static final String EXTRACT_ORDER = URL + "/api/v1/openapi/extract";
    //
    // //查询订单详情
    // public static final String QUERY_ORDER = URL + "/api/v1/openapi/get_order_by_orderno";
    //
    // //查询订单详情
    // public static final String QUERY_COIN_DETAIL = URL + "/api/v1/openapi/get_coin_by_id";
    //
    // //锁定库存
    // public static final String LOCK_COIN_STOCK = URL + "/api/v1/openapi/coin/lock";
    //
    // //解锁库存
    // public static final String UNLOCK_COIN_STOCK = URL + "/api/v1/openapi/coin/unlock";

    //
//    public static final String CHECK_USER = URL + "/api/v1/openapi/check_user";

}
