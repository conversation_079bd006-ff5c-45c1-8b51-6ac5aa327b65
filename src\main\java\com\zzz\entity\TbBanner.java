package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 小程序轮播表
    * </p>
*
* <AUTHOR>
* @since 2024-08-29
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_banner")
    @ApiModel(value="TbBanner对象", description="小程序轮播表")
    public class TbBanner implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = " 主键、自增 ")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = " 标题 ")
    private String title;

            @ApiModelProperty(value = " 跳转地址（小程序地址）")
    private String jumpUrl;

            @ApiModelProperty(value = " 图片url ")
    private String imageUrl;

            @ApiModelProperty(value = " 排序，越小的越在前 ")
    private Integer sort;

            @ApiModelProperty(value = "状态；1启用、0停用")
    private Integer status;

            @ApiModelProperty(value = " 备注 ")
    private String remark;

            @ApiModelProperty(value = "删除状态。1删除，0正常 ")
    private Integer isDel;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;


}
