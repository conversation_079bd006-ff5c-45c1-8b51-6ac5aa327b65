package com.zzz.service;

import com.zzz.component.bamboo.response.DistributionOrgInfoResponse;
import com.zzz.dto.BranchOrgDto;
import com.zzz.entity.BbDistributionOrg;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 客户表（Bamboo） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IBbDistributionOrgService extends IService<BbDistributionOrg> {

    Integer addDistribution(DistributionOrgInfoResponse listRespons);

    List<BbDistributionOrg> getDistributionOrgList();

    BbDistributionOrg getDistributionByPid(String outId);

    BranchOrgDto getDistributionByBId(Integer branchId);
}
