package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 优惠券领取表
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_coupon_receive")
    @ApiModel(value="TbCouponReceive对象", description="优惠券领取表")
    public class TbCouponReceive implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "用户id")
    private String userId;

            @ApiModelProperty(value = "优惠券id")
    private Integer couponId;

        @ApiModelProperty(value = "使用时间")
    private LocalDateTime useTime;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

            @ApiModelProperty(value = "1使用2未使用")
    private Integer applyState;


}
