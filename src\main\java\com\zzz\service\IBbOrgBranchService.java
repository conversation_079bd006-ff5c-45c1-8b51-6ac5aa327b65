package com.zzz.service;

import com.zzz.component.bamboo.response.OrgBranchInfo;
import com.zzz.entity.BbOrgBranch;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 客户网点表（Bamboo） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IBbOrgBranchService extends IService<BbOrgBranch> {

    void addOrgBranch(BbOrgBranch orgBranchInfo,Integer status);

    List<BbOrgBranch> getList();

    List<BbOrgBranch> getListByPid(Integer id);
}
