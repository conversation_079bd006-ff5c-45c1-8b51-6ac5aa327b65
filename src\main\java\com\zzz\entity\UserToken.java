package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 用户token表
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("user_token")
    @ApiModel(value="UserToken对象", description="用户token表")
    public class UserToken implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

            @ApiModelProperty(value = "用户id")
    private Integer userId;

            @ApiModelProperty(value = "用户令牌")
    private String token;

            @ApiModelProperty(value = "过期时间")
    private LocalDateTime expireTime;

            @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;


}
