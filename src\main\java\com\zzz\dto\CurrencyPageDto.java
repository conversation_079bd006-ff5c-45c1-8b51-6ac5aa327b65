package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Data
@Builder
@ApiModel("币种信息分页实体")
public class CurrencyPageDto {

    private String updateTime;

    private Long totalPage;

    private Long totalRecord;

    private List<CurrencyDto> list;

}
