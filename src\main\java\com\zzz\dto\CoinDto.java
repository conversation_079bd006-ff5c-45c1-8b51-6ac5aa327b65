package com.zzz.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since  1.0
 * @since   2024-07-17
 **/
@Data
public class CoinDto {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 零钱包名称
     */
    @ApiModelProperty("零钱包名称")
    private String coinname;

    /**
     * 零钱包编码
     */
    @ApiModelProperty("零钱包编码")
    private String coincode;

    /**
     * 单价；388元/份
     */
    @ApiModelProperty("单价；388元/份")
    private BigDecimal price;

    @ApiModelProperty("总面值")
    private BigDecimal parvalue;

    /**
     * 库存
     */
    @ApiModelProperty("库存")
    private Integer quantity;

    /**
     * 单次最大购买数量
     */
    @ApiModelProperty("单次最大购买数量")
    private Integer maxQuantity;

    /**
     * 锁定库存
     */
    @ApiModelProperty("锁定库存")
    private Integer lockQuantity;

    /**
     * // * 累计，已用库存
     */
    @ApiModelProperty("累计，已用库存")
    private Integer useQuantity;

    /**
     * 备注
     */
    @ApiModelProperty("REMARKS")
    private String remarks;

    /**
     * 详情介绍
     */
    @ApiModelProperty("详情介绍")
    private String detail;

    /**
     * 所属网点
     */
    @ApiModelProperty("所属网点")
    private String sitecode;

    /**
     * 钱包类型：1零钱包2整钱包
     */
    @ApiModelProperty("钱包类型：1零钱包2整钱包")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime createdate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime updatedate;

    /**
     * 状态1正常0关闭
     */
    @ApiModelProperty("状态1正常0关闭")
    private String status;

    /**
     * 币种编号
     */
    @ApiModelProperty("币种编号")
    private String curId;

    /**
     * 币种编号
     */
    @ApiModelProperty("币种编号")
    private String currency;

    /**
     * 币种汇率
     */
    @ApiModelProperty("币种汇率")
    private BigDecimal rate;

    /**
     * 子钱包列表
     */
    @ApiModelProperty("币种编号")
    private List<CoinChildDto> list;
}
