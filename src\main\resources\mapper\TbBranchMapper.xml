<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.TbBranchMapper">

    <!-- 分页列表查询 -->
    <select id="listPage" resultType="com.zzz.dto.BranchDto">
        SELECT tbb.id,
            tbb.sitecode,
            tbb.sitename,
            tbb.corpcode,
            tbb.siteaddr,
            tbb.sitemanager,
            tbb.sitetel,
            tbb.online_flag,
            tbb.ws_orgcode,
            tbb.ws_flag,
            tbb.longitude,
            tbb.latitude,
            tbb.start_time,
            tbb.end_time,
            (tc.market_price * tcb.rate_unit) currate,
            round(ST_Distance_Sphere(
                    point(tbb.longitude, tbb.latitude),
                    point(#{vo.longitude}, #{vo.latitude})
                ) / 1000, 3
            ) distance
        FROM tb_branch tbb
        LEFT JOIN tb_tags_taggings tag ON tbb.id = tag.site_id
        LEFT JOIN tbcurrency tc ON tbb.id = tc.branch_id
        LEFT JOIN tb_currency_base tcb ON tc.currency = tcb.cur_code
        WHERE tbb.is_del = 0
	        AND tbb.online_flag = 1
            AND tc.`status` = 1
            AND tc.is_del = 0
            <if test="vo.tagId != null and vo.tagId != ''">
                AND tag.tag_id = #{vo.tagId}
            </if>
            <if test="vo.currency != null and vo.currency != ''">
                AND tc.currency = #{vo.currency}
            </if>
        GROUP BY tbb.id
        ORDER BY distance
    </select>
</mapper>
