package com.zzz.controller.v1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zzz.dto.CoinDto;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbCoinPurseService;
import com.zzz.vo.CoinPurseVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-23
 **/
@Api(tags = "零钱包管理")
@Slf4j
@RestController
@RequestMapping("/api/v1/coin")
public class CoinController {

    @Autowired
    private ITbCoinPurseService iTbCoinPurseService;

    /**
     * 根据网点ID和币种ID 获取零钱包 tb_coin_purse
     */
    @ApiOperation("获取零钱包(网点ID和币种ID)")
    @PostMapping("/get_coin_purse")
    public JSONResult<CoinDto> getCoinPurseBySiteId(@RequestBody CoinPurseVo coinPurseVo){
        return JSONResult.ok(iTbCoinPurseService.getCoinPurseBySiteId(coinPurseVo));
    }
}
