package com.zzz.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PersonalMaxPurchaseLimitDto {
    /**
     * 超额类型
     */
    @ApiModelProperty("超额类型")
    private Integer exceedType;

    /**
     * 是否待核查
     */
    @ApiModelProperty("是否待核查")
    private String isCheck;

    /**
     * '个人主体分类状态代码；01正常、02预关注、03关注名单',
     */
    @ApiModelProperty("个人主体分类状态代码")
    private String typeStatus;

    @ApiModelProperty("本年度购汇额度")
    private Double yearBalanceIn;

    @ApiModelProperty("当日购汇额度")
    private Double dayBalancIn;


    @ApiModelProperty("本年度结汇额度")
    private Double yearBalanceOut;

    @ApiModelProperty("当日结汇额度")
    private Double dayBalanceOut;

    @ApiModelProperty("币种库存")
    private BigDecimal curBalance;

    @ApiModelProperty("单日限额")
    private BigDecimal dayLimit;

    @ApiModelProperty("最大可购买金额")
    private BigDecimal maxPurchasableAmount;
}
