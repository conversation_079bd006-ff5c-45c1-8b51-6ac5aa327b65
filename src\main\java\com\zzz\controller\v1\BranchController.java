package com.zzz.controller.v1;

import com.zzz.dto.*;
import com.zzz.entity.TbBranch;
import com.zzz.entity.Tbfees;
import com.zzz.response.JSONResult;
import com.zzz.service.*;
import com.zzz.util.SpringBeanUtil;
import com.zzz.vo.BranchPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Api(tags = "网点管理")
@Slf4j
@RestController
@RequestMapping("/api/v1/branch")
public class BranchController {

    @Autowired
    private ITbTagsService tbTagsService;

    @Autowired
    private ITbBranchService iTbBranchService;

    @Autowired
    private ITbfeesService iTbfeesService;

    @Autowired
    private ITbBranchUrgentService iTbBranchUrgentService;

    /**
     * 网点列表（分页）
     */
    @ApiOperation("网点列表（分页）")
    @PostMapping("/get_branch_page")
    public JSONResult<BranchPageDto> getBranchPage(@RequestBody @Valid BranchPageVo branchPageVo){
        return JSONResult.ok(iTbBranchService.getBranchPage(branchPageVo));
    }

    /**
     *  网点标签列表
     */
    @ApiOperation("网点标签列表")
    @GetMapping("/get_tags_list")
    public JSONResult<List<TagsDto>> getTagsList(){
        return JSONResult.ok(tbTagsService.getTagsList());
    }

    /**
     * 根据网点ID 获取网点信息
     */
    @ApiOperation("获取网点详情（网点ID ）")
    @GetMapping("/get_branch")
    public JSONResult<BranchDto> getBranchById(@RequestParam("siteId") Integer siteId){
        TbBranch tbBranch = iTbBranchService.getBranchById(siteId);
        return JSONResult.ok(SpringBeanUtil.copyProperties(tbBranch,BranchDto.class));
    }


    /**
     * 日期加急费
     */
    @ApiOperation(value = "获取网点加急排期列表")
    @PostMapping("/get_urgent_list_by_branchid")
    public JSONResult<List<BranchUrgentDto>> urgentFee(@RequestParam("id") Integer id) {
        return iTbBranchService.getUrgentListByBranchid(id);
    }

    /**
     * 根据网点和日期 获取加急费用
     */
    @ApiOperation(value = "获取加急费用(网点ID和日期)")
    @GetMapping("/get_urgentfee_detail")
    public JSONResult<BranchUrgentDto> getUrgentFeeDetail(
            @RequestParam("id") Integer id,
            @RequestParam("date") String date
    ){
        return iTbBranchUrgentService.getUrgentFeeDetail(id,date);
    }


    /**
     * 根据网点ID 获取手续费信息
     */
    @ApiOperation("获取手续费信息(网点ID)")
    @GetMapping("/get_branch_fees")
    public JSONResult<TbfeesDto> getBranchFreesById(@RequestParam("branchId") Integer branchId){
        Tbfees tbfees = iTbfeesService.getFeesByBranchId(branchId);
        return JSONResult.ok(SpringBeanUtil.copyProperties(tbfees,TbfeesDto.class));
    }


}
