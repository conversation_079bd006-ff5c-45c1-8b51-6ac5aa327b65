package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("用户实名认证信息实体")
public class UserInformationDto {


    @ApiModelProperty(value = "实名认证唯一id，下单需要")
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证")
    private String card;

    @ApiModelProperty(value = "手机号")
    private String phone;

}
