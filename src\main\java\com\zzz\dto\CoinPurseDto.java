package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("网点零钱包信息实体")
public class CoinPurseDto {

    @ApiModelProperty(value = "唯一ID")
    private Integer id;

    @ApiModelProperty(value = "单价；388元/份")
    private BigDecimal price;

    @ApiModelProperty(value = "库存")
    private Integer quantity;

    @ApiModelProperty(value = "温馨提示")
    private String remarks;

    @ApiModelProperty(value = "详情")
    private String detail;

    @ApiModelProperty(value = "网点id")
    private Integer branchId;

    @ApiModelProperty(value = "1零钱包2整钱包")
    private Integer type;

    @ApiModelProperty(value = "币种id")
    private Integer exchangerateId;

    //零钱包包含面额列表
    @ApiModelProperty(value = "零钱包包含面额列表")
    private List<CoinChiedDto> list;

}
