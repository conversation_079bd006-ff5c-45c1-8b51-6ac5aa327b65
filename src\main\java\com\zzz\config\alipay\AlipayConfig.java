package com.zzz.config.alipay;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;

/**
 * 支付宝支付配置
 **/

public class AlipayConfig {
    /**
     * 支付宝网关地址
     */
    public static final  String PAY_GATEWAY="https://openapi.alipay.com/gateway.do";
//    public static final  String PAY_GATEWAY="https://openapi-sandbox.dl.alipaydev.com/gateway.do";

    /**
     * 支付宝 APPID
     */
    public static final  String APPID="2021004153668730";

    /**
     * 应用私钥
     */
    public static final String APP_PRI_KEY = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCbS1dfHknYPJafS7wALInz8hzwiri6fWREiq+/zzoWryXCeAlupOtdKb84rXkbSwGyB5ZSYX6iCXTimSt5ENW4UOFJLcXEpW6ErPjp1kp/x2p7sGNSxucBRxu1U7lPhZxe98mqlvzk3ywPF5m2MNXTtJQ/eLdWYFGp+jwfgvwxJqLKHI71tq0cxUqiZl1aRKd53tkxOrl8dWN7C5U1rT08nPaxRL9Eca7wFrKorO1fbrpbTGuCE4pPhRYyUXidSAbx/ut6XrozFhzAX9Ubb0Ote3juDP0qhFAnK3aKFI33R3qphgDNlsqpCGpea22R/EK25xCfrosiBqCqa5ejSPnJAgMBAAECggEAD7viblCzSqVY3Wre8WWR1JWDORvU+5AYyJkElXp5eDLmsH1Y90zeS+Ie7a8lxg4jbdY29MHMAr7SiOLb9Lgv7yzCppdsoi/gsrCTx51ejvO1ZjLI3WopnYeG1yhUbiorXbPdStthZA74d878U+k+wORa4NNqP+YiYAZqcQbkZlZLW96pxImU0NQoGOBMrn0GzhU55WcWc2SlXcKpX2N6s0hC1okrDkOM5n0gpdard9u+qzaOFEBHpteSqYdrRG5oadnf9PPnY/oDvqN1fDrAUduqoHFGBGePI+0Iyud5RGbFQ74iqxDcU3k/t1N0tZBahPmPlpX7aDOb+IxPovVDAQKBgQDNqaPI2iKBRphRjplt7SuwWjgx+eAnAo8gCs8iu9YJ9r09rjJtK7XcDgVwYo4UY0jZoRZTqc7Q3BZD1QETIJ4S9UiD/rkCS8mbL7GiPqX7ZiobQMVcCcN34/NixA38IkiGU2/4EFetyjCJvxETBy0/ZodQ9Ljbd74JiOrx5zvZ5QKBgQDBTbvLiUCIlQ8E6Yehv2IrIQmpRh7lWWUAG2pv7e7egFy1+VsMuI5crKFUqmNE/lZb1SbuGRpRiudcgBY52XAW1Ick6BExvUw1W1olTan0g+zzucZ0fpxQG+MTd6xYn1O1oJiQGGgobIIChyw90Y2fLQ4lcf4+2TQSpwsghPYSFQKBgCGf+bpOB9QxEZhMA0PYwgrcRVHOc9qM7Y+klMcuBMmuQLSD9ZSpz9sBo9wDEQ2+SdvjHX5iRgEZ6r3+ZmWVMKMS3V9PVzpL39Nykc1gBs06n+nCxkHNCttgNpvQSCUQCeGEZMsfCObOJu9ugMEmrDwwbDFNQagIRNbUPu18C+oNAoGALL0Q+cqKZRIap8hk28qvrcu3sLXJ8Mr/1UaDG+S1pEmRK4zKxebxDAHoVPAaiZ1T32011WV58eV0JrR+cGUizCXkIWaUlC57tN2vv3J6I11lOgOFKvEivGe35omMGfbGKQNCTBdaBAEZFfyzAJu/a1fCLmhzr5gVtTt0F9KuIQECgYASJ1v7JYPeT3jrEvTzckRA1UY8in9S5j3vCnRN+EHuENwXim5yq01mPnD5rRyqHgG5OWHM4asXauRsA85PaAuj1tdgpxjh+nLW8VTlgz7UPlSV54PYexjlt8p7+Qrcw8Onn0R1E6ncSxM4GCKOiN2fA/h83kXUKt3+TCYL2Pm2pw==";

    /**
     * 支付宝公钥
     */
    public static final String ALIPAY_PUB_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlv0BAabZUW7yenb3g6ggc8kcDwf4euU5ZQQ3iCsA6/e20se5zwHO+p5A+cMAtT9MXPCbxm+8lHSsuMdGE7ZgM0RoRM+OxZ+rlPSvf01SK7E5E3vsFxGIopoyYCK/bFBME19kTz1lAdoGxxco4kNSAKI0sNQdpsZ+m9lODeUDDq+mTH2HT+NE+TmGXowPIdDf63/Y8+jsMCQT2+WgxOPlcFNfozSLCOcrxbXzoUUvFl/H/mG9mOr4GfOxP6RpvYMXDv5rmODWofjygAOR86U702z6r8b+YBxoerW+pPBCL3d5Mbyvo0/pWtFQKNv+9CgmMXg4j4J4XWZuZt7EF8zlfwIDAQAB";

    /**
     * 内容加密密钥
     */
    public static final String DECRYPT_KEY = "2KxcnEk6gQeUBKxPugOnaw==";

    /**
     * 签名类型
     */
    public static final  String SIGN_TYPE="RSA2";


    /**
     * 字符编码
     */
    public static final  String CHARSET="UTF-8";


    /**
     * 返回参数格式
     */
    public static final  String FORMAT="json";


    /**
     * 构造函数私有化
     */
    private AlipayConfig(){

    }


    private volatile static AlipayClient instance = null;


    /**
     * 单例模式获取, 双重锁校验
     * @return
     */
    public static AlipayClient getInstance(){
        if(instance==null){
            synchronized (AlipayConfig.class){
                if(instance == null){
                    instance = new DefaultAlipayClient(PAY_GATEWAY,APPID,APP_PRI_KEY,FORMAT,CHARSET,ALIPAY_PUB_KEY,SIGN_TYPE);
                }
            }
        }
        return instance;
    }
}
