package com.zzz.emuns;

/**
 * <p>
 * 数据删除状态（统一）
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-25
 **/
public enum DeleteStatusEnum {


    /**
     * 删除
     */
    DELETE_SUCCESS(1),

    /**
     * 正常
     */
    NORMAL(0),


    ;
    /**
     * 订单状态
     */
    public final int value;

    DeleteStatusEnum(int value) {
        this.value = value;
    }

    public static Object getByValue(Integer status) {
        for(DeleteStatusEnum DeleteStatusEnum:DeleteStatusEnum.values()){
            if(DeleteStatusEnum.value==status){
                return DeleteStatusEnum;
            }
        }
        return null;
    }
}
