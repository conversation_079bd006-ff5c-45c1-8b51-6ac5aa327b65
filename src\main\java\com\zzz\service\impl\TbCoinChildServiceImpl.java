package com.zzz.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zzz.entity.TbCoinChild;
import com.zzz.mapper.TbCoinChildMapper;
import com.zzz.service.ITbCoinChildService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-23
 */
@Service
public class TbCoinChildServiceImpl extends ServiceImpl<TbCoinChildMapper, TbCoinChild> implements ITbCoinChildService {

    @Autowired
    private TbCoinChildMapper tbCoinChildMapper;

    @Override
    public List<TbCoinChild> selectCoinChindByCoinId(Integer id) {
        return tbCoinChildMapper.selectList(
                new LambdaQueryWrapper<TbCoinChild>()
                        .eq(TbCoinChild::getCoinId,id)
        );
    }

    @Override
    public double selectSumAmtById(Integer walletId) {
        List<TbCoinChild> list = tbCoinChildMapper.selectList(
                new LambdaQueryWrapper<TbCoinChild>()
                        .eq(TbCoinChild::getCoinId, walletId)
        );
        if(list.isEmpty()){
            return 0;
        }
        List<Double> resList = list.stream().map(item -> NumberUtil.mul( item.getAclValue() ,item.getNum()).doubleValue()).collect(Collectors.toList());
        return resList.stream().mapToDouble(Double::doubleValue).sum();
    }
}
