package com.zzz.component.chinaums.open.api;

/**
 * Created by ZHANGWEI on 2016/12/2.
 */
public abstract interface OpenApiRequest<T extends OpenApiResponse> {
    public abstract Class<T> responseClass();
    /*
    api版本号
     */
    public abstract String apiVersion();
    /*
    api接口名称定义
     */
    public abstract String apiMethodName();
    /*
    开放平台分配servicecode
     */
    public abstract String serviceCode();

    /*
    是否需要获取token
     */
    public abstract boolean needToken();
}
