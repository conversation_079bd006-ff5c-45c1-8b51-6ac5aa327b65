package com.zzz.component.max.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户币种可兑换最大数量 max 请求参数
 * <AUTHOR> to 2024/11/9
 */
@Data
public class MaxCurnumRequest {
    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("证件号码")
    private String docno;

    @ApiModelProperty("客户姓名")
    private String personName;
}
