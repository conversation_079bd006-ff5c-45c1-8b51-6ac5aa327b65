package com.zzz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.UserInformationDto;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.UserInformation;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.UserInformationMapper;
import com.zzz.service.IUserInformationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户实名信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class UserInformationServiceImpl extends ServiceImpl<UserInformationMapper, UserInformation> implements IUserInformationService {

    @Value("${server.rsa.publicKeyStr}")
    private String publicKeyStr;
    @Value("${server.rsa.privateKeyStr}")
    private String privateKeyStr;

    @Autowired
    private UserInformationMapper userInformationMapper;

    @Override
    public UserInformation getRealName(String userName, String userCard) {
        return userInformationMapper.selectOne(
                new LambdaQueryWrapper<UserInformation>()
                        .eq(UserInformation::getName,userName)
                        .eq(UserInformation::getCard,userCard)
        );
    }

    @Override
    public UserInformationDto getRealNameInfo() {
        //当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        UserInformation userInformation = userInformationMapper.selectOne(new LambdaQueryWrapper<UserInformation>().eq(UserInformation::getUserId, reqContextUser.getId()));
        if(null == userInformation){
            throw new ServiceException(CommonErrorEnum.REALNAME_IS_NULL);
        }

        //解密操作
        UserInformationDto userInformationDto = new UserInformationDto();
        try{
            RSA rsa = new RSA(privateKeyStr, publicKeyStr);
            // 加密传输
           String name = rsa.encryptBase64(userInformation.getName(), KeyType.PublicKey);
           String idCard = rsa.encryptBase64(userInformation.getCard(), KeyType.PublicKey);
            String phone = rsa.encryptBase64(userInformation.getPhone(), KeyType.PublicKey);

            userInformationDto.setName(name);
            userInformationDto.setCard(idCard);
            userInformationDto.setPhone(phone);
       }catch (Exception e){
           throw new ServiceException(CommonErrorEnum.SYSTEM_ERROR);
       }
        userInformationDto.setId(userInformation.getId());
        userInformationDto.setUserId(userInformation.getUserId());
        return userInformationDto;
    }

    @Override
    public Integer addRealNameInfo(String realname, String idcard,String phone) {
        //当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        //新增实名认证信息
        UserInformation userInformation = new UserInformation();
        userInformation.setUserId(reqContextUser.getId());
        userInformation.setAddTime(LocalDateTime.now());
        userInformation.setCard(idcard);
        userInformation.setName(realname);
        userInformation.setPhone(phone);
        return userInformationMapper.insert(userInformation);
    }

    @Override
    public UserInformation getRealNameByUid(Integer userId) {
        return userInformationMapper.selectOne(new LambdaQueryWrapper<UserInformation>().eq(UserInformation::getUserId,userId));
    }

}
