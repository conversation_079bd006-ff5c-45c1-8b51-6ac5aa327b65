spring:
  flyway:
    enabled: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://**************:3306/plusdb?characterEncoding=utf8&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai
    username: plusdb
    password: hHBppinSKYWGEDjP
  redis:
    #数据库索引
    database: 12
    host: localhost
    port: 6379
    password: 
    #连接超时时间
    timeout: 5000
    lettuce:
      pool:
        max-idle: 20000
        min-idle: 2
        max-active: 36000
        max-wait: 5000
  ## rabbitmq配置 - start
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: admin
    password: 09EFbnOTPsGLilUChcXW
    virtual-host: dev
  #    listener:
  #      simple:
  #        #ack策略
  #        acknowledge-mode: none
  #        #消息确认策略
  #    publisher-confirm-type: correlated
mybatis-plus:
  # mapper.xml扫描
  mapper-locations: classpath*:mapper/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  configuration:
    # 开启自动驼峰命名规则
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    # 打印SQL语句,此处需注释掉，否则影响到logback日志正常打印输出
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#saToken相关
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # jwt加密key
  jwt-secret-key: saToken2024zzz
#knife4j文档相关
knife4j:
  enable: true
  openapi:
    title: Knife4j官方文档
    description: "`我是测试`,**你知道吗**
    # aaa"
    email: <EMAIL>
    concat: 八一菜刀
    url: https://docs.xiaominfo.com
    version: v4.0
    group:
      test1:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.zzz.controller
###################server模块相关
server:
  service:
    on-off: true
  #rsa加密相关
  rsa:
    #私钥
    #    privateKeyStr: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKvJbT6YT8CMTtKXBayqM4cmSSBPLTxzguRJBY+IC71PlrVKMzPwymccj5QE4Q76cPclCszuvYnR58oYw3bE2eeW1qqKetLI2ipBoXCFeOK33zZL5VZSkOQxuDzqSHs/9BS+kfMNGrN4hfdBjVC+C0l/GLDxE4Q9I1RsbhqVa5/hAgMBAAECgYA/+eEEecbhx58nUHUdi7lq8Lg1HyeSptYtXICEpr7wfu56j6qoAlnusrVKA9MpN93QPOxby+GsrQ6stjI9Tix0NfxpR00O3fyv9Q9xiGJwn2dA23z2ak+SvKLfUAKkoTrGtfz8keyJezC0VNErd0Q9GMpeUDsly2Jd+DtKplY90QJBANaC0Wz60WBPNr+MpTab6qCNMfFNAGeFsiRkoejiUOek3Mr09KezjhJkdMTFrKsVfCxCPrpcqchkt6uKleOJnEUCQQDNAy++xa5IwYO18bFEJNaXbos2BcQnCi13XKW56x1Zxgv2zhX/MEEE0FzpPT0BsC8hm+ApVhKuTi3hcXblP2TtAkEArJQ21Uy9YpSYYWdQDC1IiL7P7Wib6K2hcrIbqPdbS+JRWF/W7Y0Bwj5by2uVzVbNvfXV7a1bU0PkmPg4OwWdWQJAPQZZy/uj6VxRDHOpRJOCnQj2v1nwDX7mEcWuPy1RS0u8e/bUbWW7tOchAPyNkFcVcHepviQ041W6KoVP0uWP8QJBAI1DW+Z1toBBQvfBWzY+ZAB7g4YxMpoNs9+mBUQJ78wmgVpimd0PZI6++J6nwyzvbhC2WwuiH6Xv9ekzVUH1IEE=
    privateKeyStr: MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAL3eb0Ski8WITuf6nP8WI6ps/3UjmLfy05bKPAWFnpkU57tqqq4OONozl5+N7co4U31uQUWZTm5nLR7Dp1FL4odX8+y4yMMEIxFqT9a5RvK6D65naxoEVTybbsdmU0XCLDz/tvjCXrsjbZiKCtU73cWhg4PIWYU1n0Y5wNbrVvP1AgMBAAECgYEAiMI/+PxgZL6Cv1rnXGEw/V+XPWOPa+eu+izewMd27iJ9q6PwHoDF+H0y2E0dmwupL26L+zqqoKEWgdNBoC/9kGl5IrQyccWyrBaFrAhC5oZC1K5lT40H5ZQZvD1EELoZ4O/5y8hbvdQ1C60AxMNfq9ZV/1iLm24wblvpoSPY8KECQQD2cxA8W+8+PH2Im+7+ot0fb8lr7yooENWaf+pRdZbsQsiKtgHNQqjbcQY+h5Fb1kJNFRhE0we6Hq7qZVb3HwEdAkEAxToOPHraB9U/FYtC1T9P/+kxsnNXb3XEGlroQjUQ93eVsOB4bzbtBNwHGqGm/66T2qfBWpuf1BAr6HE5hVjeuQJBALzAr8Sq3PebuMGyPD+GWTlEks455N2+1vp30e0F1Muq8pFaiNK+pTk8vX2Ssz/VP/5tk6LSxe5Epc8cQpkOg/kCQG9hePa43na7/DNbgTPS3IgGltVThBZjKItFuZLiSyGmPDg2nG0aVMKAy9ZDYn9UhRVf1bco2hs1wsYzYyumY6kCQQDOLWfMaQD696LGBLkyYKFL3ICqubdWBYRmC9gZZKLDdFa3SSDB+edFVIEKTzBBS9j/kzh1BM1cF/X2ODsrEybd
    #公钥
    #    publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCryW0+mE/AjE7SlwWsqjOHJkkgTy08c4LkSQWPiAu9T5a1SjMz8MpnHI+UBOEO+nD3JQrM7r2J0efKGMN2xNnnltaqinrSyNoqQaFwhXjit982S+VWUpDkMbg86kh7P/QUvpHzDRqzeIX3QY1QvgtJfxiw8ROEPSNUbG4alWuf4QIDAQAB
    publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC93m9EpIvFiE7n+pz/FiOqbP91I5i38tOWyjwFhZ6ZFOe7aqquDjjaM5efje3KOFN9bkFFmU5uZy0ew6dRS+KHV/PsuMjDBCMRak/WuUbyug+uZ2saBFU8m27HZlNFwiw8/7b4wl67I22YigrVO93FoYODyFmFNZ9GOcDW61bz9QIDAQAB
# S3配置
aws:
  region: 北京
  accessKey: ********************
  secretKey: WwkHzppJZoqejNmzjXVD9m7P5L6FMOt3NCWmS9hw
  bucketName: hongshanshu-test
#自定义消息队列配置，发送锁定库存消息-》延迟exchange-》lock.queue-》死信exchange-》release.queue
mqconfig:
  #延迟队列，不能被监听消费
  order_close_delay_queue: order.close.delay.queue
  #延迟队列的消息过期后转发的队列
  order_close_queue: order.close.queue
  #交换机
  order_event_exchange: order.event.exchange
  #进入延迟队列的路由key
  order_close_delay_routing_key: order.close.delay.routing.key
  #消息过期，进入释放队列的key,进入死信队列的key
  order_close_routing_key: order.close.routing.key
  #消息过期时间,毫秒,测试改为15分钟
  ttl: 900000
# 第三方系统请求地址
api:
  # max系统请求地址
  maxBaseUrl: https://10.0.0.121:18102
  # bamboo 系统请求地址
  bambooBaseUrl: http://bamboo-test.travelexcn.com
# UMS支付配置
ums:
  pay:
    # 应用ID
    appId: 8a81c1be8fc4b725019039e118e202ee
    # 应用密钥
    appKey: f3b0cfaa4e194a368fe2ec79696805c9
    # 商户号
    mid: 89831016012Z583
    # 终端号
    tid: Z5830001
    # 子商户号
    subMid: *********
    # 来源编号(4位)
    sourceCode: 39GG
    # 支付结果通知地址
    notifyUrl: https://plus-test.travelexcn.com/api/v1/pay/notify
    # 网页跳转地址
    returnUrl: https://plus-test.travelexcn.com/api/v1/wechat/pay/return
    # 微信支付接口URL
    wxPayUrl: https://api-mop.chinaums.com/v1/netpay/wx/unified-order
    # 支付宝支付接口URL
    aliPayUrl: https://api-mop.chinaums.com/v1/netpay/trade/create
    # 银联支付接口URL
    unionPayUrl: https://api-mop.chinaums.com/v1/netpay/uac/mini-order
    # 退款接口URL
    refundUrl: https://api-mop.chinaums.com/v1/netpay/refund
    # 订单关闭接口URL
    closeUrl: https://api-mop.chinaums.com/v1/netpay/close
    # 订单查询接口URL
    queryUrl: https://api-mop.chinaums.com/v1/netpay/query