package com.zzz.service;

import com.zzz.dto.BranchPageDto;
import com.zzz.dto.CoinDto;
import com.zzz.dto.CoinPurseDto;
import com.zzz.entity.TbCoinPurse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;
import com.zzz.vo.CoinPurseVo;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * <p>
 * 零钱宝整钱包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbCoinPurseService extends IService<TbCoinPurse> {

    CoinDto getCoinPurseBySiteId(CoinPurseVo req);

    TbCoinPurse getCoinByBizId(Integer walletId);
}
