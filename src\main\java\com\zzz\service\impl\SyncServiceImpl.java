package com.zzz.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.zzz.component.bamboo.BamBooAPI;
import com.zzz.component.bamboo.request.QueryCustomerPageRequest;
import com.zzz.component.bamboo.request.QueryProductPageRequest;
import com.zzz.component.bamboo.response.DistributionOrgInfoResponse;
import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.MaxAPI;
import com.zzz.entity.*;
import com.zzz.mapper.*;
import com.zzz.response.JSONResult;
import com.zzz.service.*;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-12
 **/
@Service
public class SyncServiceImpl implements SyncService {

    @Autowired
    private IBbDistributionOrgService bbDistributionOrgService;

    @Autowired
    private IBbOrgBranchService iBbOrgBranchService;

    @Autowired
    private IBbProductService bbProductService;

    @Autowired
    private ITbcurrencyService tbcurrencyService;

    @Autowired
    private TbcurrencyMapper tbcurrencyMapper;


    @Autowired
    private ITbBranchService iTbBranchService;

    @Autowired
    private ITbBranchService tbBranchService;

    @Autowired
    private ITbCoinPurseService tbCoinPurseService;

    @Autowired
    private ITbCoinChildService tbCoinChildService;

    @Autowired
    private ITbCurrencyBaseService iTbCurrencyBaseService;
    @Resource
    private MaxAPI maxAPI;
    @Resource
    private BamBooAPI bamBooAPI;


    @Override
    public JSONResult syncCustomer() {
        QueryCustomerPageRequest queryCustomerPageRequest = new QueryCustomerPageRequest();
        queryCustomerPageRequest.setPage(1);
        queryCustomerPageRequest.setLimit(100);
        List<DistributionOrgInfoResponse> listResponses = bamBooAPI.queryCustomerList(queryCustomerPageRequest);
        if(!listResponses.isEmpty()){
            //无数据新增
            //有数据更新
            for (DistributionOrgInfoResponse listRespons : listResponses) {
                bbDistributionOrgService.addDistribution(listRespons);
            }
        }
        return JSONResult.ok();
    }

    //无数据新增
    //有数据更新
    @Override
    public JSONResult syncProduct() {
        //增加系统 币种数据
        List<TbBranch> branchList = iTbBranchService.getBranchList(1);

        //获取bamboo 列表

//        List<BbOrgBranch> bbBranch = iBbOrgBranchService.getList();
//        Map<Integer, String> collect = bbBranch.stream().collect(Collectors.toMap(it -> it.getId(), it -> it.getBranchCode()));

        //币种基础信息表 为了匹配 有合适的币种才添加进去
        List<TbCurrencyBase> baseCurlist =  iTbCurrencyBaseService.getList();
        Map<String, TbCurrencyBase> baseCurMap = baseCurlist.stream().collect(Collectors.toMap(ite -> ite.getCurCode(), ite -> ite));


        //获取客户列表
        List<BbDistributionOrg> list = bbDistributionOrgService.getDistributionOrgList();
        if(!list.isEmpty()){

            list.forEach(item->{
                QueryProductPageRequest queryProductPageRequest = new QueryProductPageRequest();
                queryProductPageRequest.setPage(1);
                queryProductPageRequest.setLimit(100);
//                queryProductPageRequest.setQuerySettlePrice(1);
                queryProductPageRequest.setCustomerCode(item.getOrgCode());
                List<ProductResultResponse> productList = bamBooAPI.queryProductList(queryProductPageRequest);
                System.out.println("数据：==="+item.getOrgCode()+"==="+productList.size());
                if(productList.size() > 0){
//                    productList.forEach(itemc->{
//                        bbProductService.addProduct(itemc);
//                    });
// 查询第三方下面的网点列表
                    List<BbOrgBranch> bbBranch = iBbOrgBranchService.getListByPid(item.getId());
                    //内部网点添加币种
                    branchList.forEach(branch->{
                        for (BbOrgBranch obj : bbBranch){
                            if(Integer.parseInt(branch.getOutId()) == obj.getId()){
                                tbcurrencyService.addCurrencyInfo(branch.getId(),productList,baseCurMap);
                            }
                        }
                    });
                }
            });
        }
        return JSONResult.ok();
    }

    @Override
    public JSONResult syncMax() {
//        ResponseData responseData = MaxAPI.siteList();
//        String data = responseData.getData();
//        JSONObject jsonObject = JSONObject.fromObject(data);
//        JSONArray array = jsonObject.getJSONArray("list");
//
//        //增加网点数据
//        for (int i = 0; i < array.size(); i++) {
//            JSONObject obj = array.getJSONObject(i);
//            tbBranchService.addBranch(null,
//                    obj.getString("sitecode"),
//                    obj.getString("sitename"),
//                    obj.getString("sitetel"),
//                    obj.getString("sitemanager"),
//                    0
//            );
//        }
        List<TbBranch> branchList = iTbBranchService.getBranchList(0);
        //增加币种数据
        branchList.forEach(branch->{
            //增加币种数据
            ResponseData responseData1 = maxAPI.currencyList(branch.getSitecode());
            if(null != responseData1){
                String data1 = responseData1.getData();
                if(StringUtils.isNoneBlank(data1)){
                    JSONArray jsonArray = JSONArray.fromObject(data1);
                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject obj2 = jsonArray.getJSONObject(j);

                        BigDecimal bigDecimal = new BigDecimal(obj2.getString("saleprice"));
                        Tbcurrency tbcurrency = new Tbcurrency();
                        tbcurrency.setBranchId(branch.getId());
                        tbcurrency.setCurrency(obj2.getString("currency"));
                        tbcurrency.setCurname(obj2.getString("curname"));
                        tbcurrency.setActualPrice(NumberUtil.mul(branch.getCurrencyPercent(),bigDecimal));
                        tbcurrency.setMarketPrice(bigDecimal);
                        tbcurrency.setStatus(0);
                        tbcurrency.setUnit(Integer.valueOf(obj2.getString("minunit")));
                        tbcurrency.setDataSource(1);
                        tbcurrencyMapper.insert(tbcurrency);
                    }
                }
            }
        });
        return null;
    }

    @Override
    public JSONResult syncMaxCoin() {
        List<TbBranch> branchList = tbBranchService.getBranchList(0);
        if(!branchList.isEmpty()){
            branchList.forEach(item->{
                ResponseData responseData = maxAPI.coinList(item.getSitecode());
                String data = responseData.getData();
                JSONObject jsonObject = JSONObject.fromObject(data);
                JSONArray array = jsonObject.getJSONArray("list");
                for (int i = 0; i < array.size(); i++) {
                    JSONObject obj = array.getJSONObject(i);
                    //增加零钱包数据
//                    tbCoinPurseService.addCoin(obj);
                }
            });
        }
        return JSONResult.ok();
    }
}
