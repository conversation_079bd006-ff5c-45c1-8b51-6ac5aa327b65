package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 订单图片表
    * </p>
*
* <AUTHOR>
* @since 2024-10-13
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_order_img")
    @ApiModel(value="TbOrderImg对象", description="订单图片表")
    public class TbOrderImg implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "订单号")
    private String orderNumber;

            @ApiModelProperty(value = "身份证正面url")
    private String cardFrontUrl;

            @ApiModelProperty(value = "身份证反面url")
    private String cardBackUrl;

            @ApiModelProperty(value = "电子水单url")
    private String waterBillUrl;

            @ApiModelProperty(value = "水单二维码下载地址（max）")
    private String icedownloadurl;

            @ApiModelProperty(value = "水单PDF存放地址（max）")
    private String icepdfurl;

            @ApiModelProperty(value = "完整水单图片地址（max）")
    private String iceimgurl;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


}
