package com.zzz.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.component.bamboo.BamBooAPI;
import com.zzz.component.bamboo.request.DeliveryDateRequest;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.dto.BranchUrgentDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.CommonInfo;
import com.zzz.entity.TbBranch;
import com.zzz.entity.TbBranchUrgent;
import com.zzz.mapper.TbBranchMapper;
import com.zzz.mapper.TbBranchUrgentMapper;
import com.zzz.response.JSONResult;
import com.zzz.service.ICommonInfoService;
import com.zzz.service.ITbBranchUrgentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 网点日期加急费用设置 服务实现类  test
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@Service
public class TbBranchUrgentServiceImpl extends ServiceImpl<TbBranchUrgentMapper, TbBranchUrgent> implements ITbBranchUrgentService {

    @Autowired
    private TbBranchUrgentMapper tbBranchUrgentMapper;

    @Autowired
    private TbBranchMapper tbBranchMapper;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Override
    public List<TbBranchUrgent> getUrgentListByBranchid(TbBranch tbBranch, String startDate, String endDate) {
        return tbBranchUrgentMapper.selectList(
                new LambdaQueryWrapper<TbBranchUrgent>()
                        .eq(TbBranchUrgent::getBranchId,tbBranch.getId())
                        .between(TbBranchUrgent::getDate,startDate,endDate)
                        .eq(TbBranchUrgent::getStatus,0)
        );
    }

    /**
     * 获取单个可提取和加急费详情
     * @param id
     * @param date
     * @return
     */
    @Override
    public JSONResult<BranchUrgentDto> getUrgentFeeDetail(Integer id, String date) {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.URGENT_TIME_P);

        // bamboo 默认0 费用 TODO
        if(id == -1){
            BranchUrgentDto branchUrgentDto = new BranchUrgentDto();
            branchUrgentDto.setId(99);
            branchUrgentDto.setBranchId(id);
            if(!list.isEmpty()){
                List<String> urgentTime = new ArrayList<>();

                Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));

                if(!list.isEmpty()){
                    String urgent = WxParamMap.get(CommonEnum.URGENT_TIME.name());
                    if(null != urgent && StringUtils.isNoneBlank(urgent)){
                        //生成需要加急费时间
                        for(int i=0;i<=Integer.parseInt(urgent);i++){
                            urgentTime.add(DateUtil.format(DateUtil.offsetDay(new Date(),i),"yyyy-MM-dd"));
                        }
                    }
                }

                String urgentFee = WxParamMap.get(CommonEnum.URGENT_FEE.name());

                if(null != urgentFee && StringUtils.isNoneBlank(urgentFee) && urgentTime.contains(date)){
                    branchUrgentDto.setFee(new BigDecimal(urgentFee));
                }else{
                    branchUrgentDto.setFee(new BigDecimal(0.00));
                }
            }else{
                branchUrgentDto.setFee(new BigDecimal(0.00));
            }

            branchUrgentDto.setDate(LocalDate.parse(date));
            return JSONResult.ok(branchUrgentDto);
        }
        TbBranchUrgent tbBranchUrgent = tbBranchUrgentMapper.selectOne(
                new LambdaQueryWrapper<TbBranchUrgent>()
                        .eq(TbBranchUrgent::getBranchId, id)
                        .eq(TbBranchUrgent::getDate, date)
                        .eq(TbBranchUrgent::getStatus, 0)
        );
        TbBranch tbBranch = tbBranchMapper.selectById(id);

        List<TbBranchUrgent> urgentList = getUrgentList(tbBranch,list);
        if(!urgentList.isEmpty()){
            Map<String, TbBranchUrgent> collect = urgentList.stream().collect(Collectors.toMap(item->item.getDate().toString(), item -> item));
            if(collect.get(date) != null){
                tbBranchUrgent = collect.get(date);
            }
        }
        //无加急费用
        if(null == tbBranchUrgent){
            return JSONResult.error(CommonErrorEnum.URGENT_FEE_IS_NULL);
        }
        return JSONResult.ok(SpringBeanUtil.copyProperties(tbBranchUrgent,BranchUrgentDto.class));
    }

    private List<TbBranchUrgent> getUrgentList(TbBranch tbBranch,List<CommonInfo> list){
        List<TbBranchUrgent> urgentList = new ArrayList<>();
        if(!list.isEmpty()){
            Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
            String urgentFee = WxParamMap.get(CommonEnum.URGENT_FEE.name());
            String extract = WxParamMap.get(CommonEnum.EXTRACT_TIME.name());
            String urgent = WxParamMap.get(CommonEnum.URGENT_TIME.name());

            if(StringUtils.isNoneBlank(extract)){
                //遍历生成可提取时间
                String[] split = extract.split("-");
                int start = Integer.parseInt(split[0]);
                int end = Integer.parseInt(split[1]);
                BigDecimal fee = new BigDecimal(0);
                if(StringUtils.isNoneBlank(urgentFee)){
                    fee = new BigDecimal(urgentFee);
                }
                for(int i=start;i<=end;i++){
                    TbBranchUrgent tbBranchUrgent = new TbBranchUrgent();
                    tbBranchUrgent.setId(i);
                    tbBranchUrgent.setBranchId(tbBranch.getId());
                    tbBranchUrgent.setFee(new BigDecimal(0));
                    DateTime dateTime = DateUtil.offsetDay(new Date(), i);
                    tbBranchUrgent.setDate(LocalDate.parse(DateUtil.format(dateTime,"yyyy-MM-dd")));
                    urgentList.add(tbBranchUrgent);
                }
                //加急
                for(int j=0;j<=Integer.parseInt(urgent);j++){
                    urgentList.get(j).setFee(fee);
                }
                //TODO  排除不可提取时间

            }
        }
        return urgentList;
    }
}
