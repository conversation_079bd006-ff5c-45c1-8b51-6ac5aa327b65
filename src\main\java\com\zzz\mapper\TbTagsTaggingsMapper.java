package com.zzz.mapper;

import com.zzz.entity.TbTags;
import com.zzz.entity.TbTagsTaggings;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 网点绑定标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TbTagsTaggingsMapper extends BaseMapper<TbTagsTaggings> {


    List<TbTags> selectListBySiteId(@Param("siteId")Integer siteId);
}
