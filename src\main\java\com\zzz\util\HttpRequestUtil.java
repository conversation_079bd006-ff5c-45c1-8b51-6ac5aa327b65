package com.zzz.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;

/**
 * HTTP 请求工具类
 */
@Slf4j
public class HttpRequestUtil {

    /**
     * 发送 HTTP GET 请求
     * @param url 请求 URL
     * @return 响应结果
     */
    public static String sendGetRequest(String url) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            URLConnection connection = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) connection;
            
            // 设置请求属性
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.setConnectTimeout(15000);
            httpUrlConnection.setReadTimeout(60000);
            
            // 建立连接
            httpUrlConnection.connect();
            
            // 读取响应
            in = new BufferedReader(new InputStreamReader(httpUrlConnection.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
                result.append("\r\n");
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("发送 GET 请求异常: {}", e.getMessage(), e);
            return null;
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 发送 HTTP POST 请求
     * @param url 请求 URL
     * @param authorization 认证信息
     * @param body 请求体
     * @return 响应结果
     */
    public static String sendPostRequest(String url, String authorization, String body) {
        StringBuilder result = new StringBuilder();
        PrintWriter out = null;
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            URLConnection connection = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) connection;
            
            // 设置请求属性
            httpUrlConnection.setRequestProperty("Content-Type", "application/json");
            httpUrlConnection.setRequestProperty("authorization", authorization);
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            
            // 发送请求
            out = new PrintWriter(httpUrlConnection.getOutputStream());
            out.write(body);
            out.flush();
            
            // 建立连接
            httpUrlConnection.connect();
            
            // 读取响应
            in = new BufferedReader(new InputStreamReader(httpUrlConnection.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
                result.append("\r\n");
            }
            
            return result.toString();
        } catch (Exception e) {
            log.error("发送 POST 请求异常: {}", e.getMessage(), e);
            return null;
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                log.error("关闭输入/输出流异常: {}", e.getMessage(), e);
            }
        }
    }
}
