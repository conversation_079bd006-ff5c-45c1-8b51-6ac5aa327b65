package com.zzz.config.aws;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * packageName com.zzz.controller
 *aws配置类
 * <AUTHOR>
 * @version JDK 11
 * @date 2024/6/19
 */
@Slf4j
@Data
@EnableScheduling
@Configuration
public class AwsConfig {
 
    @Value("${aws.accessKey}")
    private String accessKey;
 
    @Value("${aws.secretKey}")
    private String secretKey;
 
    @Value("${aws.region}")
    private String region;
 
    @Value("${aws.bucketName}")
    private String bucketName;
 
    @Bean
    public AmazonS3 getAmazonS3() {
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        ClientConfiguration baseOpts = new ClientConfiguration();
        //
        baseOpts.setSignerOverride("S3SignerType");
        baseOpts.setProtocol(Protocol.HTTPS);
        //
        AmazonS3 amazonS3 = AmazonS3ClientBuilder.standard()
                .withRegion(region)
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
//                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(hostName, region))  // 如果有endpoint，可以用这个，这个和withRegion(Region)不能一起使用
//                .withPathStyleAccessEnabled(true)  // 如果配置了S3域名，就需要加这个进行路径访问，要不然会报AccessKey不存在的问题
                .withClientConfiguration(baseOpts)
                .build();
        return amazonS3;
    }
}