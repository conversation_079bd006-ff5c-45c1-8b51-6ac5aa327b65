package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-08
 **/
@Data
public class CustomerSettlePriceResponse {

    private String customerCode;
    private String customerName;
    private BigDecimal settlePrice;
    private String currencyCode;
    private String currencyName;
    private String settleCurrencyCode;
    private String settleCurrencyName;
    private BigDecimal listPrice;
}
