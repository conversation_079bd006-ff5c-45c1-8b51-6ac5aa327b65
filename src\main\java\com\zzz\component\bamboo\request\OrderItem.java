package com.zzz.component.bamboo.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *  订单子对象
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class OrderItem {

    /**
     *商品名称
     */
    private String productCname;
    /**
     *商品编码
     */
    private String code;
    /**
     *订购数量
     */
    private BigDecimal productItemNum;
    /**
     *货币币种
     */
    private String currencyCode;
    /**
     *币种面值
     */
    private BigDecimal currencyFaceValue;

    /**
     *币种面值
     */
    private BigDecimal currencyFaceAmount;

    /**
     *仓库编码
     */
    private String warehouseCode;
    /**
     *仓库名称
     */
    private String warehouseName;

    /**
     * 冻结库存code
     */
    private String billCode;
}
