package com.zzz.config.ums;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * UMS支付配置类
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-15
 */
@Data
@Component
@ConfigurationProperties(prefix = "ums.pay")
public class UmsPayConfig {
    private String appId;
    private String appKey;
    private String mid;
    private String tid;
    private String subMid;
    private String sourceCode;
    private String notifyUrl;
    private String returnUrl;
    private String wxPayUrl;
    private String aliPayUrl;
    private String unionPayUrl;
    private String queryUrl;
    private String closeUrl;
    private String refundUrl;
 
    /**
     * 获取机构商户号
     * 兼容代码中使用的getInstMid方法
     * 在UmsPayServiceImpl中被调用，需要根据支付类型返回对应的InstMid
     *
     * @return 默认返回MINIDEFAULT，实际应根据支付类型返回对应的InstMid
     */
    public String getInstMid() {
        // 默认返回MINIDEFAULT，与TestMain中的示例一致
        return "MINIDEFAULT";
    }
}
