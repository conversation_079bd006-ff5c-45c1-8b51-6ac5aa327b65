//package com.zzz.config.rabbitmq;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.CustomExchange;
//import org.springframework.amqp.core.Queue;
//import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
//import org.springframework.amqp.support.converter.MessageConverter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.Order;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * @ Author     ：zqw
// * @ Description：
// */
//@Configuration
//public class RabbitConfig {
//
//    @Bean
//    public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
//        return new Jackson2JsonMessageConverter(objectMapper);
//    }
//
//
//    /**-----------------------------------------         测试队列       ----------------------------------------*/
//
//    /**
//     * 测试消息队列
//     *
//     * @return
//     */
//    @Bean
//    @Order(value = Integer.MIN_VALUE)
//    public Queue testMessageQueue() {
//        return new Queue(EnumsRabbitQueue.TEST_QUEUE.getName());
//    }
//
//    /**
//     * 发送测试消息交换机
//     *
//     * @return CustomExchange
//     */
//    @Bean
//    @Order(value = Integer.MIN_VALUE + 1)
//    public CustomExchange testMessageDelayExchange() {
//        Map<String, Object> args = new HashMap<>();
//        args.put("x-delayed-type", "direct");
//        return new CustomExchange(EnumsRabbitQueue.TEST_QUEUE.getExchange(), "x-delayed-message", true, false, args);
//    }
//
//    /**
//     * 绑定发送测试消息延迟队列
//     *
//     * @return Binding
//     */
//    @Bean
//    @Order(value = Integer.MIN_VALUE + 2)
//    public Binding testMessageBinding() {
//        return BindingBuilder.bind(testMessageQueue()).to(testMessageDelayExchange()).with(EnumsRabbitQueue.TEST_QUEUE.getDelayKey()).noargs();
//    }
//
//}
