package com.zzz.service.impl;

import com.zzz.entity.BbApiRequestRecord;
import com.zzz.mapper.BbApiRequestRecordMapper;
import com.zzz.service.IBbApiRequestRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 请求记录表（Bamboo） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class BbApiRequestRecordServiceImpl extends ServiceImpl<BbApiRequestRecordMapper, BbApiRequestRecord> implements IBbApiRequestRecordService {

}
