package com.zzz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zzz.dto.UserCouponDto;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbCouponReceive;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * <p>
 * 优惠券领取表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TbCouponReceiveMapper extends BaseMapper<TbCouponReceive> {

    IPage<UserCouponDto> getUserCouponPage(Page<UserCouponDto> page,@Param("userId") Integer userId);

    TbCouponMrg getCouponByRecId(@Param("couponId") Integer couponId);
}
