package com.zzz.emuns;

/**
 * packageName com.zzz.entity.vo
 * 订单支付状态枚举
 * <AUTHOR>
 * @version JDK 11
 * @date 2024/6/17
 */
public enum OrderPayStatusEnum {
    /** 未支付 */
    NO_PAY(0),

    /** 已支付 */
    PAY(1),

    /** 支付失败 */
    PAY_FAIL(2)
    ;
    /**
     * 状态
     */
    public final int value;

    OrderPayStatusEnum(int value) {
        this.value = value;
    }

    public static Object getByValue(Integer status) {
        for(OrderStatusEnum OrderPayStatusEnum:OrderStatusEnum.values()){
            if(OrderPayStatusEnum.value==status){
                return OrderPayStatusEnum;
            }
        }
        return null;
    }

}
