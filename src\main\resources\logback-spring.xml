<?xml version="1.0" encoding="UTF-8"?>
<!-- scan: 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。 scanPeriod: 设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
	debug: 当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 configuration
	子节点为 appender、logger、root -->
<configuration debug="false" scan="true"
               scanPeriod="60 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 定义日志文件 输出位置 -->
    <property name="PATH_ROOT" value="./logs/service"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr"
                    converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 日志文件名称 -->
    <property name="FILE_NAME" value="service"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){red} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%thread]){cyan} %clr(%-40.40logger{50}){magenta} %clr(:){magenta} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>
    <!-- ConsoleAppender 控制台输出日志 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--解决乱码问题 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!-- 滚动记录文件，先将日志记录到指定文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PATH_ROOT}/${FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--每天对日志文件进行压缩保存-->
            <fileNamePattern>${PATH_ROOT}/${FILE_NAME}.%d{yyyy-MM-dd}.log.gz
            </fileNamePattern>
            <!--日志文件保留天数-->
            <!--            <MaxHistory>30</MaxHistory>-->
            <!--总日志大小-->
            <!--            <totalSizeCap>10GB</totalSizeCap>-->
            <!--日志文件过大时会使编辑器打开非常慢，因此设置日志最大50MB -->
            <!--            <maxFileSize>50MB</maxFileSize>-->
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 异常日志输出文件 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PATH_ROOT}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${PATH_ROOT}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
            <!--解决乱码问题 -->
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- **************************************** 不同环境的日志打印 级别从高到低 OFF 、 FATAL 、 ERROR 、 WARN 、 INFO 、 DEBUG 、 TRACE 、 ALL **************************************** -->
    <!--开发环境:打印控制台 -->
    <springProfile name="dev">
        <logger name="com.zzz" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
    </springProfile>

    <!--测试环境:打印控制台 -->
    <springProfile name="test">
        <logger name="com.zzz" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
    </springProfile>

    <!--线上环境:打印控制台 -->
    <springProfile name="prod">
        <logger name="com.zzz" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
    </springProfile>

    <!--预发布环境:打印控制台 -->
    <springProfile name="pre">
        <logger name="com.zzz" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
    </springProfile>
    <logger name="io.lettuce.core" level="off"/>
    <!-- Level: FATAL 0 ERROR 3 WARN 4 INFO 6 DEBUG 7 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR"/>
    </root>

</configuration>