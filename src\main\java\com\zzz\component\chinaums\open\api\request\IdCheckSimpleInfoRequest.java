package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.IdCheckSimpleInfoResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/2/21
 * Time: 14:49
 * 所属模块：
 * 功能说明：简项公民身份证信息核查请求报文
 */
public class IdCheckSimpleInfoRequest implements OpenApiRequest<IdCheckSimpleInfoResponse> {

    @ApiField(name = "certifId",required = true ,desc = "公民身份号码")
    private String certifId;
    @ApiField(name = "name",required = true ,desc = "姓名")
    private String name;
    public Class<IdCheckSimpleInfoResponse> responseClass() {
        return IdCheckSimpleInfoResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "身份证验证";
    }

    public String serviceCode() {
        return "/creditcheck/idcheck/simpleinfo";
    }

    public boolean needToken() {
        return true;
    }

    public String getCertifId() {
        return certifId;
    }

    public void setCertifId(String certifId) {
        this.certifId = certifId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
