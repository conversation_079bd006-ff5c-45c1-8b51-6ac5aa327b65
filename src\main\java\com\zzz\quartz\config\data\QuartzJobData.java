package com.zzz.quartz.config.data;

import com.zzz.quartz.domain.QuartzJob;

/**
 * 定时任务数据
 * <AUTHOR> to 2024/09/15
 */
public class QuartzJobData {

    /**
     * 获取支付失败订单任务数据组装
     * <AUTHOR> to 2024/9/15
     */
    public QuartzJob payFailOrderRetryJobData() {
        QuartzJob quartzJob = new QuartzJob();
        quartzJob.setId(2L);
        quartzJob.setBeanName("com.zzz.quartz.task.RetryPayFailOrderTask");
        quartzJob.setMethodName("run");
        quartzJob.setParams(null);
        quartzJob.setCronExpression("0/10 * * * * ?");
        quartzJob.setIsPause(false);
        quartzJob.setJobName("支付失败订单重试任务");
        return quartzJob;
    }
}
