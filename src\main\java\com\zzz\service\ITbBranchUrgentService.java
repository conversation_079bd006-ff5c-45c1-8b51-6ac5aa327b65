package com.zzz.service;

import com.zzz.dto.BranchUrgentDto;
import com.zzz.entity.TbBranch;
import com.zzz.entity.TbBranchUrgent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;

import java.util.List;

/**
 * <p>
 * 网点日期加急费用设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
public interface ITbBranchUrgentService extends IService<TbBranchUrgent> {

    List<TbBranchUrgent> getUrgentListByBranchid(TbBranch tbBranch, String startDate, String endDate);

    JSONResult<BranchUrgentDto> getUrgentFeeDetail(Integer id, String date);
}
