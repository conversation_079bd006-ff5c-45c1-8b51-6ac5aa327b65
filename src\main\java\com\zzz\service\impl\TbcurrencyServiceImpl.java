package com.zzz.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.MaxAPI;
import com.zzz.component.max.request.MaxCurnumRequest;
import com.zzz.component.max.response.MaxCurnumResponse;
import com.zzz.dto.CurrencyDto;
import com.zzz.dto.CurrencyPageDto;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.TbCurrencyBase;
import com.zzz.entity.Tbcurrency;
import com.zzz.entity.UserInformation;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbcurrencyMapper;
import com.zzz.service.*;
import com.zzz.util.CusNumberUtil;
import com.zzz.util.SpringBeanUtil;
import com.zzz.util.UserContextUtil;
import com.zzz.vo.CurrencyPageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 币种 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
@Slf4j
public class TbcurrencyServiceImpl extends ServiceImpl<TbcurrencyMapper, Tbcurrency> implements ITbcurrencyService {
    @Autowired
    private TbcurrencyMapper tbcurrencyMapper;
    @Autowired
    private ITbCurrencyBaseService iTbCurrencyBaseService;
    @Resource
    private ICommonInfoService iCommonInfoService;
    @Resource
    private ITbmonusdrateService iTbmonusdrateService;
    @Resource
    private IUserInformationService iUserInformationService;
    @Resource
    private MaxAPI maxAPI;

    @Override
    public List<CurrencyDto> getCurrencyHot() {
//        List<Tbcurrency> list = tbcurrencyMapper.selectList(
//                new QueryWrapper<Tbcurrency>()
//                        .select("MIN(market_price) market_price,currency,curname,url,country,unit")
//                        .eq("status",1)
//                        .eq("popular",1)
//                        .groupBy("currency")
//        );
        List<CurrencyDto> list = tbcurrencyMapper.getCurrencyHot();
        list.forEach(item->{
            BigDecimal mul = NumberUtil.mul(item.getMarketPrice(), item.getRateUnit());
            item.setMarketPrice(NumberUtil.round(mul.doubleValue(), 4, RoundingMode.HALF_UP));
        });
        return list;
    }

    @Override
    public CurrencyPageDto getCurrencyPage(CurrencyPageVo req) {
        if(CusNumberUtil.isNumber(req.getPage())){
            throw new ServiceException(CommonErrorEnum.PAGE_CODE_IS_NULL);
        }
        if(CusNumberUtil.isNumber(req.getSize())){
            throw new ServiceException(CommonErrorEnum.PAGE_SIZE_IS_NULL);
        }
        //分页查询数据
        Page<CurrencyDto> page = new Page<>(req.getPage(), req.getSize());
        IPage<CurrencyDto> dataIPage = tbcurrencyMapper.selectCurrencyPage(page, req);
        List<CurrencyDto> list = dataIPage.getRecords();
        list.forEach(item->{
            BigDecimal mul = NumberUtil.mul(item.getMarketPrice(), item.getRateUnit());
            item.setMarketPrice(NumberUtil.round(mul.doubleValue(), 4, RoundingMode.HALF_UP));
        });
        //返回查询结果
        return CurrencyPageDto.builder()
                .updateTime(DateUtil.format(LocalDateTime.now(),"HH:mm:ss"))
                .totalPage(dataIPage.getPages())
                .totalRecord(dataIPage.getTotal())
                .list(list)
                .build();
    }

    @Override
    public CurrencyDto getCurrencyByCur(String currency,Integer branchId) {
        //1、非空校验
        if(StringUtils.isBlank(currency)){
            throw new ServiceException(CommonErrorEnum.CUR_CODE_IS_NULL);
        }
        //只返回 币种基础信息
        if(branchId == null){
            TbCurrencyBase currencyByCode = iTbCurrencyBaseService.getCurrencyByCode(currency);
            CurrencyDto currencyDto = new CurrencyDto();
            currencyDto.setCurrency(currencyByCode.getCurCode());
            currencyDto.setCurname(currencyByCode.getCurName());
            currencyDto.setUrl(currencyByCode.getPicUrl());
            return currencyDto;
        }
//        if(CusNumberUtil.isNumber(branchId)){
//            throw new ServiceException(CommonErrorEnum.BRANCH_ID_IS_NULL);
//        }

        //2、获取数据
        Tbcurrency tbcurrency = tbcurrencyMapper.selectOne(
                new LambdaQueryWrapper<Tbcurrency>()
                        .eq(Tbcurrency::getCurrency,currency)
                        .eq(Tbcurrency::getBranchId,branchId)
                        .eq(Tbcurrency::getStatus,1)
                        .eq(Tbcurrency::getIsDel,0)
        );
        if(null == tbcurrency){
            throw new ServiceException(CommonErrorEnum.CURRENCY_IS_NULL);
        }
        CurrencyDto currencyDto = SpringBeanUtil.copyProperties(tbcurrency, CurrencyDto.class);

        //3.获取币种基础数据 并设置
        TbCurrencyBase tbCurrencyBase = iTbCurrencyBaseService.getCurrencyByCode(currencyDto.getCurrency());
        if(null == tbCurrencyBase){
            throw new ServiceException(CommonErrorEnum.CURRENCY_BASE_IS_NULL);
        }
        currencyDto.setMinUnit(tbCurrencyBase.getMinAmt());
        currencyDto.setRateUnit(tbCurrencyBase.getRateUnit());
        currencyDto.setUnit(tbCurrencyBase.getMinUnit());
        currencyDto.setUrl(tbCurrencyBase.getPicUrl());

        // // 4.获取系统配置：单笔交易金额不超过等值美元
        // CommonInfo commonInfo = iCommonInfoService.getOne(Wrappers.<CommonInfo>lambdaQuery()
        //         .eq(CommonInfo::getCodeValue, CommonEnum.NO_EXCEED_USD.name()));
        // if (null != commonInfo) {
        //     Tbmonusdrate tbmonusdrate = iTbmonusdrateService.getOne(Wrappers.<Tbmonusdrate>lambdaQuery()
        //             .eq(Tbmonusdrate::getMonth, DateUtil.format(new Date(), "yyyyMM"))
        //             .eq(Tbmonusdrate::getCurrency, currencyDto.getCurrency()));
        //     if (null != tbmonusdrate) {
        //         // 其他币种金额 * 美元折算价 / 100 = 等值美元金额
        //         // 其他币种金额最大兑换金额 = 等值美元金额 / 美元折算价 * 100
        //         BigDecimal maxAmount = NumberUtil.div(Convert.toBigDecimal(commonInfo.getAclValue()), tbmonusdrate.getStatrate(), 2).multiply(new BigDecimal("100"));
        //         // 最大购买数量 = 最大兑换金额 / 售卖汇率
        //         BigDecimal maxUnit = NumberUtil.div(maxAmount, currencyDto.getMarketPrice());
        //         // 返回前端值：币种最小倍数的整数倍
        //         currencyDto.setMaxUnit(Convert.toInt(NumberUtil.mul(NumberUtil.div(maxUnit, currencyDto.getUnit(), 0, RoundingMode.DOWN), currencyDto.getUnit())));
        //     }
        // }

        // 4.最大币种额度需要从外管局实时获取
        // UserInformation userInformation = iUserInformationService.getOne(Wrappers.<UserInformation>lambdaQuery()
        //         .eq(UserInformation::getUserId, UserContextUtil.get().getId())
        // );
        // if (null == userInformation) {
        //     throw new ServiceException(CommonErrorEnum.REALNAME_IS_NULL);
        // }
        //
        // MaxCurnumRequest request = new MaxCurnumRequest();
        // request.setCurrency(currency);
        // request.setDocno(userInformation.getCard());
        // request.setPersonName(userInformation.getName());
        // ResponseData responseData = maxAPI.maxCurnum(request);
        // log.info("max用户币种可兑换最大数量：{}", JSONUtil.toJsonStr(responseData));
        // if (!"200".equals(responseData.getCode())) {
        //     throw new ServiceException(Integer.valueOf(responseData.getCode()), "上游返回(" + responseData.getMessage() + ")");
        // }
        // MaxCurnumResponse maxCurnumResponse = JSONUtil.toBean(responseData.getData(), MaxCurnumResponse.class);
        // if (null != maxCurnumResponse) {
        //     // 最大购买数量
        //     BigDecimal maxUnit = maxCurnumResponse.getMulDay();
        //     // 返回前端值：币种最小倍数的整数倍
        //     currencyDto.setMaxUnit(Convert.toInt(NumberUtil.mul(
        //             NumberUtil.div(maxUnit, currencyDto.getUnit(), 0, RoundingMode.DOWN), currencyDto.getUnit()
        //     )));
        // }

        currencyDto.setUpdateTime(DateUtil.format(tbcurrency.getUpdateTime(),"HH:mm:ss"));
        //基础汇率 * 汇率单位
        currencyDto.setMarketPrice(NumberUtil.mul(currencyDto.getMarketPrice(),tbCurrencyBase.getRateUnit()));
        return currencyDto;
    }

    @Override
    public List<Tbcurrency> getCurrencyIdsByCurName(String currency) {
        List<Tbcurrency> list = tbcurrencyMapper.selectList(
                new LambdaQueryWrapper<Tbcurrency>()
                        .eq(Tbcurrency::getCurrency,currency)
                        .eq(Tbcurrency::getStatus,1)
                        .eq(Tbcurrency::getIsDel,0)
        );
        return list;
    }

    @Override
    public CurrencyDto getCurrencyById(Integer curId) {
        Tbcurrency tbcurrency = tbcurrencyMapper.selectOne(
                new LambdaQueryWrapper<Tbcurrency>()
                        .eq(Tbcurrency::getId,curId)
        );
        CurrencyDto currencyDto = SpringBeanUtil.copyProperties(tbcurrency, CurrencyDto.class);
        TbCurrencyBase currencyByCode = iTbCurrencyBaseService.getCurrencyByCode(currencyDto.getCurrency());
        currencyDto.setUnit(currencyByCode.getMinUnit());
        currencyDto.setRateUnit(currencyByCode.getRateUnit());
        currencyDto.setMinUnit(currencyByCode.getMinAmt());
        currencyDto.setUpdateTime(DateUtil.format(LocalDateTime.now(),"HH:mm:ss"));
        return currencyDto;
    }

    @Override
    @Transactional
    public void addCurrencyInfo(Integer id, List<ProductResultResponse> productList, Map<String, TbCurrencyBase> baseCurMap) {
        productList.forEach(item->{
            TbCurrencyBase tbCurrencyBase = baseCurMap.get(item.getCurrencyCode());
            if(null != tbCurrencyBase && tbCurrencyBase.getMinUnit() == Integer.parseInt(item.getCurrencyFaceValue())){
                Tbcurrency tbcurrency = new Tbcurrency();
                tbcurrency.setBranchId(id);
                tbcurrency.setCurrency(tbCurrencyBase.getCurCode());
                tbcurrency.setCurname(tbCurrencyBase.getCurName());
                tbcurrency.setUrl(StringUtils.isBlank(tbCurrencyBase.getPicUrl()) ? item.getMainPictureUrl():tbCurrencyBase.getPicUrl());
                tbcurrency.setCountry("");
                tbcurrency.setActualPrice(tbCurrencyBase.getSellRate());
                tbcurrency.setMarketPrice(tbCurrencyBase.getSellRate());
                tbcurrency.setStatus(0);
                tbcurrency.setUnit(Integer.valueOf(item.getCurrencyFaceValue()));
                tbcurrency.setDataSource(1);
                tbcurrency.setOutCurCode(item.getId()+"");
                tbcurrencyMapper.insert(tbcurrency);
            }
        });
    }

    @Override
    public List<CurrencyDto> getCurrencyListById(String bId) {
        List<CurrencyDto> list = tbcurrencyMapper.getCurrencyListById(bId);
        if(list.isEmpty()){
            throw new ServiceException(CommonErrorEnum.CURRENCY_IS_NULL);
        }
        return list;
    }
}
