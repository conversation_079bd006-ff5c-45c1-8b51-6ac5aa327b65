package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.response.RealNameVerifyResponse;
import com.zzz.component.chinaums.open.api.annotation.ApiField;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/6
 * Time: 15:23
 * 所属模块：
 * 功能说明：实名认证
 */
public class RealNameVerifyRequest implements OpenApiRequest<RealNameVerifyResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;

    public Class<RealNameVerifyResponse> responseClass() {
        return RealNameVerifyResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "实名认证";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/realname/verify";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
