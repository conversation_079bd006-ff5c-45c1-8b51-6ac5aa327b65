package com.zzz.emuns;

/**
 * redis key 路径枚举
 */
public enum RedisKeyEnum {

    //支付宝小程序登录 1天有效期
    ALI_LOGIN_TOKEN("ali:login:", 60 * 60 * 24 * 1, "用户登录token"),
    //地区缓存
    ALL_AREA_CACHE("area:cache:", -1, "所有地区cache"),

    //订单防重提交，分布式锁key
    CREATE_ORDER_KEY("order:create:%s",0,"订单防重提交key"),



    //支付加锁 根据订单订单号
    ORDER_PAY_KEY("order:pay:%s",0,"根据订单号加锁"),


    //异步通知 支付结果，防重
    ORDER_NOTIFY_KEY("order:notify:%s",0,"订单防重提交key"),


    CAPTCHA_KEY("%s:captcha:%s",0,"图形验证码"),

    SUBMIT_ORDER_TOKEN_KEY("order:submit:%s:%s",0,"提交订单令牌的缓存key"),

    /**
     * 区分不同业务所发送的短信
     * key名称规则：sms:code:{业务名称}+{phone}
     */
    PHONE_VALID("sms:code:", 60 * 5, "短信验证码缓存");

    /**
     * redis 储存key
     */
    private final String key;
    /**
     * redis 储存时间，单位：s
     */
    private final int expireTime;
    /**
     * key的描述
     */
    private final String keyInfo;


    RedisKeyEnum(String key, int expireTime, String keyInfo) {
        this.key = key;
        this.expireTime = expireTime;
        this.keyInfo = keyInfo;
    }


    public String getKey() {
        return key;
    }

    public int getExpireTime() {
        return expireTime;
    }

    public String getKeyInfo() {
        return keyInfo;
    }
}
