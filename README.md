#### 一.项目名称
   springboot 公共框架项目
   
#### 二.项目概述
   关于springboot的公共基础框架
   
#### 三.项目目录结构
+ springboot父项目
    + controller 控制器，对外提供api
    + entity 实体类
    + mapper DAO层
    + service 服务层
       + impl 实现类
            
#### 四.命名规范
1. 包命名
     + 包命使用小写英文字母进行命名，并使用“.”进行分割，每个被分割的单元只能包含一个名词。一般地，包命名常采用顶级域名作为前缀，例如com，net，org，edu，gov，cn，io等
2. 类命名
     + 类(Class)通常采用名词进行命名，且首字母大写，如果一个类名包含两个以上名词，建议使用驼峰命名(Camel-Case)法书写类名,每个名词首字母也应该大写。一般地，类名的书写尽量使其保持简单和描述的完整性，因此在书写类名时不建议使用缩写(一些约定俗成的命名除外，例如Internationalization and Localization缩写成i18n，Uniform Resource Identifier缩写成URI，Data Access Object缩写成DAO，JSON Web Token缩写成JWT，HyperText Markup Language缩写成HTML等等)
3. 接口命名
     + 首字母大写，与普通类名不同的是，接口命名时通常采用形容词或动词来描述接口的动作行为,例如：Closeable，AfterAdvice
4. 抽象类命名
     + 抽象类(Abstract Class)是一种特殊的类，其命名与普通类的命名规范相当。一般地，为了将抽象类与普通类和接口做出区别，提高抽象类的可读性，在命名抽象类时，会以“Abstract”/“Base”作为类命的前缀，例如：AbstractRepository，BaseDao     
5. 异常类命名
     + 异常类(Exception Class)也是类的一种，但与普通类命名不同的是，异常类在命名时需要使用“Exception”作为其后缀，例如：FileNotFoundException，UserAlreadyExistException。另外，在Java中还有另外一类异常类，它们属于系统异常，这一类异常类的命名使用“Error”作为其后缀，以区分Exception(编码，环境，操作等异常)，例如：VirtualMachineError，NoClassDefFoundError  
6. 方法命名
     + 方法(Method)命名时,其首字母应该小写，如果方法签名由多个单词组成，则从第二个单词起，使用驼峰命名法进行书写。一般地，在对方法进行命名时，通常采用动词/动词+名词的组合，例如：getUserName     
7. 变量命名
     + 变量(Variable)命名包括参数名称，成员变量和局部变量。变量命名通常以小写字母开头，如果变量名由多个单词构成，则从第二个单词起首字母需要大写，例如：id,nickName
8. 常量命名
     + 常量名称采用全部大写的英文单词书写，如果常量名称由多个单词组成，则单词之间统一使用“_”进行分割,例如：MAX_AGE_VALUE
9. 枚举命名
     + 枚举(Enum)类是一种特殊的类，其命名规范遵循普通类的命名约束条件，首字母大写，采用驼峰命名法；枚举类中定义的值的名称遵循常量的命名规范，且枚举值的名称需要与类名有一定的关联性，例如：  
     public enum Color{
         RED,YELLOW,BLUE,GREEN,WHITE;
     }
     public enum PhysicalSize{
         TINY,SMALL,MEDIUM,LARGE,HUGE,GIGANTIC;
     }              
10. 接口实现类命名
     + 接口实现类使用“Impl作为后缀”，例如：OrderServiceImpl
11. 测试类和测试方法命名
     + 测试类采用被测试业务模块名/被测试接口/被测试类+“Test”的方法进行书写，例如：UserServiceTest，测试类中的测试函数采用“test”+用例操作_状态的组合方式进行书写，例如：testFindByUsernameAndPassword     
12. 数据库表及字段命名
     + 数据表采用小写加下划线方式命名，例如：sys_user
     + 字段的命名使用小写加下划线方式命名，例如 id和user_id        
                     
#### 五.接口规范说明
   + 所有接口以JSON格式返回数据
   + 正常返回code='200',code为其它值则表示返回结果有问题
   + 接口规范参照REST标准，获取数据使用get请求方式，添加数据使用post请求方式，编辑更新数据使用put请求方式，删除数据使用delete请求方式
   + 分页常规参数没有传后端，则默认为page=1,pageSize=10,即默为查询10条数据

#### 六.环境准备
   + JDK1.8
   + Redis (v2.1.6+)
   + Mysql (v5.7)
   + Maven (v3+)
   + rabbitmq (3.7+)
   
#### 七.技术栈

|         技术         | 说明                             | 官网                                                                              |
|:------------------:|--------------------------------|---------------------------------------------------------------------------------|
|  SpringBoot 2.4.0  | web开发必备框架                      | [https://spring.io/projects/spring-boot](https://spring.io/projects/spring-boot) |
|   MyBatis 2.0.1    | ORM框架                          | http://www.mybatis.org/mybatis-3/zh/index.html                                  |
| MyBatisPlus 3.1.1  | 零sql，简化数据库操作，分页插件              | [https://baomidou.com/](https://baomidou.com/)                                  |
|    Redis 2.1.6     | 缓存加速，多数据结构支持业务功能               | [https://redis.io](https://redis.io)                                            |
|   Lombok 1.18.30   | 简化代码                           | [https://projectlombok.org](https://projectlombok.org)                          |
|   Hutool 5.8.26    | Java工具类库                       | https://hutool.cn/docs/#/                                                       |
|   Kinife4j 4.0.0   | API文档生成工具                      | https://doc.xiaominfo.com/                                                      |
|   SaToken 1.37.0   | Sa-Token 权限认证                  | https://sa-token.cc/doc.html                                                      |
|  redisson 3.12.3   | 分布式锁                           | https://github.com/redisson/redisson                                            |
| commons-pool 2.6.1 | Aredis序列化实例                    |                                                                                 |
|    rabbitmq 3.8    | 异步消息,需要安装x-delay-message延时消息插件 |                                                     |

#### 八.分支管理
1. master 分支：
    + master 为主分支，也是用于部署生产环境(PRO)的分支，确保master分支稳定性
      master 分支一般由release 或hotfix分支合并，任何时间都不能直接修改代码
2. develop 分支：
    + develop 为开发分支，始终保持最新完成以及bug修复后的代码
     一般开发的新功能时，feature分支都是基于develop分支下创建的，feature分支新功能/缺陷，开发完成并自测无问题后，合并到develop上
3. feature 分支：
    + 开发新功能/修复缺陷时，以develop为基础创建feature分支
      分支命名: feature/ 开头的为特性分支， 命名规则: feature/user_module、 feature/cart_module，该需求上线，便将此分支删除
4. test 分支：
    + test为测试分支，以develop分支为基础创建，作为测试环境(FAT)的部署分支
     （ 当有一组feature开发完成并自测无问题，首先会合并到develop分支，进入提测时，会基于develop创建test分支。如果测试过程中若存在bug需要修复，则直接由开发者在feature分支修复并提交。然后依次合并到develop，test分支上）
5. release分支：
    + release 为预上线分支，以test分支为基础创建，作为预发布环境(UAT)的部署分支
     （ 当测试环境测试通过后，基于 test 分支最新版本创建一条 release 分支，如果在验证预发布环境过程中若存在bug需要修复，则直接由开发者在feature分支进行修复，修复完毕后，依次合并到develop，test分支上，再由test分支合并到release分支。当预发布环境验证完成达到上线标准之后，合并release分支到master分支，此时master为最新代码，用作上线 ）
6. hotfix 分支：
    + 分支命名: hotfix/ 开头的为修复分支，它的命名规则与 feature 分支类似
      线上出现紧急问题时，需要及时修复，以master分支为基线，创建hotfix分支，修复完成后，需要合并到master，develop，test，release分支上
   
   ###============代码变更仅允许在feature和hotfix分支进行操作，其他分支变更均由分支操作导致变更============================###
 
   
#### 九.项目打包部署+部署
   + mvn clean install (打包)
   + nohup java -jar XXX.jar --spring.profiles.include=test & (部署-启动服务)


#### 十.Swagger接口文档 
   + http://127.0.0.1:8889/doc.html(不同环境，修改ip地址+端口号进行访问)
   
#### 十一.日志管理
   + 日志管理(项目工程logs文件夹下，存放所有服务的日志输出文件.相关配置文件为logback-spring.xml)   