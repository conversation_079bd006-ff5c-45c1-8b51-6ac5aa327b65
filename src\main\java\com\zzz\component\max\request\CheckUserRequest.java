package com.zzz.component.max.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CheckUserRequest {

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String personName;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String docno;


    @ApiModelProperty("交易金额")
    private BigDecimal amount;

    @ApiModelProperty("币种")
    private String currency;
}
