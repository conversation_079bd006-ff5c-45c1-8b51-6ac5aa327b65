package com.zzz.controller.v1;


import com.zzz.dto.CommonInfoDto;
import com.zzz.dto.PurposeDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.CommonInfo;
import com.zzz.response.JSONResult;
import com.zzz.service.ICommonInfoService;
import com.zzz.service.ITbOrderPurposeService;
import com.zzz.util.SpringBeanUtil;
import com.zzz.util.UploadToS3Util;
import com.zzz.util.environment.EnvironmentUtil;
import com.zzz.util.sms.SmsUtil;
import com.zzz.vo.SmsSendVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <p>
 * 通用 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Api(tags = "通用API")
@Slf4j
@RestController
@RequestMapping("/api/v1/common")
public class CommonController {

    @Autowired
    private SmsUtil smsUtil;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Autowired
    private ITbOrderPurposeService tbOrderPurposeService;

    /**
     * 短信发送
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "短信发送")
    @PostMapping("/sms/send")
    public JSONResult smsSend(@RequestBody @Valid SmsSendVo req) {
        if (StringUtils.isBlank(req.getPhone())) {
            return JSONResult.error(CommonErrorEnum.PHOME_IS_NULL);
        }
        if (!Pattern.matches("^1[3-9][0-9]{9}$", req.getPhone())) {
            return JSONResult.error(CommonErrorEnum.PHONE_FORMAT_ERROR);
        }
        if (StringUtils.isBlank(req.getTypeConstant())) {
            return JSONResult.error(CommonErrorEnum.SMS_TYPE_IS_NULL);
        }
        //非正式环境，返回验证码给前端
        String result = smsUtil.sendSms(req.getPhone(), req.getTypeConstant());
        if (EnvironmentUtil.isNonProd()) return JSONResult.ok(result);
        return JSONResult.ok();
    }

    /**
     * 文件上传
     */


    /**
     * 电子购汇申请书
     */
    @ApiOperation(value = "电子购汇申请书")
    @GetMapping("/get_application_book")
    public JSONResult<CommonInfoDto> getApplicationBook() {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.ICE_APPLICATION_P);
        return JSONResult.ok(SpringBeanUtil.copyProperties(list.get(0), CommonInfoDto.class));
    }

    /**
     * 服务条款
     */
    @ApiOperation(value = "服务条款")
    @GetMapping("/get_service")
    public JSONResult<CommonInfoDto> getService() {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.SERVER_CONTENT_P);
        return JSONResult.ok(SpringBeanUtil.copyProperties(list.get(0), CommonInfoDto.class));
    }

    /**
     * 获取支付相关协议列表
     */
    @ApiOperation(value = "获取支付相关协议列表")
    @GetMapping("/agreement-list")
    public JSONResult<List<CommonInfoDto>> queryAgreementList() {
        List<CommonInfoDto> list = iCommonInfoService.queryAgreementList();
        return JSONResult.ok(list);
    }

    /**
     * 获取支付相关协议详情
     */
    @ApiOperation(value = "获取支付相关协议详情")
    @GetMapping("/agreement-detail")
    public JSONResult<CommonInfoDto> getAgreementDetail(@RequestParam("id") Integer id) {
        CommonInfoDto commonInfoDto = iCommonInfoService.getAgreementDetail(id);
        return JSONResult.ok(commonInfoDto);
    }

    /**
     * 隐私声明
     */
    @ApiOperation(value = "隐私声明")
    @GetMapping("/get_stealth")
    public JSONResult<CommonInfoDto> getStealth() {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.STEALTH_CONTENT_P);
        return JSONResult.ok(SpringBeanUtil.copyProperties(list.get(0), CommonInfoDto.class));
    }


    /**
     * 兑换须知
     */
    @ApiOperation(value = "兑换须知")
    @GetMapping("/get_exchange_notice")
    public JSONResult<CommonInfoDto> getExchangeNotice() {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.EXCHANGE_NITICE_P);
        return JSONResult.ok(SpringBeanUtil.copyProperties(list.get(0), CommonInfoDto.class));
    }

    /**
     * 获取自定义广告
     */
    @ApiOperation(value = "获取自定义广告")
    @GetMapping("/get_banner")
    public JSONResult<CommonInfoDto> getBanner() {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.BANNER_P);
        return JSONResult.ok(SpringBeanUtil.copyProperties(list.get(0), CommonInfoDto.class));
    }

    /**
     * 购汇用途列表
     */
    @ApiOperation(value = "购汇用途列表")
    @PostMapping("/get_purpose_list")
    public JSONResult<List<PurposeDto>> getPurposeList() {
        return JSONResult.ok(tbOrderPurposeService.getPurposeList());
    }


    @PostMapping("upload")
    @ApiOperation("文件上传接口")
    public JSONResult abilityPictureUpload(@RequestParam("file") MultipartFile file, HttpServletRequest request) {

        UploadToS3Util.uploadToS3(file, "hongshanshu-test", null);
        String url = null;
        if (StringUtils.isNotEmpty(url)) {
            return JSONResult.ok(url);
        }
        return JSONResult.error("文件上传失败");
    }

}
