package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.GenericTagQueryResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/6/6
 * Time: 15:18
 * 所属模块：
 * 功能说明：实时标签查询
 */
public class GenericTagQueryRequest implements OpenApiRequest<GenericTagQueryResponse>{
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<GenericTagQueryResponse> responseClass() {
        return GenericTagQueryResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "实时标签查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/tag/generic/query";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
