package com.zzz.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 15:40 2024/3/4
 * @ Description： RSA 加密响应结果
 * @ Version:     1.0
 */
@Retention(RetentionPolicy.RUNTIME)//运行时生效
@Target(ElementType.METHOD)//作用在方法上
public @interface RsaResponse {
}
