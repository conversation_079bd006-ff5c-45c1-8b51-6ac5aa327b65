package com.zzz.dto;

import cn.dev33.satoken.stp.SaTokenInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-08-10
 **/
@Data
@Accessors(chain = true)
@ApiModel("手机号登录响应实体")
public class AlipayPhoneDto {


    @ApiModelProperty(value = "用户id", required = true)
    private Integer id;

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "手机号是否验证 0 否 1 是")
    private Integer valid;

    @ApiModelProperty(value = "token信息", required = true)
    private SaTokenInfo tokenInfo;

    public static AlipayPhoneDto create() {
        return new AlipayPhoneDto();
    }
}
