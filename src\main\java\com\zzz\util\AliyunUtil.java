package com.zzz.util;

import cn.hutool.json.JSONUtil;
import com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationRequest;
import com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationResponse;
import com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationResponseBody;
import com.aliyun.teaopenapi.models.Config;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * packageName com.zzz.util
 *
 * <AUTHOR>
 * @version JDK 11
 * @date 2024/6/6
 */
public class AliyunUtil {

        public static ThreeElementsVerificationResponseBody threeElementsVerificationResponse(String accessKeyId, String accessKeySecret, List<String> list) throws Exception {
            List<String> args = new ArrayList<>();
            args.add(0,"v757VmTcuf");
            args.add(1,"18664773823");
            args.add(2,"NORMAL");
            args.add(3,"450902199607252232");
            args.add(4,"庞能华");
            com.aliyun.dytnsapi20200217.Client client =createClient(accessKeyId, accessKeySecret);
            ThreeElementsVerificationRequest request = new ThreeElementsVerificationRequest();
            request.authCode = args.get(0);
            request.inputNumber = args.get(1);
            request.mask = args.get(2);
            request.certCode = args.get(3);
            request.name = args.get(4);
            ThreeElementsVerificationResponse response = client.threeElementsVerification(request);
            System.out.println(response);
            String code = response.body.code;
            if (!com.aliyun.teautil.Common.equalString(code, "200")) {

                System.out.println("错误信息:" + response.body + "");
                return response.body;
            }

            System.out.println("响应结果:" + response.body + "");
            return response.body;
        }


    public static com.aliyun.dytnsapi20200217.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config();
        config.accessKeyId = accessKeyId;
        config.accessKeySecret = accessKeySecret;
        return new com.aliyun.dytnsapi20200217.Client(config);
    }


    public static void main(String[] args) throws Exception {
        ThreeElementsVerificationResponseBody threeElementsVerificationResponseBody = AliyunUtil.threeElementsVerificationResponse("LTAI5tPggriijXjtVrs7H6mK", "******************************", null);
        System.out.println(JSONUtil.toJsonStr(threeElementsVerificationResponseBody));
    }

}
