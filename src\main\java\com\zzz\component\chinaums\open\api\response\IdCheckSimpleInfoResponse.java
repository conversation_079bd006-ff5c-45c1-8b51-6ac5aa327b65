package com.zzz.component.chinaums.open.api.response;

import com.zzz.component.chinaums.open.api.OpenApiResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/2/21
 * Time: 14:49
 * 所属模块：
 * 功能说明：核查平台-身份证验证
 */
public class IdCheckSimpleInfoResponse extends OpenApiResponse {
    /**
     * 公民身份号码核查结果
     */
    private String resultCertifId;

    /**
     * 姓名核查结果
     */
    private String resultName;

    /**
     * base64编码的照片
     */
    private String photo;

    public String getResultCertifId() {
        return resultCertifId;
    }

    public void setResultCertifId(String resultCertifId) {
        this.resultCertifId = resultCertifId;
    }

    public String getResultName() {
        return resultName;
    }

    public void setResultName(String resultName) {
        this.resultName = resultName;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }
}
