spring:
  profiles:
    active: dev
  application:
    name: travelex-plus
server:
  port: 18101
  http-port: 8101
  #开启https，配置跟证书一一对应
  ssl:
    key-store: classpath:d682a075dc2f65a0ca066124f3246728.jks
    key-store-password: Rk8{XWsaf7#AaE6h              #压缩包解压里面会有，下载时自己设置的密码
    key-store-type: JKS
    enabled: true
    # 如果你不想验证客户端证书，可以省略 client-auth 配置，或者设置为 need（仅请求客户端证书，但不强制）
#    client-auth: none