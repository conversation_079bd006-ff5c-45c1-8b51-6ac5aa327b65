package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 零钱包信息
 * <AUTHOR> to 2024/11/13
 */
@Data
@ApiModel("网点零钱包信息实体")
public class CoinBaseDto {

    @ApiModelProperty(value = "唯一ID")
    private Integer id;

    @ApiModelProperty(value = "规格代码")
    private String bizcode;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "币种名称")
    private String curName;

    @ApiModelProperty(value = "规格名称")
    private String name;

    @ApiModelProperty(value = "单价；388元/份")
    private BigDecimal price;

    @ApiModelProperty(value = "面值")
    private BigDecimal parValue;

    @ApiModelProperty(value = "可用状态，0可用，1禁用")
    private Integer state;

    @ApiModelProperty(value = "温馨提示")
    private String remarks;

    @ApiModelProperty(value = "创建时间")
    private String createTime;
}
