package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：微信获取手机号 请求入参
 * @ Version:     1.0
 */
@Data
@ApiModel("获取手机号-微信")
public class WxPhoneVo implements Serializable {

    @ApiModelProperty(name = "code", value = "手机号获取凭证，微信getPhoneNumber回调函数的event.detail.code获取", required = true, example = "4b9553e6e12216558a820e36b76cbcd4c09ad43ca37bf837178f4537244c1924")
    @NotBlank
    private String code;

    @ApiModelProperty(name = "openId", value = "第三方平台开放ID，如：微信授权成功的openId", required = true,example = "13666666666")
    @NotBlank
    private String openId;
}
