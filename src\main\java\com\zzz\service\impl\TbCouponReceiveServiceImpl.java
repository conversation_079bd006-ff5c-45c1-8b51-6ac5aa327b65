package com.zzz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.UserCouponDto;
import com.zzz.dto.UserCouponPageDto;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbCouponReceive;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbCouponReceiveMapper;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbCouponMrgService;
import com.zzz.service.ITbCouponReceiveService;
import com.zzz.util.CusNumberUtil;
import com.zzz.vo.UserCouponPageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠券领取表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class TbCouponReceiveServiceImpl extends ServiceImpl<TbCouponReceiveMapper, TbCouponReceive> implements ITbCouponReceiveService {

    @Autowired
    private TbCouponReceiveMapper tbCouponReceiveMapper;

    @Autowired
    private ITbCouponMrgService tbCouponMrgService;

    @Override
    public UserCouponPageDto getUserCouponPage(UserCouponPageVo req) {
        //1、必传，非空校验
        if(CusNumberUtil.isNumber(req.getPage())){
            throw new ServiceException(CommonErrorEnum.PAGE_CODE_IS_NULL);
        }
        if(CusNumberUtil.isNumber(req.getSize())){
            throw new ServiceException(CommonErrorEnum.PAGE_SIZE_IS_NULL);
        }
        //2、当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        //3、分页查询用户优惠券
        Page<UserCouponDto> page = new Page<>(req.getPage(), req.getSize());
        IPage<UserCouponDto> dataIPage = tbCouponReceiveMapper.getUserCouponPage(page,reqContextUser.getId());
        //4、返回查询结果
        return UserCouponPageDto.builder()
                .totalPage(dataIPage.getPages())
                .totalRecord(dataIPage.getTotal())
                .list(dataIPage.getRecords())
                .build();
    }

    @Override
    public JSONResult doReceive(Integer coupenId) {
        //1、必传，非空校验
        if(CusNumberUtil.isNumber(coupenId)){
            throw new ServiceException(CommonErrorEnum.COUPEN_ID_IS_NULL);
        }
        //2、获取优惠券信息
        TbCouponMrg tbCouponMrg = tbCouponMrgService.getCouponById(coupenId);
        if(null == tbCouponMrg){
            return JSONResult.error(CommonErrorEnum.COUPEN_IS_NULL);
        }
        //3、判断有效期
        Integer expired = tbCouponMrg.getExpired();
        if(expired == 1){
            return JSONResult.error(CommonErrorEnum.COUPEN_IS_EXPIRED);
        }
        //4、判断库存
        String numberLimit = tbCouponMrg.getNumberLimit();
        if(Integer.valueOf(numberLimit) <= 0){
            return JSONResult.error(CommonErrorEnum.COUPEN_STOCK_IS_NULL);
        }
        //5、当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        //判断用户是否已领取
        List<TbCouponReceive> list =
                tbCouponReceiveMapper.selectList(
                        new LambdaQueryWrapper<TbCouponReceive>()
                                .eq(TbCouponReceive::getUserId,reqContextUser.getId())
                                .eq(TbCouponReceive::getCouponId,tbCouponMrg.getId())
                );
        //重复领取
        if(!list.isEmpty()){
            return JSONResult.error(CommonErrorEnum.COUPON_REPEATED_CLAIM);
        }
        //领取优惠券
        TbCouponReceive tbCouponReceive = new TbCouponReceive();
        tbCouponReceive.setUserId(String.valueOf(reqContextUser.getId()));
        tbCouponReceive.setCouponId(coupenId);
        //未使用
        tbCouponReceive.setApplyState(2);
        tbCouponReceive.setCreateTime(LocalDateTime.now());
        int insert = tbCouponReceiveMapper.insert(tbCouponReceive);
        log.info("用户{}({})领取优惠券成：{}",reqContextUser.getId(),reqContextUser.getPhone(),coupenId);
        //优惠券库存-1
        tbCouponMrgService.subCouponCountById(coupenId);
        return JSONResult.ok(insert);
    }

    @Override
    public void unLockById(Integer couponId) {
        //当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        //查询客户领取的优惠券
        TbCouponReceive tbCouponReceive = tbCouponReceiveMapper.selectOne(
                new LambdaQueryWrapper<TbCouponReceive>()
                        .eq(TbCouponReceive::getId,couponId)
                        .eq(TbCouponReceive::getUserId,reqContextUser.getId())
        );
        if(null != tbCouponReceive ){
            //判断优惠券信息
            TbCouponMrg couponById = tbCouponMrgService.getCouponById(tbCouponReceive.getCouponId());
            if(couponById.getExpired() == 1){
                //释放优惠券
                tbCouponReceive.setApplyState(2);
                tbCouponReceive.setUpdateTime(LocalDateTime.now());
                tbCouponReceiveMapper.updateById(tbCouponReceive);
            }
        }
    }

    @Override
    public List<TbCouponReceive> getUserRecCouponList(Integer userId) {
        return tbCouponReceiveMapper.selectList(new LambdaQueryWrapper<TbCouponReceive>().eq(TbCouponReceive::getUserId,userId));
    }

    @Override
    public TbCouponMrg getCouponByRecId(Integer couponId) {
        //根据用户已领取优惠券编号获取优惠券信息
        TbCouponMrg tbCouponMrg = tbCouponReceiveMapper.getCouponByRecId(couponId);
        return tbCouponMrg;
    }

    @Override
    public void useById(Integer couponId) {
        //锁定状态才可以使用
        TbCouponReceive tbCouponReceive = tbCouponReceiveMapper.selectOne(
                new LambdaQueryWrapper<TbCouponReceive>()
                        .eq(TbCouponReceive::getId,couponId)
                        .eq(TbCouponReceive::getApplyState,0)

        );
        if(null != tbCouponReceive){
            //使用
            tbCouponReceive.setApplyState(1);
            tbCouponReceive.setUseTime(LocalDateTime.now());
            tbCouponReceive.setUpdateTime(LocalDateTime.now());
            tbCouponReceiveMapper.updateById(tbCouponReceive);
        }
    }

    @Override
    public void lockById(Integer couponId) {
        //未使用 状态才可以 锁定
        TbCouponReceive tbCouponReceive = tbCouponReceiveMapper.selectOne(
                new LambdaQueryWrapper<TbCouponReceive>()
                        .eq(TbCouponReceive::getId,couponId)
                        .eq(TbCouponReceive::getApplyState,2)

        );
        if(null != tbCouponReceive){
            //锁定
            tbCouponReceive.setApplyState(0);
            tbCouponReceive.setUpdateTime(LocalDateTime.now());
            tbCouponReceiveMapper.updateById(tbCouponReceive);
        }
    }
}
