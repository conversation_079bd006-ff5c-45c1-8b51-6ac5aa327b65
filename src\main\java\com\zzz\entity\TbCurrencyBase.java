package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;

    import java.math.BigDecimal;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 币种基础信息表
    * </p>
*
* <AUTHOR>
* @since 2024-07-13
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_currency_base")
    @ApiModel(value="TbCurrencyBase对象", description="币种基础信息表")
    public class TbCurrencyBase implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "主键；记录ID")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "币种编码")
    private String curCode;

            @ApiModelProperty(value = "币种名称")
    private String curName;

            @ApiModelProperty(value = "最小金额单位")
    private Integer minUnit;

    @ApiModelProperty(value = "最低购买金额；起订量")
    private Integer minAmt;

            @ApiModelProperty(value = "汇率单位，譬如：USD为1、JAP为100")
    private Integer rateUnit;

            @ApiModelProperty(value = "币种对应图标URL地址")
    private String picUrl;

            @ApiModelProperty(value = "状态；1启用、0停用")
    private Integer status;

            @ApiModelProperty(value = "排序编码")
    private Integer sortno;

    @ApiModelProperty(value = "1代表热门币种")
    private Integer popular;

    @ApiModelProperty(value = "购汇汇率")
    private BigDecimal sellRate;


            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;


}
