package com.zzz.service;

import com.zzz.dto.CouponPageDto;
import com.zzz.entity.TbCouponMrg;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.vo.CouponPageVo;

/**
 * <p>
 * 平台优惠券 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbCouponMrgService extends IService<TbCouponMrg> {

    CouponPageDto getCouponPage(CouponPageVo couponPageVo);

    TbCouponMrg getCouponById(Integer coupenId);

    void subCouponCountById(Integer coupenId);

}
