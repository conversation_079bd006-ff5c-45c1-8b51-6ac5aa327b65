package com.zzz.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 通用异常码
 * Author: <a href="https://github.com/zongzibinbin">abin</a>
 * Date: 2023-03-26
 */
@AllArgsConstructor
@Getter
public enum CommonErrorEnum implements ErrorEnum {

    SYSTEM_ERROR(-1, "系统出小差了，请稍后再试哦~~"),
    PARAM_VALID(-2, "参数校验失败{0}"),
    FREQUENCY_LIMIT(-3, "请求太频繁了，请稍后再试哦~~"),
    LOCK_LIMIT(-4, "请求太频繁了，请稍后再试哦~~"),
    MAX_COMMON_ERROR(-5, "%s"),
    VALIDATION_EXCEPTION(-6,"统一验证框架的错误提示"),


    //用户信息
    USER_NO_LOGIN_ERROR(100000, "请先登录~"),
    NON_VALID_PHONE(100001, "请先验证手机号"),
    GET_WX_AUTH_ERROR(100002, "获取微信用户信息失败"),
    GET_USER_PHONE_ERROR(100003, "获取微信用户手机号失败"),
    AUTH_GET_TOKEN_ERROR(100004, "获取微信accessToken失败"),
    GET_USER_PHONENUMBER_ERROR(100005, "获取微信用户手机号失败"),
    USER_IS_NULL(100006, "用户信息不存在"),
    REALNAME_IS_NULL(100007, "无实名认证信息，请先进行实名认证"),
    REALNAME_IS_EXIST(100008, "已实名认证过~"),
    USER_LOGIN_ERROR(100009, "用户信息有误~"),
    REALNAME_ERROR(100010, "信息有误，实名认证失败~"),
    WX_AUTH_CODE_ERROR(100011, "登录授权code不能为空"),
    WX_AUTH_PHONE_CODE_ERROR(100012, "微信手机号授权码code不能为空"),
    WX_OPENID_ERROR(100013, "微信openId不能为空"),


    ALIPAY_AUTH_PHONE_CODE_ERROR(100014, "支付宝手机号授权码token不能为空"),
    ALIPAY_OPENID_ERROR(100015, "支付宝userid不能为空"),



    //优惠券信息
    COUPEN_IS_NULL(200001, "优惠券信息不存在"),
    COUPEN_IS_EXPIRED(200002, "优惠券已过期"),
    COUPEN_STOCK_IS_NULL(200003, "优惠券已被领完"),
    COUPON_REPEATED_CLAIM(200004, "您已领取过此优惠券"),

    //订单
    ORDER_IS_NULL(300001, "订单不存在"),
    ORDER_STATUS_ERROR(300002, "此订单状态不允许取消"),

    ORDER_USERNAME_IS_NULL(300003, "提货人姓名不能为空"),
    ORDER_PHONE_IS_NULL(300004, "提货人手机不能为空"),
    ORDER_IDCARD_IS_NULL(300005, "提货人身份证不能为空"),
    ORDER_BRANCH_IS_NULL(300006, "提货网点不能为空"),
    ORDER_PUTPOSE_IS_NULL(300007, "购汇用途不能为空"),
    ORDER_AMOUNT_IS_NULL(300008, "订单总金额不能为空"),
    ORDER_TIME_IS_NULL(300009, "提取时间不能为空"),
    ORDERNO_IS_NULL(300010, "订单编号不能为空"),
    REFUND_STATUS_ERROR(300011, "该订单不支持退款"),
    REFUND_STATUS2_ERROR(300012, "请先取消订单再申请退款"),
    ORDER_STATUS_PAY_ERROR(300013, "非待支付订单"),
    ORDER_CURRENCY_IS_NULL(300014, "币种信息必选"),
    ORDER_PUTPOSE_OTHER_IS_NULL(300015, "汇率用途为其他，自定义内容必填"),
    CURRENCY_IS_NULL(300016, "币种信息不存在"),
    WALLET_IS_NULL(300017, "零钱包信息不存在"),
    ORDER_PAY_AMOUNT_IS_NULL(300018, "支付金额不能为空"),
    ORDER_PAY_TYPE_IS_NULL(300019, "支付方式不能为空"),
    VERIFICATION_AMOUNT_ERROR(300020, "价格校验失败，请重新下单"),
    REPEAT_SUBMIT_ORDER(300021, "请勿频繁操作，稍后再试~"),
    ORDER_CONFIRM_TOKEN_EQUAL_FAIL(300022,"订单令牌缺少"),
    OPS_REPEAT(300023,"重复操作"),
    CURRENCY_BASE_IS_NULL(300017, "币种基础信息不存在"),
    BAMBOO_CUR_FACEVALUE_ERROR(300018, "币种面值错误，请重新选择"),
    BAMBOO_CUR_STOCK_ERROR(300019, "币种库存不足"),
    BAMBOO_LOCK_STOCK_FAIL(300020, "锁定库存失败"),
    MAX_COIN_STOCK_ERROR(300021, "零钱包库存不足"),
    BAMBOO_SETTLEPRICE_IS_NULL(300022, "商品牌价信息没有设置"),
    BAMBOO_COMMON_ERROR(300023, "提示：%s"),
    BRANCH_NOT_ONLINE_FLAG(300024, "网点不接收在线订单"),
    MAX_AMOUNT_ERROR(300025, "超过币种兑换最大金额"),
    MAX_SITE_COIN_BUY_NUM_ERROR(300026, "超过网点零钱包最大可购买数量，最大可购买数量：%s"),


    //公用信息
    COMMON_IS_NULL(900001, "配置信息不存在"),
    LIST_IS_NULL(900002, "列表数据为空"),
    BRANCH_IS_NULL(900003, "网点不存在"),
    PURPOSE_IS_NULL(900004, "用途不能为空"),
    PURPOSE_OTHER_IS_NULL(900005, "用途类型为其他，自定义内容必填"),
    REALNAME_NOT_NULL(900006,"真实姓名不能为空"),
    IDCARD_NOT_NULL(900007,"身份证号码不能为空"),
    PHOME_IS_NULL(900008,"手机号码不能为空"),
    PHONE_FORMAT_ERROR(900008,"手机号码格式错误"),
    SMS_TYPE_IS_NULL(900009,"短信发送类型不能为空"),

    BRANCH_CLOSE_URGENT(900010,"网点已关闭加急费用"),
    URGENT_FEE_IS_NULL(900011,"此时间无加急费用"),
    COIN_DATA_IS_NULL(900012,"当前币种无零钱包数据"),
    PHOME_CODE_IS_NULL(900013, "手机验证码不能为空"),
    PAGE_SIZE_IS_NULL(900014, "页数不能为空"),
    PAGE_CODE_IS_NULL(900015, "页码不能为空"),
    LONGITUDE_IS_NULL(900016, "经度不能为空"),
    LATITUDE_IS_NULL(900017, "维度不能为空"),
    BRANCH_ID_IS_NULL(900018, "网点ID不能为空"),
    BAMBOO_BRANCH_NOT_EXTRACT_DATE(900019, "上游网点无可提取日期"),
    CURRENCY_ID_IS_NULL(900020, "币种ID不能为空"),
    CUR_CODE_IS_NULL(900021, "币种代码不能为空"),
    COUPEN_ID_IS_NULL(900022, "优惠券ID不能为空"),
    NON_BUSINESS_HOURS_ERROR(900023, "非营业时间段，平台暂不支持此操作"),
    EXTRACT_DATE_ERROR(900024, "提取日期错误"),
    SAME_TODAY_IS_NOT_EXTRACT_DATE(900025, "该时间段无法选择当天为提取日期"),



    ;
    private final Integer code;
    private final String msg;

    @Override
    public Integer getErrorCode() {
        return this.code;
    }

    @Override
    public String getErrorMsg() {
        return this.msg;
    }
}
