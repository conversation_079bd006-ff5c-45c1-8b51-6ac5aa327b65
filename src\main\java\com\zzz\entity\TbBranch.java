package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 网点
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_branch")
    @ApiModel(value="TbBranch对象", description="网点")
    public class TbBranch implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "网点编码")
    private String sitecode;

            @ApiModelProperty(value = "网点名称")
    private String sitename;

            @ApiModelProperty(value = "网点所属机构编码")
    private String corpcode;

            @ApiModelProperty(value = "网点地址")
    private String siteaddr;

            @ApiModelProperty(value = "网点负责人")
    private String sitemanager;

            @ApiModelProperty(value = "网点电话")
    private String sitetel;

            @ApiModelProperty(value = "是否接收在线订单；1接收、0不接收")
    private Integer onlineFlag;

            @ApiModelProperty(value = "外管接口-网点金融机构标识码")
    private String wsOrgcode;

            @ApiModelProperty(value = "网点接口开启状态；1开启、0关闭")
    private Integer wsFlag;

            @ApiModelProperty(value = "经度")
    private Double longitude;

            @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "网点接口开启状态；1开启、0关闭")
    private Integer urgentStatus;

    @ApiModelProperty(value = "当前时间近几天按加急处理；3或7")
    private Integer urgentFutureDay;


            @ApiModelProperty(value = "营业开始时间")
    private String startTime;

            @ApiModelProperty(value = "营业结束时间")
    private String endTime;


    @ApiModelProperty(value = "币种倍数，网点币种基础汇率*此基数=币种汇率")
    private Integer currencyPercent;

    @ApiModelProperty(value = "第三方外部ID")
    private String outId;

    @ApiModelProperty(value = "网点所属，0 max ,1 bamboo")
    private String type;

    @ApiModelProperty(value = "删除状态。1删除，0正常 ")
    private String isDel;

}
