<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.BbProductMapper">

    <select id="getProductByCid" resultType="com.zzz.entity.BbProduct">
        select * from bb_product pdt LEFT JOIN tbcurrency cur on pdt.id = cur.out_cur_code where cur.id = #{id}
    </select>
</mapper>
