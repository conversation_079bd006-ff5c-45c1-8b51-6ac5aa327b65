package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.dto.TagsDto;
import com.zzz.emuns.DeleteStatusEnum;
import com.zzz.entity.TbTags;
import com.zzz.mapper.TbTagsMapper;
import com.zzz.service.ITbTagsService;
import com.zzz.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class TbTagsServiceImpl extends ServiceImpl<TbTagsMapper, TbTags> implements ITbTagsService {

    @Autowired
    private TbTagsMapper tbTagsMapper;


    @Override
    public List<TagsDto> getTagsList() {
        List<TbTags> list = tbTagsMapper.selectList(Wrappers.<TbTags>lambdaQuery()
                .eq(TbTags::getIsDel, DeleteStatusEnum.NORMAL.value));

        return SpringBeanUtil.copyProperties(list,TagsDto.class);
    }

}
