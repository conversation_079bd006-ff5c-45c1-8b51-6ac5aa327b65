package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.dto.PurposeDto;
import com.zzz.entity.TbOrderPurpose;
import com.zzz.mapper.TbOrderPurposeMapper;
import com.zzz.service.ITbOrderPurposeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用途 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class TbOrderPurposeServiceImpl extends ServiceImpl<TbOrderPurposeMapper, TbOrderPurpose> implements ITbOrderPurposeService {

    @Autowired
    private TbOrderPurposeMapper tbOrderPurposeMapper;

    @Override
    public List<PurposeDto> getPurposeList() {
        return SpringBeanUtil.copyProperties(tbOrderPurposeMapper.selectList(null),PurposeDto.class);
    }

    @Override
    public TbOrderPurpose getPurposeById(Integer purpose) {
        return tbOrderPurposeMapper.selectOne(
                new LambdaQueryWrapper<TbOrderPurpose>()
                        .eq(TbOrderPurpose::getId,purpose)
        );
    }
}
