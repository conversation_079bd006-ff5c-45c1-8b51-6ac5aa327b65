package com.zzz.component.chinaums.open.api.internal.util.http;

import com.zzz.component.chinaums.open.api.OpenApiContext;

/**
 * Created by ZHANGWEI on 2016/12/4.
 */
public interface IHttpTransport {
    public abstract String doPost(boolean isDebug,String url, String token, String request) throws Throwable;
    public abstract String doPost(OpenApiContext context, String request) throws Throwable;
}
