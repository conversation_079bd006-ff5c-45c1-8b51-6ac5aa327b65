package com.zzz.component.chinaums.open.api.internal.util.converter;

import com.zzz.component.chinaums.open.api.OpenApiException;
import com.zzz.component.chinaums.open.api.OpenApiResponse;

import java.text.ParseException;

/**
 * Created by ZHANGWEI on 2016/12/2.
 */
public abstract interface Converter {
    public abstract <T extends OpenApiResponse> T toResponse(String paramString, Class<T> paramClass)
            throws OpenApiException, IllegalAccessException, InstantiationException, ParseException, ClassNotFoundException;
}
