package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.FacerecognitionParamResultQueryResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/6/5
 * Time: 14:34
 * 所属模块：
 * 功能说明：人脸识别结果查询
 */
public class FacerecognitionParamResultQueryRequest implements OpenApiRequest<FacerecognitionParamResultQueryResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<FacerecognitionParamResultQueryResponse> responseClass() {
        return FacerecognitionParamResultQueryResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "人脸识别结果查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/facerecognition/result/query";
    }

    public boolean needToken() {
        return true;
    }
    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
