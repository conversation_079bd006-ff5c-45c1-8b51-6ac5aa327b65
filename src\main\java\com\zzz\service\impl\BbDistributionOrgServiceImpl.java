package com.zzz.service.impl;

import com.zzz.component.bamboo.response.DistributionOrgInfoResponse;
import com.zzz.component.bamboo.response.OrgBranchInfo;
import com.zzz.dto.BranchOrgDto;
import com.zzz.entity.BbDistributionOrg;
import com.zzz.entity.BbOrgBranch;
import com.zzz.mapper.BbDistributionOrgMapper;
import com.zzz.service.IBbDistributionOrgService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.service.IBbOrgBranchService;
import com.zzz.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 客户表（Bamboo） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class BbDistributionOrgServiceImpl extends ServiceImpl<BbDistributionOrgMapper, BbDistributionOrg> implements IBbDistributionOrgService {

    @Autowired
    private BbDistributionOrgMapper bbDistributionOrgMapper;

    @Autowired
    private IBbOrgBranchService bbOrgBranchService;

    @Override
    @Transactional
    public Integer addDistribution(DistributionOrgInfoResponse listRespons) {
        BbDistributionOrg bbDistributionOrg = SpringBeanUtil.copyProperties(listRespons,BbDistributionOrg.class);
        bbDistributionOrgMapper.insert(bbDistributionOrg);
        Integer id = bbDistributionOrg.getId();
        List<OrgBranchInfo> branchList = listRespons.getBranchList();
        branchList.forEach(item->{
            BbOrgBranch orgBranchInfo = SpringBeanUtil.copyProperties(item, BbOrgBranch.class);
            orgBranchInfo.setCusId(id);
            bbOrgBranchService.addOrgBranch(orgBranchInfo,listRespons.getStatus());
        });
        return id;
    }

    @Override
    public List<BbDistributionOrg> getDistributionOrgList() {
        return bbDistributionOrgMapper.selectList(null);
    }

    @Override
    public BbDistributionOrg getDistributionByPid(String outId) {
        //关联查询
        return bbDistributionOrgMapper.getDistributionByPid(outId);
    }

    /**
     *  根据网点编号 获取bamboo 客户网点基础信息
     * @param branchId
     * @return
     */
    @Override
    public BranchOrgDto getDistributionByBId(Integer branchId) {
        return bbDistributionOrgMapper.getDistributionByBId(branchId);
    }
}
