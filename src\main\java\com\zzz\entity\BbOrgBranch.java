package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 客户网点表（Bamboo）
    * </p>
*
* <AUTHOR>
* @since 2024-07-12
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("bb_org_branch")
    @ApiModel(value="BbOrgBranch对象", description="客户网点表（Bamboo）")
    public class BbOrgBranch implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer cusId;

    private String branchCode;

    private String branchName;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String regionCode;

    private String regionName;

    private String detailAddress;

    private String contactPerson;

    private String contactEmail;

    private String contactTelephone;

    private String remark;


}
