package com.zzz.component.bamboo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zzz.component.HttpUtils;
import com.zzz.component.bamboo.request.*;
import com.zzz.component.bamboo.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *  接口集合
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Slf4j
@Component
public class BamBooAPI {

    @Resource
    private ApiUrlConst apiUrlConst;

    //创建订单
    public  OrderResponse createOrder(OrderRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getCreateOrderUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        return OrderResponse.builder().orderCode(responseData.getData()).build();
    }
    //取消订单
    public  ResponseData cancelOrder(CancelOrderRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getCancelOrderUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return responseData;
        }
        return responseData;
    }

    //查询订单详情
    public  OrderResultResponse queryOrder(QueryOrderRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getOrderDetailUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        return JSONUtil.toBean(responseData.getData(),OrderResultResponse.class);
    }

    //查询库存
    public  List<StockResultResponse> queryStockList(QueryStockRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getQueryStockListUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        String data = responseData.getData();
        String listObj = JSONObject.parseObject(data).getString("listObj");
        JSONArray array = JSONUtil.parseArray(listObj);
        return JSONUtil.toList(array, StockResultResponse.class);
    }

    //冻结
    //{"code":"500","message":"当前billCode已生成冻结流水: TJL1810218023920136192 ,mpcode :THB500,storeCode :TraveleFlagshipStore ,warehouseCodeMW001","success":false}
    public  ResponseData freezeStock(List<FreezeStockRequest> request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getFreezeStockUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        return responseData;
    }

    //解冻库存
    public  ResponseData unFreezeStock(List<FreezeStockRequest> request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getUnfreezeStockUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        return responseData;
    }

    //查询商品列表
    public  List<ProductResultResponse> queryProductList(QueryProductPageRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getQueryProductListUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        String data = responseData.getData();
        String listObj = JSONObject.parseObject(data).getString("listObj");
        JSONArray array = JSONUtil.parseArray(listObj);
        return JSONUtil.toList(array, ProductResultResponse.class);
    }

    //查询客户列表
    public  List<DistributionOrgInfoResponse> queryCustomerList(QueryCustomerPageRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getQueryCustomerListUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        String data = responseData.getData();
        String listObj = JSONObject.parseObject(data).getString("listObj");
        JSONArray array = JSONUtil.parseArray(listObj);
        return JSONUtil.toList(array, DistributionOrgInfoResponse.class);
    }

    //查询客户结算牌价列表
    public  List<CustomerSettlePriceResponse> querySettilePrice(SettilePriceRequest request){
        ResponseData responseData = HttpUtils.doPost(apiUrlConst.getQuerySettlePriceListUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        String data = responseData.getData();
        JSONArray array = JSONUtil.parseArray(data);
        return JSONUtil.toList(array, CustomerSettlePriceResponse.class);
    }


    //查询可配送日期列表
    public  ResponseData queryDeliveryDateList(DeliveryDateRequest request){
        ResponseData responseData = HttpUtils.doGet(apiUrlConst.getQueryDeliveryDateListUrl(), JSONUtil.toJsonStr(request));
        if(!responseData.getCode().equals("0")){
            return null;
        }
        return responseData;
    }

    public  void main(String[] args) {
        //1.查询客户
//        QueryCustomerPageRequest queryCustomerPageRequest = new QueryCustomerPageRequest();
//        queryCustomerPageRequest.setPage(1);
//        queryCustomerPageRequest.setLimit(100);
////        queryCustomerPageRequest.setOrgName("通济隆外币兑换（中国）有限公司");
//        List<DistributionOrgInfoResponse> distributionOrgInfoResponses = queryCustomerList(queryCustomerPageRequest);
//        log.info("查询客户结果:{}",JSONObject.toJSONString(distributionOrgInfoResponses));
////        [{"cancelOrderFee":1.000000,"currencyCode":"CNY","deliveryCycleDays":7,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"TVXCNRT-CQ","orgName":"通济隆外币兑换（中国）有限公司重庆分公司","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"CQVAULT001"},{"cancelOrderFee":0.000000,"currencyCode":"USD","deliveryCycleDays":5,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"BOCSH","orgName":"中国银行上海分行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":2,"warehouseCode":"W001"},{"cancelOrderFee":0.000000,"currencyCode":"USD","deliveryCycleDays":3,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"CITICCQ","orgName":"中信银行重庆分行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"CQVAULT001"},{"cancelOrderFee":0.000000,"currencyCode":"USD","deliveryCycleDays":3,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"CMBSZ","orgName":"招商银行深圳分行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"TEST002"},{"cancelOrderFee":1.000000,"currencyCode":"USD","deliveryCycleDays":7,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"CMBSH","orgName":"招商银行上海分行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"W001"},{"cancelOrderFee":1.000000,"currencyCode":"USD","deliveryCycleDays":3,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"BOBSH","orgName":"北京银行上海支行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"W001"},{"cancelOrderFee":0.000000,"currencyCode":"HKD","deliveryCycleDays":5,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"CITICBANKSH001","orgName":"中信银行上海分行","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"W001"},{"cancelOrderFee":0.000000,"currencyCode":"USD","deliveryCycleDays":7,"enterpriseName":"","merchantName":"通济隆外币兑换（中国）有限公司","orgCode":"ZGYHSH001","orgName":"中国银行上海","registeredCityName":"","registeredProvinceName":"","registeredRegionName":"","remark":"","status":1,"warehouseCode":"W001"}]

//          2.查询商品
//        QueryProductPageRequest queryProductPageRequest = new QueryProductPageRequest();
//        queryProductPageRequest.setPage(1);
//        queryProductPageRequest.setLimit(100);
////        queryProductPageRequest.setCurrencyCode("USD");
//        queryProductPageRequest.setQuerySettlePrice(1);
//        queryProductPageRequest.setCustomerCode("ZGYHSH001");
//        queryProductPageRequest.setCodes(Arrays.asList("USD100"));
////        queryProductPageRequest.setOrgCode("TVXCNRT-CQ");
////        queryProductPageRequest.setCurrencyCode("THB");
////        queryProductPageRequest.setCurrencyCode("THB");
//        List<ProductResultResponse> list = queryProductList(queryProductPageRequest);
//        log.info("查询商品结果:{}",JSONObject.toJSONString(list));

////        3.查询配送时间
//        DeliveryDateRequest request = new DeliveryDateRequest();
//        request.setCustomerCode("BOCSH");
//        ResponseData responseData = queryDeliveryDateList(request);
//        log.info("查询配送时间结果:{}",JSONObject.toJSONString(responseData));
//        //{"code":"0","data":"[\"2024-07-08 00:00:00\",\"2024-07-09 00:00:00\",\"2024-07-10 00:00:00\",\"2024-07-11 00:00:00\",\"2024-07-12 00:00:00\"]","message":"成功"}
////10:52:41.233 [main] INFO com.zzz.component.bamboo.BamBooAPI - 查询配送时间结果:{"code":"0","data":"[\"2024-07-08 00:00:00\",\"2024-07-09 00:00:00\",\"2024-07-10 00:00:00\",\"2024-07-11 00:00:00\",\"2024-07-12 00:00:00\"]","message":"成功"}
//        String[] split = responseData.getData().replace("[", "").replace("[", "").split(",");
//        String s = split[0].replaceAll("\"", "");
//        System.out.println(s );
//        System.out.println(s .substring(0,s.length()-9));
//        System.out.println(LocalDate.parse(s.substring(0,s.length()-9)));

////        4.查询库存
//        QueryStockRequest queryStockRequest = new QueryStockRequest();
//        queryStockRequest.setPage(1);
//        queryStockRequest.setLimit(10);
//        queryStockRequest.setCustomerCode("BOCSH");
//        queryStockRequest.setWarehouseCode("MW001");
//        queryStockRequest.setCodes(Arrays.asList("USD100"));
////        queryStockRequest.setWarehouseCode("MW001");
//        List<StockResultResponse> stockResultResponses = queryStockList(queryStockRequest);
//        log.info("查询库存结果:{}",JSONObject.toJSONString(stockResultResponses));
//////        查询库存结果:[{"actualVirtualAvailableStockNum":0.0000,"chineseName":"泰铢500","code":"THB500","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"CQVAULT001","warehouseName":"重庆中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"泰铢500","code":"THB500","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"TEST002","warehouseName":"深圳中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"泰铢500","code":"THB500","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"TEST001","warehouseName":"作废仓"},{"actualVirtualAvailableStockNum":2098.0000,"chineseName":"泰铢500","code":"THB500","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":2100,"warehouseCode":"W001","warehouseName":"上海中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"越南盾100000","code":"VND100000","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"CQVAULT001","warehouseName":"重庆中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"越南盾100000","code":"VND100000","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"TEST002","warehouseName":"深圳中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"越南盾100000","code":"VND100000","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":0,"warehouseCode":"TEST001","warehouseName":"作废仓"},{"actualVirtualAvailableStockNum":978.0000,"chineseName":"越南盾100000","code":"VND100000","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":1000,"warehouseCode":"W001","warehouseName":"上海中心仓"},{"actualVirtualAvailableStockNum":19.0000,"chineseName":"阿联酋迪拉姆100","code":"AED100","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":20,"warehouseCode":"TEST002","warehouseName":"深圳中心仓"},{"actualVirtualAvailableStockNum":0.0000,"chineseName":"加拿大元100","code":"CAD100","merchantCode":"Travelex","merchantName":"通济隆外币兑换（中国）有限公司","virtualStockNum":2,"warehouseCode":"TEST002","warehouseName":"深圳中心仓"}]

        //5.冻结库存
//        List<FreezeStockRequest> requestList = new ArrayList<>();
//        FreezeStockRequest freezeStockRequest = new FreezeStockRequest();
//        freezeStockRequest.setBillCode("TJL2024080900000001");
//        freezeStockRequest.setMpCode("PHP1000");
//        freezeStockRequest.setStoreCode("TraveleFlagshipStore");
//        freezeStockRequest.setStockNum(20);
//        freezeStockRequest.setWarehouseCode("SZCPR01");
//        requestList.add(freezeStockRequest);
//        ResponseData responseData = freezeStock(requestList);
//        log.info("查询库存结果:{}",JSONObject.toJSONString(responseData));

//        //6.创建订单CITICBANKSH001
//        OrderRequest orderRequest = new OrderRequest();
//        orderRequest.setOutOrderCode("*************");
//        orderRequest.setMerchantName("通济隆外币兑换（中国）有限公司");
//        orderRequest.setMerchantCode("Travelex");
//        orderRequest.setStoreName("通济隆外币现钞批发旗舰店");
//        orderRequest.setStoreCode("TraveleFlagshipStore");
//        orderRequest.setCustomerName("招商银行深圳分行");
//        orderRequest.setCustomerCode("CMBSZ");
//        orderRequest.setOrderCreateTime(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"));
//        orderRequest.setBranchCode("CMBSZFT001");
//        orderRequest.setBranchName("福田支行");
//        orderRequest.setExpectDeliverDate("2024-08-09");
//        orderRequest.setOrderDeliveryMethodId("24");
//
//        //商品信息
//        List<OrderItem> itemList = new ArrayList<>();
//
//        OrderItem orderItem = new OrderItem();
//        orderItem.setProductCname("菲律宾比索1000");
//        orderItem.setCode("PHP1000");
//        orderItem.setProductItemNum(new BigDecimal("20"));
//        orderItem.setCurrencyCode("PHP");
//        orderItem.setCurrencyFaceValue(new BigDecimal("1000"));
//        orderItem.setCurrencyFaceAmount(new BigDecimal("1000"));
//        orderItem.setWarehouseCode("SZCPR01");
//        orderItem.setWarehouseName("通济隆上海拟合仓");
//        orderItem.setBillCode("TJL2024080900000001");
//
////        OrderItem orderItem = new OrderItem();
////        orderItem.setProductCname("越南盾100000");
////        orderItem.setCode("VND100000");
////        orderItem.setProductItemNum(new BigDecimal("10"));
////        orderItem.setCurrencyCode("PEN");
////        orderItem.setCurrencyFaceValue(new BigDecimal("100000"));
////        orderItem.setCurrencyFaceAmount(new BigDecimal("100000"));
////        orderItem.setWarehouseCode("MW001");
////        orderItem.setWarehouseName("通济隆上海拟合仓");
//
//        itemList.add(orderItem);
//        orderRequest.setItemList(itemList);
//        OrderResponse order = createOrder(orderRequest);
//        log.info("创建订单结果:{}",JSONObject.toJSONString(order));
////        创建订单结果:{"orderCode":"{\"rollBackMark\":{\"5\":\"调用库存服务冻结虚拟库存成功,如果下单失败，需要回滚\"},\"orderPaymentStatus\":3,\"orderTypeMap\":{\"W0CITIC24070773925\":101},\"orderStatus\":1050,\"orderCode\":\"W0CITIC24070773925\",\"orderWebStatus\":2,\"orderCreateTimeMap\":{\"W0CITIC24070773925\":\"2024-07-07 16:01:23\"}}"}
//////        创建订单结果:{"orderCode":"{\"rollBackMark\":{\"5\":\"调用库存服务冻结虚拟库存成功,如果下单失败，需要回滚\"},\"orderPaymentStatus\":3,\"orderTypeMap\":{\"W0CITIC24070768010\":101},\"orderStatus\":1050,\"orderCode\":\"W0CITIC24070768010\",\"orderWebStatus\":2,\"orderCreateTimeMap\":{\"W0CITIC24070768010\":\"2024-07-07 16:03:23\"}}"}

        //7.取消订单
        CancelOrderRequest request = new CancelOrderRequest();
        request.setOrderCode("W0CITIC24083045842");
        request.setOrderCsCancelReason("不需要了");
        request.setOrderCancelDate(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"));
        request.setOrderCanceOperate("测试取消，解锁库存");
        ResponseData responseData = cancelOrder(request);
        log.info("查询库存结果:{}",JSONObject.toJSONString(responseData));

//        //8.解冻库存
//        List<FreezeStockRequest> requestList = new ArrayList<>();
//        FreezeStockRequest freezeStockRequest = new FreezeStockRequest();
//        freezeStockRequest.setBillCode("TJL2024071800000014");
//        freezeStockRequest.setMpCode("PHP1000");
//        freezeStockRequest.setStoreCode("TraveleFlagshipStore");
//        freezeStockRequest.setStockNum(20);
//        freezeStockRequest.setWarehouseCode("SZCPR01");
//        requestList.add(freezeStockRequest);
//        ResponseData responseData = unFreezeStock(requestList);
//        log.info("查询库存结果:{}",JSONObject.toJSONString(responseData));

//        //查询订单详情 1829140885141737472 W0ZGYHS24082972071
//        QueryOrderRequest queryOrderRequest = new QueryOrderRequest();
//        queryOrderRequest.setOrderCode("W0CITIC24083045842");
//        OrderResultResponse orderResultResponse = queryOrder(queryOrderRequest);
//        log.info("查询库存结果:{}",JSONObject.toJSONString(orderResultResponse));

//        //查询客户牌价  无权限
//        SettilePriceRequest settilePriceRequest = new SettilePriceRequest();
//        settilePriceRequest.setCustomerCode("BOCSH");
//        List<CustomerSettlePriceResponse> list = querySettilePrice(settilePriceRequest);
//        log.info("查询库存结果:{}",JSONObject.toJSONString(list));
    }

}
