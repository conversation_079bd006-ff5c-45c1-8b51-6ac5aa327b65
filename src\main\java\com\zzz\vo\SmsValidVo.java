package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：发送短信统一 请求入参
 * @ Version:     1.0
 */
@Data
@ApiModel("验证手机号（首次登录）参数")
public class SmsValidVo implements Serializable {

    @ApiModelProperty(name = "phone", value = "手机号", required = true,example = "13666666666")
    @NotBlank
    private String phone;

    @ApiModelProperty(name = "code", value = "验证码（暂固定：654321）", required = true,example = "654321")
    @NotBlank
    private String code;

    @ApiModelProperty(name = "typeConstant", value = "短信类型（前端暂时自己区分，如：code）", required = true,example = "code")
    @NotBlank
    private String typeConstant;

}
