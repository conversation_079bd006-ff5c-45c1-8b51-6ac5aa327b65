package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class OrderResultResponse {

    private Integer orderStatus;
    private String orderCode;
    private String outOrderCode;
    private String merchantName;
    private String merchantCode;
    private String sysSource;
    private String storeName;
    private String storeCode;
    private String customerName;
    private String customerCode;
    private String currency;
    private String goodReceiverName;
    private String goodReceiverProvince;
    private String goodReceiverProvinceCode;
    private String goodReceiverCity;
    private String goodReceiverCityCode;
    private String goodReceiverArea;
    private String goodReceiverAreaCode;
    private String goodReceiverAddress;
    private String goodReceiverMobile;
    private BigDecimal productAmount;
    private BigDecimal orderAmount;
    private String createUsername;
    private String orderCreateTime;
    private String branchCode;
    private String branchName;
    private String expectDeliverDate;
    private String orderDeliveryMethodIdStr;
    private String pickupAddressName;
    private String pickupAddress;
    private String pickupUserName;
    private String pickupUserMobile;
    private String orderCanceOperate;
    private String orderCsCancelReason;
    private String orderCancelDateStr;
    private BigDecimal cancelFee;
    private List<OrderItemResult> itemList;

}
