package com.zzz.util;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Map;
import java.util.TreeMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.SneakyThrows;

public class UmsPayUtil {
	
	
    /**
     * 生成符合UMS规则的商户订单号: {来源代码(4位)}{39GGyyyyMMddHHmmssSSS(17位)}{7位随机数}
     * @param sourceCode 4位来源代码
     * @return 生成的商户订单号
     */
    public static String generateMerOrderId(String sourceCode,int length) {
        String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        String random = RandomUtil.randomNumbers(length);
        return sourceCode + timestamp + random;
    }

    /**
     * 生成符合UMS规则的商户订单号: {来源代码(4位)}{yyyyMMddHHmmssSSS(17位)}{7位随机数}
     * @param sourceCode 4位来源代码
     * @return 生成的商户订单号
     */
    public static String generateMerOrderId(String sourceCode) {
        String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        String random = RandomUtil.randomNumbers(7);
        return sourceCode + timestamp + random;
    }

    /**
     * 为UMS API请求生成签名
     * @param appId 应用ID
     * @param appKey 应用密钥
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param body 请求体的JSON字符串
     * @return 生成的签名
     */
    @SneakyThrows
    public static String getSignature(String appId, String appKey, String timestamp, String nonce, String body) {
        byte[] data = body.getBytes("utf-8");
        InputStream is = new ByteArrayInputStream(data);
        String testSH = DigestUtils.sha256Hex(is);
        String s1 = appId+timestamp+nonce+testSH;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appKey.getBytes("utf-8"),"HmacSHA256"));
        byte[] localSignature = mac.doFinal(s1.getBytes("utf-8"));
        return Base64.encodeBase64String(localSignature);
    }

    /**
     * 构建微信统一下单请求体
     * @param merOrderId 商户订单号
     * @param mid 商户号
     * @param tid 终端号
     * @param subMid 子商户号（可选）
     * @param totalAmount 订单总金额
     * @param orderDesc 订单描述
     * @param notifyUrl 支付结果通知URL
     * @param subOrderNo 子订单号
     * @param openId 微信openId
     * @param name 用户姓名
     * @param certNo 证件号码
     * @return 微信统一下单请求体
     */
    public static Map<String, Object> buildWxUnifiedOrderRequestBody(
            String merOrderId, String mid, String tid, String subMid, BigDecimal totalAmount,
            String orderDesc, String notifyUrl, String subOrderNo, String openId, String name, String certNo) {
        Map<String, Object> reqBody = new TreeMap<>();
        reqBody.put("mid", mid);
        reqBody.put("tid", tid);
        if (subMid != null && !subMid.isEmpty()) {
            reqBody.put("subMid", subMid);
        }
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("totalAmount", totalAmount.multiply(new BigDecimal(100)).intValue()); // 转换为分
        reqBody.put("orderDesc", orderDesc);
        reqBody.put("notifyUrl", notifyUrl);
        reqBody.put("subOrderNo", subOrderNo);
        //reqBody.put("subAppId", openId); // 微信openId
        reqBody.put("subOpenId", openId); // 微信openId
        reqBody.put("openid", openId); // 微信openId
        //交易类型
        reqBody.put("tradeType", "MINI");
        reqBody.put("name", name);
        reqBody.put("certNo", certNo);
        reqBody.put("fixBuyer", "T"); // 强制实名认证
        reqBody.put("requestTimestamp", DateUtil.formatDateTime(DateUtil.date()));
        reqBody.put("instMid", "MINIDEFAULT");
        reqBody.put("secureTransaction", "false");
        return reqBody;
    }

    /**
     * 构建支付宝统一下单请求体
     * @param merOrderId 商户订单号
     * @param mid 商户号
     * @param tid 终端号
     * @param subMid 子商户号（可选）
     * @param totalAmount 订单总金额
     * @param orderDesc 订单描述
     * @param notifyUrl 支付结果通知URL
     * @param subOrderNo 子订单号
     * @param name 用户姓名
     * @param certType 证件类型，例如"01"表示身份证
     * @param certNo 证件号码
     * @return 支付宝统一下单请求体
     */
    public static Map<String, Object> buildAlipayUnifiedOrderRequestBody(
            String merOrderId, String mid, String tid, String subMid, BigDecimal totalAmount,
            String orderDesc, String notifyUrl, String subOrderNo, String name, String certType, String certNo) {
        Map<String, Object> reqBody = new TreeMap<>();
        reqBody.put("mid", mid);
        reqBody.put("tid", tid);
        if (subMid != null && !subMid.isEmpty()) {
            reqBody.put("subMid", subMid);
        }
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("totalAmount", totalAmount.multiply(new BigDecimal(100)).intValue()); // 转换为分
        reqBody.put("orderDesc", orderDesc);
        reqBody.put("notifyUrl", notifyUrl);
        reqBody.put("subOrderNo", subOrderNo);
        reqBody.put("name", name);
        reqBody.put("certType", certType); // 例如，"01"表示身份证
        reqBody.put("certNo", certNo);
        reqBody.put("fixBuyer", "T"); // 强制实名认证
        return reqBody;
    }

    /**
     * 构建银联（云闪付）统一下单请求体
     * @param merOrderId 商户订单号
     * @param mid 商户号
     * @param tid 终端号
     * @param subMid 子商户号（可选）
     * @param totalAmount 订单总金额
     * @param orderDesc 订单描述
     * @param notifyUrl 支付结果通知URL
     * @param subOrderNo 子订单号
     * @param name 用户姓名
     * @param certNo 证件号码
     * @return 银联统一下单请求体
     */
    public static Map<String, Object> buildUnionPayUnifiedOrderRequestBody(
            String merOrderId, String mid, String tid, String subMid, BigDecimal totalAmount,
            String orderDesc, String notifyUrl, String subOrderNo, String name, String certNo) {
        Map<String, Object> reqBody = new TreeMap<>();
        reqBody.put("mid", mid);
        reqBody.put("tid", tid);
        if (subMid != null && !subMid.isEmpty()) {
            reqBody.put("subMid", subMid);
        }
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("totalAmount", totalAmount.multiply(new BigDecimal(100)).intValue()); // 转换为分
        reqBody.put("orderDesc", orderDesc);
        reqBody.put("notifyUrl", notifyUrl);
        reqBody.put("subOrderNo", subOrderNo);
        reqBody.put("name", name);
        reqBody.put("certNo", certNo);
        reqBody.put("fixBuyer", "T"); // 强制实名认证
        return reqBody;
    }

    /**
     * 构建退款请求体
     * @param merOrderId 商户订单号
     * @param refundOrderId 退款订单号
     * @param mid 商户号
     * @param tid 终端号
     * @param subMid 子商户号（可选）
     * @param refundAmount 退款金额
     * @param subOrderNo 子订单号
     * @return 退款请求体
     */
    public static Map<String, Object> buildRefundRequestBody(
            String merOrderId, String refundOrderId, String mid, String tid, String subMid,
            BigDecimal refundAmount, String subOrderNo) {
        Map<String, Object> reqBody = new TreeMap<>();
        reqBody.put("mid", mid);
        reqBody.put("tid", tid);
        if (subMid != null && !subMid.isEmpty()) {
            reqBody.put("subMid", subMid);
        }
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("refundOrderId", refundOrderId);
        reqBody.put("refundAmount", refundAmount.multiply(new BigDecimal(100)).intValue()); // 转换为分
        reqBody.put("subOrderNo", subOrderNo);
        return reqBody;
    }

    /**
     * 构建关闭订单请求体
     * @param merOrderId 商户订单号
     * @param mid 商户号
     * @param tid 终端号
     * @param subMid 子商户号（可选）
     * @param subOrderNo 子订单号
     * @return 关闭订单请求体
     */
    public static Map<String, Object> buildCloseOrderRequestBody(
            String merOrderId, String mid, String tid, String subMid, String subOrderNo) {
        Map<String, Object> reqBody = new TreeMap<>();
        reqBody.put("mid", mid);
        reqBody.put("tid", tid);
        if (subMid != null && !subMid.isEmpty()) {
            reqBody.put("subMid", subMid);
        }
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("subOrderNo", subOrderNo);
        return reqBody;
    }
}