package com.zzz.component.bamboo.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 1. 创建订单接口
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class OrderRequest {

    /**
     *外部订单编码
     */
    private String outOrderCode;
    /**
     *商家名称
     */
    private String merchantName;
    /**
     *商家编码
     */
    private String merchantCode;
    /**
     *店铺名称
     */
    private String storeName;
    /**
     *店铺编码
     */
    private String storeCode;
    /**
     *客户名称
     */
    private String customerName;
    /**
     *客户编码
     */
    private String customerCode;
    /**
     *提交订单时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String orderCreateTime;
    /**
     *支行网点编码
     */
    private String branchCode;
    /**
     *支行网点名称
     */
    private String branchName;
    /**
     *配送时间，格式：yyyy-MM-dd
     */
    private String expectDeliverDate;
    /**
     *配送方式 10 通济隆子配送 24 客户自提
     */
    private String orderDeliveryMethodId;
    /**
     *订单商品行
     */
    private List<OrderItem> itemList;
}
