package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Data
@ApiModel("币种信息实体")
public class CurrencyDto {


    @ApiModelProperty(value = "自增，唯一")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "网点ID")
    private Integer branchId;

    @ApiModelProperty(value = "币种编码")
    private String currency;

    @ApiModelProperty(value = "币种名称")
    private String curname;

    @ApiModelProperty(value = "图标地址")
    private String url;

    @ApiModelProperty(value = "国家或地区")
    private String country;

    @ApiModelProperty(value = "基础汇率，5.0457CNY；例：1JPY=5.0457CNY")
    private BigDecimal actualPrice;

    @ApiModelProperty(value = "售卖汇率，5.0457CNY；例：1JPY=5.0457CNY")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "步值，增加整倍数")
    private Integer unit;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改时间")
    private String updateTime;

    @ApiModelProperty(value = "最小金额单位，最小起订量；")
    private Integer minUnit;

    @ApiModelProperty(value = "最大金额单位，最大起订量；")
    private Integer maxUnit;

    @ApiModelProperty(value = "汇率单位；100JPY；")
    private Integer rateUnit;


}
