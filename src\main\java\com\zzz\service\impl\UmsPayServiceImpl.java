package com.zzz.service.impl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zzz.config.ums.UmsPayConfig;
import com.zzz.emuns.CommonEnum;
import com.zzz.entity.CommonInfo;
import com.zzz.entity.TbOrder;
import com.zzz.service.ICommonInfoService;
import com.zzz.service.IUmsPayService;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * UMS支付服务实现
 */
@Slf4j
@Service
public class UmsPayServiceImpl implements IUmsPayService {

    @Autowired
    private UmsPayConfig umsPayConfig;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Override
    public Map<String, Object> generatePayParams(TbOrder order) {
        try {
            // 1. 构建请求体
            Map<String, Object> reqBody = buildRequestBody(order);
            String reqBodyJson = JSONUtil.toJsonStr(reqBody);
            log.info("UMS支付请求体: {}", reqBodyJson);

            // 2. 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = getSignature(umsPayConfig.getAppId(), umsPayConfig.getAppKey(), timestamp, nonce, reqBodyJson);

            // 3. 构建请求URL和参数
            Map<String, Object> result = new HashMap<>();
            // 根据支付类型选择不同的支付URL
            String payType = order.getPayType();
            if (payType == null || payType.isEmpty() || "WECHAT".equals(payType)) {
                result.put("payUrl", umsPayConfig.getWxPayUrl());
            } else if ("ALIPAY".equals(payType)) {
                result.put("payUrl", umsPayConfig.getAliPayUrl());
            } else if ("UNIONPAY".equals(payType)) {
                result.put("payUrl", umsPayConfig.getUnionPayUrl());
            } else {
                // 默认使用微信支付
                result.put("payUrl", umsPayConfig.getWxPayUrl());
            }
            result.put("authorization", "OPEN-FORM-PARAM");
            result.put("appId", umsPayConfig.getAppId());
            result.put("timestamp", timestamp);
            result.put("nonce", nonce);
            result.put("content", URLEncoder.encode(reqBodyJson, "UTF-8"));
            result.put("signature", URLEncoder.encode(signature, "UTF-8"));
            result.put("orderNo", order.getOrderNumber());

            return result;
        } catch (Exception e) {
            log.error("生成UMS支付参数异常", e);
            throw new RuntimeException("生成支付参数失败", e);
        }
    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        // 处理支付回调逻辑
        log.info("UMS支付回调参数: {}", JSONUtil.toJsonStr(params));

        try {
            // 验证必要参数
            String status = params.get("status");
            String merOrderId = params.get("merOrderId");
            String mid = params.get("mid");
            String tid = params.get("tid");
            String totalAmount = params.get("totalAmount");

            if (StringUtils.isAnyBlank(status, merOrderId, mid, tid, totalAmount)) {
                log.error("UMS支付回调参数不完整");
                return false;
            }

            // 验证商户号和终端号
            if (!mid.equals(umsPayConfig.getMid()) || !tid.equals(umsPayConfig.getTid())) {
                log.error("UMS支付回调商户号或终端号不匹配");
                return false;
            }

            // 验证支付状态，SUCCESS表示支付成功
            if (!"SUCCESS".equals(status)) {
                log.error("UMS支付回调状态不是成功: {}", status);
                return false;
            }

            // 获取订单号
            String attachedData = params.get("attachedData");
            if (StringUtils.isNotBlank(attachedData)) {
                log.info("UMS支付成功，订单号: {}", attachedData);
            } else {
                log.info("UMS支付成功，商户订单号: {}", merOrderId);
            }

            return true;
        } catch (Exception e) {
            log.error("处理UMS支付回调异常", e);
            return false;
        }
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequestBody(TbOrder order) {
        Map<String, Object> reqBody = new HashMap<>();

        // 生成商户订单号，按照银商规范: {来源编号(4位)}{时间(yyyyMMddmmHHssSSS)(17位)}{7位随机数}
        String merOrderId = generateMerOrderId(order.getOrderNumber());

        // 设置必要参数
        reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
        reqBody.put("merOrderId", merOrderId);
        reqBody.put("mid", umsPayConfig.getMid());
        reqBody.put("tid", umsPayConfig.getTid());
        reqBody.put("instMid", umsPayConfig.getInstMid());
        reqBody.put("subAppId", umsPayConfig.getSubMid());

        // 设置金额，单位为分
        BigDecimal amount = order.getOrderPayAmount().multiply(new BigDecimal("100")).setScale(0, RoundingMode.DOWN);
        reqBody.put("totalAmount", amount.toString());

        // 设置订单描述
        reqBody.put("orderDesc", "外币兑换-订单号: " + order.getOrderNumber());

        // 设置通知地址
        reqBody.put("notifyUrl", umsPayConfig.getNotifyUrl());
        reqBody.put("returnUrl", umsPayConfig.getReturnUrl());

        // 设置担保交易标识
        reqBody.put("secureTransaction", "false");

        // 设置微信支付相关参数
        reqBody.put("appType", "3"); // 3表示微信小程序

        // 获取微信小程序appId
        List<CommonInfo> wxConfigList = iCommonInfoService.getBaseInfoByCode(CommonEnum.WX_MINI_P);
        Map<String, String> wxConfigMap = wxConfigList.stream()
                .collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
        String wxAppId = wxConfigMap.get(CommonEnum.APPID.name());

        reqBody.put("appId", wxAppId); // 微信小程序appId
        reqBody.put("subAppId", umsPayConfig.getSubMid()); // 微信子商户号

        // 设置订单附加数据，用于回调时识别
        reqBody.put("attachedData", order.getOrderNumber());

        return reqBody;
    }

    /**
     * 生成商户订单号
     */
    private String generateMerOrderId(String orderNo) {
        // 来源编号(4位) + 时间(17位) + 随机数(7位)
        String sourceCode = umsPayConfig.getSourceCode();
        String timeStr = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        String randomStr = RandomUtil.randomNumbers(7);
        return sourceCode + timeStr + randomStr;
    }

    /**
     * 获取签名
     */
    private String getSignature(String appid, String appkey, String timestamp, String nonce, String body) throws Exception {
        byte[] data = body.getBytes(StandardCharsets.UTF_8);
        InputStream is = new ByteArrayInputStream(data);
        String sha256Hex = DigestUtils.sha256Hex(is);
        String s1 = appid + timestamp + nonce + sha256Hex;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appkey.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] localSignature = mac.doFinal(s1.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(localSignature);
    }
}
