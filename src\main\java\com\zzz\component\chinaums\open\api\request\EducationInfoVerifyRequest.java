package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.EducationInfoVerifyResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/5/8
 * Time: 17:02
 * 所属模块：
 * 功能说明：学历信息验证
 */
public class EducationInfoVerifyRequest implements OpenApiRequest<EducationInfoVerifyResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;
    public Class<EducationInfoVerifyResponse> responseClass() {
        return EducationInfoVerifyResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "学历信息验证查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/education/query";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
