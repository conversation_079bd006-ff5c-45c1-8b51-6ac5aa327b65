package com.zzz.controller;
import com.zzz.component.chinaums.open.api.OpenApiCache;
import com.zzz.component.chinaums.open.api.OpenApiContext;
import com.zzz.component.chinaums.open.api.constants.ConfigBean;
import com.zzz.component.chinaums.open.api.internal.util.http.HttpTransport;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
public class TestMain {

    static String appId = "8a81c1be8fc4b725019039e118e202ee";
    static String appKey = "Pa366mj7YfdGm3a2Rsr8wpswBc2PNpXN328PSJWehSKw7AZ6";
    static String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    static String nonce = UUID.randomUUID().toString().replace("-", "");

    static String authorization;


        public static void main(String[] args) throws Exception{
            //开发者ID 平台分配
            String appId = "8a81c1be8fc4b725019039e118e202ee";
            //开发者秘钥 平台分配
            String appKey = "Pa366mj7YfdGm3a2Rsr8wpswBc2PNpXN328PSJWehSKw7AZ6";




//            生产环境: POST https://api-mop.chinaums.com/v1/netpay/wx/unified-order
//            测试环境: POST https://test-api-open.chinaums.com/v1/netpay/wx/unified-order
            String url = "https://api-mop.chinaums.com/v1/netpay/wx/unified-order";


//            银联支付账号信息如下：
//            ************机构号
//            账号：TONGJILONG
//            密码：Aa112233!

            //实例化客户端
            ConfigBean configBean = new ConfigBean();
            OpenApiContext context = new OpenApiContext();
            String merOrderId = System.currentTimeMillis()+"";
            String subAppId = "wxc61f8399d8ff1e73";
//            String request = "{\"requestTimestamp\":\"2022-04-2010:10:13\", \"merOrderId\":\""+merOrderId+"\", \"srcReserve\":\"39GG\", \"mid\":\"89831016012Z583\",\"tid\":\"Z5830001\",\"instMid\":\"MINIDEFAULT\", \"goods\":\"[{\"goodsId\":\"001\",\"goodsName\":\"鸡蛋\",\"quantity\":\"10\",\"price\":\"1\",\"goodsCategory\":\"food meterial\",\"body\":\"two eggs\",\"unit\":\"个\",\"discount\":\"5\",\"subMerchantId\":\"988460101800201\",\"merOrderId\":\"dd20220419111222\",\"subOrderAmount\":\"100\"}]\",                     \"attachedData\":\"\",                     \"expireTime\":\"2022-04-2210:10:13\",                     \"goodsTag\":\"\",                     \"orderDesc\":\"测试\",                     \"originalAmount\":\"10\",                     \"productId\":\"002\",                     \"totalAmount\":\"1\",                     \"divisionFlag\":\"true\",                     \"asynDivisionFlag\":\"true\",                     \"platformAmount\":\"1\",                     \"subOrders\":\"[{\"mid\":\"898201612345678\",\"merOrderId\":\"dd20220419111222\",\"totalAmount\":\"1\"}]\",                     \"notifyUrl\":\"http://www.test.com/notify\",                     \"secureTransaction\":\"false\",                     \"subAppId\":\"\",                     \"userId\":\"\",                     \"tradeType\":\"MINI\",                     \"limitCreditCard\":\"false\",                     \"installmentNumber\":\"3\",                     \"name\":\"张*三\",                     \"mobile\":\"131****3453\",                     \"certType\":\"IDENTITY_CARD\",                     \"certNo\":\"110101********9008\",                     \"fixBuyer\":\"false\",                     \"retCommParams\":\"{\"foodOrderType\":\"pre_order\"}\",                     \"feeRatio\":\"5\",                     \"costSubsidy\":\"默认false\",                     \"preauthTransaction\":\"false\",                     \"clientIp\":\"*******\",                     \"subOpenId\":\"\"}";
            String request = "{\"requestTimestamp\":\"2022-04-20 10:10:13\",\"merOrderId\":\""+merOrderId+"\",\"srcReserve\":\"39GG\",\"mid\":\"89831016012Z583\",\"tid\":\"Z5830001\",\"instMid\":\"MINIDEFAULT\",\"attachedData\":\"\",\"orderDesc\":\"测试\",\"originalAmount\":\"10\",\"totalAmount\":\"1\",\"notifyUrl\":\"http://www.test.com/notify\",\"subAppId\":\""+subAppId+"\",\"tradeType\":\"MINI\",\"clientIp\":\"127.0.0.1\",\"subOpenId\":\"oQ5W36wmwpuReUzRBrLWC-Dch2SY\"}";
            context.setStartTime(System.currentTimeMillis());
            context.setRequestId(UUID.randomUUID().toString().replace("-", ""));
            context.setOpenServUrl(url.split("/v")[0].concat("/"));
            context.setApiServiceUrl(url);
            context.setVersion(url.split("/")[3]);
            context.setAppId(appId);
            context.setAppKey(appKey);
            context.setConfigBean(configBean);
            context.setServiceCode(url.split("/v")[1].substring(1));
            OpenApiCache.getCurrentToken(context);
            System.out.println(HttpTransport.getInstance().doPost(context, request));
        }



    /**
     * open-body-sig方式获取到Authorization 的值
     *
     * @param appId  f0ec96ad2c3848b5b810e7aadf369e2f
     * @param appKey 775481e2556e4564985f5439a5e6a277
     * @param body   json字符串 String body = "{\"merchantCode\":\"123456789900081\",\"terminalCode\":\"00810001\",\"merchantOrderId\":\"20123333644493200\",\"transactionAmount\":\"1\",\"merchantRemark\":\"测试\",\"payMode\":\"CODE_SCAN\",\"payCode\":\"285668667587422761\",\"transactionCurrencyCode\":\"156\"}";
     * @return
     * @throws Exception
     */
    public static String getOpenBodySig(String appId, String appKey, String body) throws Exception {
        byte[] data = body.getBytes("UTF-8");
        System.out.println("data:\n" + body);
        InputStream is = new ByteArrayInputStream(data);
        String bodyDigest = DigestUtils.sha256Hex(is);
        System.out.println("bodyDigest:\n" + bodyDigest);
        String str1_C = appId + timestamp + nonce + bodyDigest;

        System.out.println("str1_C:" + str1_C);
        byte[] localSignature = hmacSHA256(str1_C.getBytes(), appKey.getBytes());
        String localSignatureStr = Base64.encodeBase64String(localSignature);   // Signature

        System.out.println("localSignatureStr: " + localSignatureStr);

        System.out.println("Authorization:\n" + "OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"\n");
        return ("OPEN-BODY-SIG AppId=" + "\"" + appId + "\"" + ", Timestamp=" + "\"" + timestamp + "\"" + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"");
    }

    /**
     * @param data
     * @param key
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    public static byte[] hmacSHA256(byte[] data, byte[] key) throws NoSuchAlgorithmException, InvalidKeyException {
        String algorithm = "HmacSHA256";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(key, algorithm));
        return mac.doFinal(data);
    }
}
