package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Data
@ApiModel("网点列表查询参数")
public class BranchPageVo {

    @ApiModelProperty("币种代码，可选，从币种列表点击过来必传")
    private String currency;

    @ApiModelProperty("标签ID")
    private String tagId;

    @ApiModelProperty(value = "经度",required =true)
    @NotNull(message = "经度不能为空")
    private Double longitude;

    @ApiModelProperty(value = "维度",required =true)
    @NotNull(message = "维度不能为空")
    private Double latitude;

    @ApiModelProperty(value="页数",required = true,example = "10")
    @NotNull(message = "页数不能为空")
    private Integer size;

    @ApiModelProperty(value="页码",required = true,example = "1")
    @NotNull(message = "页码不能为空")
    private Integer page;

}
