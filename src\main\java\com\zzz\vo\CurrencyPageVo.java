package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Data
@ApiModel("币种列表查询参数")
public class CurrencyPageVo {


    @ApiModelProperty(value = "查询关键字,币种名称或币种代码")
    private  String keywords;

    @ApiModelProperty(value="页数",required = true,example = "10")
    private Integer size;

    @ApiModelProperty(value="页码",required = true,example = "1")
    private Integer page;
}
