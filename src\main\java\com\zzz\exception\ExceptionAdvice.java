package com.zzz.exception;

import cn.dev33.satoken.exception.NotLoginException;
import com.fasterxml.jackson.core.JsonParseException;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.response.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 异常控制处理器
 */
@Slf4j
@RestControllerAdvice
public class ExceptionAdvice {

    /**
     * 捕捉参数格式错误异常(HttpMessageNotReadableException) (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public JSONResult validException(HttpMessageNotReadableException e, Throwable ex) {
        e.printStackTrace();
        return JSONResult.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "参数格式错误");
    }

    /**
     * 捕捉参数格式错误异常(HttpMessageNotReadableException) (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(JsonParseException.class)
    public JSONResult validException(JsonParseException e, Throwable ex) {
        e.printStackTrace();
        return JSONResult.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "参数格式错误");
    }


    /**
     * 捕捉校验异常(MethodArgumentNotValidException) (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public JSONResult validException(MethodArgumentNotValidException e, Throwable ex) {
        FieldError fieldError = e.getBindingResult().getFieldError();
        return JSONResult.error(fieldError.getField() + fieldError.getDefaultMessage());
    }

    /**
     * 捕捉404异常 (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(NoHandlerFoundException.class)
    public JSONResult handle(NoHandlerFoundException e) {
        e.printStackTrace();
        return JSONResult.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
    }

    /**
     * 捕捉其他所有异常 (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public JSONResult globalException(HttpServletRequest request, Throwable ex, Exception e) {
        e.printStackTrace();
        String errorMsg = String.format("系统繁忙，请稍后再试!（%s）",e.getMessage());
        return JSONResult.error(errorMsg);
    }

    /**
     * 捕捉ServiceException  (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ServiceException.class)
    public JSONResult ServiceException(HttpServletRequest request, Throwable ex, Exception e) {
        e.printStackTrace();
        ServiceException serviceException = (ServiceException) e;
        return JSONResult.error(serviceException.getErrorCode(), serviceException.getMessage());
    }

    /**
     * NotLoginException  (200)
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(NotLoginException.class)
    public JSONResult NotLoginException(NotLoginException nle) {
        String message = "";
        if (nle.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未提供Token";
        } else if (nle.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "未提供有效的Token";
        } else if (nle.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "登录信息已过期，请重新登录";
        } else if (nle.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "您的账户已在另一台设备上登录，如非本人操作，请立即修改密码";
        } else if (nle.getType().equals(NotLoginException.KICK_OUT)) {
            message = "已被系统强制下线";
        } else {
            message = "当前会话未登录";
        }
        return JSONResult.error(nle.getCode(), message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public JSONResult ConstraintValidationExceptionHandler(ConstraintViolationException e) {
        e.printStackTrace();
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        String message = "";
        for (ConstraintViolation c : constraintViolations) {
            message = c.getMessage();
        }

        return JSONResult.error(CommonErrorEnum.VALIDATION_EXCEPTION.getCode(), message);
    }
}
