package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.entity.TbCurrencyBase;
import com.zzz.mapper.TbCurrencyBaseMapper;
import com.zzz.service.ITbCurrencyBaseService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 币种基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-13
 */
@Service
public class TbCurrencyBaseServiceImpl extends ServiceImpl<TbCurrencyBaseMapper, TbCurrencyBase> implements ITbCurrencyBaseService {

    @Autowired
    private TbCurrencyBaseMapper tbCurrencyBaseMapper;

    @Override
    public TbCurrencyBase getCurrencyByCode(String currency) {
        return tbCurrencyBaseMapper.selectOne(new LambdaQueryWrapper<TbCurrencyBase>().eq(TbCurrencyBase::getCurCode,currency));
    }

    @Override
    public List<TbCurrencyBase> getList() {
        return tbCurrencyBaseMapper.selectList(null);
    }
}
