package com.zzz.controller.v1;

import com.zzz.dto.OrderPageDto;
import com.zzz.response.JSONResult;
import com.zzz.service.SyncService;
import com.zzz.vo.OrderPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-12
 **/
@Api(tags = "同步第三方数据")
@Slf4j
@RestController
@RequestMapping("/api/v1/sync")
public class SyncController {

    @Autowired
    private SyncService syncService;

    /**
     * 同步客户数据 Bamboo
     */
    @ApiOperation("同步客户数据(Bamboo)")
    @GetMapping("/bamboo/customer")
    public JSONResult syncCustomer(){
        return syncService.syncCustomer();
    }

    /**
     * 同步商品数据 Bamboo
     */
    @ApiOperation("同步商品数据(Bamboo)")
    @GetMapping("/bamboo/product")
    public JSONResult syncProduct(){
        return syncService.syncProduct();
    }

    // 同步 max
    @ApiOperation("同步Max系统数据")
    @GetMapping("/max")
    public JSONResult syncMax(){
        return syncService.syncMax();
    }

    //同步零钱包
    @ApiOperation("同步Max系统数据")
    @GetMapping("/coin")
    public JSONResult syncMaxCoin(){
        return syncService.syncMaxCoin();
    }

}
