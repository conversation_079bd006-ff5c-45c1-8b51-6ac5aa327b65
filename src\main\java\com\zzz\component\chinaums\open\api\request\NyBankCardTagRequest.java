package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.NyBankCardTagResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/3/21
 * Time: 18:07
 * 所属模块：
 * 功能说明：楠云银行卡标签提取
 */
public class NyBankCardTagRequest  implements OpenApiRequest<NyBankCardTagResponse> {
    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;

    public Class<NyBankCardTagResponse> responseClass() {
        return NyBankCardTagResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "楠云银行卡标签提取查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/bankcardtag/query";
    }

    public boolean needToken() {
        return true;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
