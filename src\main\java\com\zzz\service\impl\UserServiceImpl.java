package com.zzz.service.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipayEncrypt;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.request.AlipayUserInfoShareRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationResponse;
import com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationResponseBody;
import com.aliyun.tea.TeaException;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.config.CommomConfig;
import com.zzz.config.alipay.AlipayConfig;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.*;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.emuns.DeleteStatusEnum;
import com.zzz.emuns.RegSourceEnum;
import com.zzz.entity.*;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.UserMapper;
import com.zzz.response.JSONResult;
import com.zzz.service.ICommonInfoService;
import com.zzz.service.IUserInformationService;
import com.zzz.service.IUserOpenInfoService;
import com.zzz.service.IUserService;
import com.zzz.util.SpringBeanUtil;
import com.zzz.util.UserContextUtil;
import com.zzz.util.sms.SmsUtil;
import com.zzz.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    /**
     * 私钥
     */
    @Value("${server.rsa.publicKeyStr}")
    private String publicKeyStr;
    @Value("${server.rsa.privateKeyStr}")
    private String privateKeyStr;

    @Autowired
    private SmsUtil smsUtil;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private IUserInformationService iUserInformationService;

    @Resource
    private IUserOpenInfoService iUserOpenInfoService;

    @Override
    public JSONResult<WxAuthDto> wxAuth(WxAuthVo req) {
        if(StringUtils.isBlank(req.getJsCode())){
            return JSONResult.error(CommonErrorEnum.WX_AUTH_CODE_ERROR);
        }
        //获取微信配置信息
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.WX_MINI_P);
        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
        String appid = WxParamMap.get(CommonEnum.APPID.name());
        String secret = WxParamMap.get(CommonEnum.APP_SECRET.name());
        //1.调用微信sdk 获取session_key和openid
        Map<String, Object> map = new HashMap<>();
        map.put("appid", appid);
        map.put("secret", secret);
        map.put("js_code", req.getJsCode());
        map.put("grant_type", "authorization_code");
        String result = HttpUtil.get(CommomConfig.WX_JSCODE_URL, map);
        log.info("------微信认证接口返回：{}", result);
        JSONObject parsedObj = JSONUtil.parseObj(result);
        String sessionKey = parsedObj.getStr("session_key");
        String openid = parsedObj.getStr("openid");
        //获取用户信息失败
        if (StrUtil.isEmpty(sessionKey) && StrUtil.isEmpty(openid))
            return JSONResult.error(CommonErrorEnum.GET_WX_AUTH_ERROR);
        //获取成功返回信息
        WxAuthDto res = WxAuthDto.create().setOpenId(openid).setSessionKey(sessionKey);
        return JSONResult.ok(res);
    }

    @Override
    @Transactional
    public JSONResult<WxPhoneDto> wxPhone(WxPhoneVo req) {
        if(StringUtils.isBlank(req.getCode())){
            return JSONResult.error(CommonErrorEnum.WX_AUTH_PHONE_CODE_ERROR);
        }
        if(StringUtils.isBlank(req.getOpenId())){
            return JSONResult.error(CommonErrorEnum.WX_OPENID_ERROR);
        }
        //获取微信配置信息
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.WX_MINI_P);
        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
        String appid = WxParamMap.get(CommonEnum.APPID.name());
        String secret = WxParamMap.get(CommonEnum.APP_SECRET.name());
        //1.获取access_token
        Map<String, Object> map = new HashMap<>();
        map.put("appid", appid);
        map.put("secret", secret);
        map.put("grant_type", "client_credential");
        String result = HttpUtil.get(CommomConfig.WX_TOKEN_URL, map);
        log.info("------微信获取access_token接口返回：", result);
        JSONObject parsedObj = JSONUtil.parseObj(result);
        String accessToken = parsedObj.getStr("access_token");
        //2.获取手机号码
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken.trim();
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        jsonObject.put("code", req.getCode());
        String getPhoneResult = HttpUtil.post(url, jsonObject.toString());
        log.info("------微信获取phone接口返回：{}", getPhoneResult);
        JSONObject getPhoneJSONObject = JSONUtil.parseObj(getPhoneResult);
        String phone = getPhoneJSONObject.getJSONObject("phone_info").getStr("purePhoneNumber");
        //微信获取不到手机号
        if (StrUtil.isEmpty(phone)) {
            return JSONResult.error(CommonErrorEnum.GET_USER_PHONE_ERROR);
        }
        //3.判断手机号在数据库是否存在
        User user = lambdaQuery().eq(User::getPhone, phone).eq(User::getIsDel, DeleteStatusEnum.NORMAL.value).one();
        if (user == null) {
            //新用户 注册进数据库
            user = new User();
            user.setNo(getUserNo());
            // user.setReg(1);
            //昵称随机
            user.setNickname(String.format("昵称%s", RandomUtil.randomString(6)));
            //头像默认
            user.setAvatar("");
            // user.setOpenId(req.getOpenId());
            user.setPhone(phone);
            user.setChannelId("1");
            save(user);
            // 添加用户注册渠道表
            UserOpenInfo userOpenInfo = new UserOpenInfo();
            userOpenInfo.setUserId(user.getId());
            userOpenInfo.setReg(RegSourceEnum.WX.value());
            userOpenInfo.setOpenId(req.getOpenId());
            userOpenInfo.setAddTime(LocalDateTime.now());
            iUserOpenInfoService.save(userOpenInfo);
        } else {
            // 判断用户注册渠道是否存在
            UserOpenInfo userOpenInfo = iUserOpenInfoService.getOne(Wrappers.<UserOpenInfo>lambdaQuery()
                    .eq(UserOpenInfo::getUserId, user.getId())
                    .eq(UserOpenInfo::getReg, RegSourceEnum.WX.value())
                    .eq(UserOpenInfo::getIsDel, DeleteStatusEnum.NORMAL.value)
            );
            if (null == userOpenInfo) {
                userOpenInfo = new UserOpenInfo();
                userOpenInfo.setUserId(user.getId());
                userOpenInfo.setReg(RegSourceEnum.WX.value());
                userOpenInfo.setOpenId(req.getOpenId());
                userOpenInfo.setAddTime(LocalDateTime.now());
                iUserOpenInfoService.save(userOpenInfo);
            }
        }
        //4.响应数据
        WxPhoneDto res = WxPhoneDto.create().setId(user.getId()).setPhone(user.getPhone());
        return JSONResult.ok(res);
    }

    @Override
    @Transactional
    public JSONResult loginPhone(LoginPhoneVo req, Integer regSource) {
        if(StringUtils.isBlank(req.getPhone())){
            return JSONResult.error(CommonErrorEnum.PHOME_IS_NULL);
        }
        //1.判断用户
        User user = lambdaQuery().eq(User::getPhone, req.getPhone()).eq(User::getIsDel, DeleteStatusEnum.NORMAL.value).one();
        if (user == null) {
            //代表用户第一次登录从H5端 注册进数据库
            user = new User();
            user.setNo(getUserNo());
            // user.setReg(1);
            //昵称随机
            user.setNickname(String.format("昵称%s", RandomUtil.randomString(6)));
            //头像默认
            user.setAvatar("");
            user.setValid(1);
            user.setPhone(req.getPhone());
            save(user);
            // 添加用户注册渠道表
            UserOpenInfo userOpenInfo = new UserOpenInfo();
            userOpenInfo.setUserId(user.getId());
            userOpenInfo.setReg(RegSourceEnum.H5.value());
            // H5为随机值
            userOpenInfo.setOpenId(IdUtil.fastSimpleUUID());
            userOpenInfo.setAddTime(LocalDateTime.now());
            iUserOpenInfoService.save(userOpenInfo);
        }
        //2.登录
        StpUtil.login(user.getId());
        //3.获取 Token  相关参数
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        //4.把用户其他信息存入session中
        ReqContextUser reqContextUser = ReqContextUser.create().setId(user.getId()).setPhone(user.getPhone()).setValid(user.getValid());
        StpUtil.getSession().set(ProjectConstant.USER, reqContextUser);
        //5.返回给前端固定信息
        LoginPhoneDto res = LoginPhoneDto.create().setId(user.getId()).setPhone(user.getPhone())
                .setValid(user.getValid() == null ? 0 : user.getValid()).setTokenInfo(tokenInfo);
        return JSONResult.ok(res);
    }

    @Override
    @Transactional
    public JSONResult validPhone(SmsValidVo req, Integer regSource) {
        //非空校验
        if(StringUtils.isBlank(req.getPhone())){return JSONResult.error(CommonErrorEnum.PHOME_IS_NULL);}
        if(StringUtils.isBlank(req.getCode())){return JSONResult.error(CommonErrorEnum.PHOME_CODE_IS_NULL);}
        if(StringUtils.isBlank(req.getTypeConstant())){return JSONResult.error(CommonErrorEnum.SMS_TYPE_IS_NULL);}

        //1.查询用户
        User user = lambdaQuery().eq(User::getPhone, req.getPhone()).eq(User::getIsDel, DeleteStatusEnum.NORMAL.value).one();
        if (user == null) {
            throw new ServiceException(CommonErrorEnum.USER_IS_NULL);
        }
        UserOpenInfo userOpenInfo = iUserOpenInfoService.getOne(Wrappers.<UserOpenInfo>lambdaQuery()
                .eq(UserOpenInfo::getUserId, user.getId())
                .eq(UserOpenInfo::getReg, RegSourceEnum.WX.value())
                .eq(UserOpenInfo::getIsDel, DeleteStatusEnum.NORMAL.value)
        );
        if (null == userOpenInfo) {
            throw new ServiceException(CommonErrorEnum.USER_IS_NULL);
        }
        //2.验证手机号
        smsUtil.validSms(req.getPhone(), req.getCode(), req.getTypeConstant(), false);
        //3.修改用户信息
        user.setValid(1);
        updateById(user);
        //4.覆盖redis的用户信息值,用作拦截器判断用户是否验证手机号码
        ReqContextUser reqContextUser = UserContextUtil.get().setValid(1);
        StpUtil.getSession().set(ProjectConstant.USER, reqContextUser);
        return JSONResult.ok();
    }

    @Override
    public UserDto getUserDetail() {
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        Integer id = reqContextUser.getId();
        User user = userMapper.selectById(id);
        //用户信息不存在
        if (null == user) {
            throw new ServiceException(CommonErrorEnum.USER_IS_NULL);
        }
        UserDto userDto = SpringBeanUtil.copyProperties(user, UserDto.class);
        //加密处理
        RSA rsa = new RSA(privateKeyStr, publicKeyStr);
        // 加密传输
        if(StringUtils.isNoneBlank(userDto.getPhone())){
            userDto.setPhone(rsa.encryptBase64(userDto.getPhone(), KeyType.PublicKey));
        }
        return userDto;
    }

    @Override
    @Transactional
    public JSONResult doRealnameAuth(RealNameAuthVo req) throws Exception {
        //1.非空校验
        if(StringUtils.isBlank(req.getRealname())){
            return JSONResult.error(CommonErrorEnum.REALNAME_NOT_NULL);
        }
        if(StringUtils.isBlank(req.getIdcard())){
            return JSONResult.error(CommonErrorEnum.IDCARD_NOT_NULL);
        }
        //2.解密数据
        RSA rsa = new RSA(privateKeyStr, publicKeyStr);
        String name = StrUtil.utf8Str(rsa.decrypt(req.getRealname(), KeyType.PrivateKey));
        String idcard = StrUtil.utf8Str(rsa.decrypt(req.getIdcard(), KeyType.PrivateKey));

        //3.验证是否已实名过
        UserInformation realName = iUserInformationService.getRealName(name,idcard);
        if(null != realName){
            return JSONResult.error(CommonErrorEnum.REALNAME_IS_EXIST);
        }
        //4.调用第三方接口验证实名
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.ALI_CONFIG_P);
        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
        //当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        User byId = getById(reqContextUser.getId());
        //实名认证接口
        Boolean flag = doRealNameVerification(byId.getPhone(), name, idcard, WxParamMap);
        if(!flag){
            //不通过
            return JSONResult.error(CommonErrorEnum.REALNAME_ERROR);
        }
        //5.实名成功，新增实名认证信息
        int i = iUserInformationService.addRealNameInfo(name,idcard,byId.getPhone());
        if(i > 0){
            //更新用户信息实名标识 为已实名
            userMapper.update(null,new LambdaUpdateWrapper<User>().eq(User::getId,reqContextUser.getId()).set(User::getRealName,1));
        }
        return JSONResult.ok();
    }

    @Override
    public JSONResult<LoginPhoneDto> loginH5(LoginH5Vo req) {
        //1.非空校验
        if(StringUtils.isBlank(req.getPhone())){return JSONResult.error(CommonErrorEnum.PHOME_IS_NULL);}
        if(StringUtils.isBlank(req.getCode())){return JSONResult.error(CommonErrorEnum.PHOME_CODE_IS_NULL);}
        if(StringUtils.isBlank(req.getTypeConstant())){return JSONResult.error(CommonErrorEnum.SMS_TYPE_IS_NULL);}

        //2.验证手机号
        smsUtil.validSms(req.getPhone(), req.getCode(), req.getTypeConstant(), false);

        //3.判断用户，不存在就注册
        User user = lambdaQuery().eq(User::getPhone, req.getPhone()).eq(User::getChannelId,3).one();
        if (user == null) {
            //代表用户第一次登录从H5端 注册进数据库
            user = new User();
            user.setNo(getUserNo());
            // user.setReg(3);
            user.setValid(1);
            //昵称随机
            user.setNickname(String.format("昵称%s", RandomUtil.randomString(6)));
            //头像默认
            user.setAvatar("");
            user.setPhone(req.getPhone());
            // user.setChannelId("3");
            save(user);
            // 添加用户注册渠道表
            UserOpenInfo userOpenInfo = new UserOpenInfo();
            userOpenInfo.setUserId(user.getId());
            userOpenInfo.setReg(RegSourceEnum.H5.value());
            // H5为随机值
            userOpenInfo.setOpenId(IdUtil.fastSimpleUUID());
            userOpenInfo.setAddTime(LocalDateTime.now());
            iUserOpenInfoService.save(userOpenInfo);
        }
        //4.登录
        StpUtil.login(user.getId());
        //5.获取 Token  相关参数
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        //6.把用户其他信息存入session中
        ReqContextUser reqContextUser = ReqContextUser.create().setId(user.getId()).setPhone(user.getPhone()).setValid(user.getValid());
        StpUtil.getSession().set(ProjectConstant.USER, reqContextUser);
        //7.返回给前端固定信息
        LoginPhoneDto res = LoginPhoneDto.create().setId(user.getId()).setPhone(user.getPhone())
                .setValid(user.getValid() == null ? 0 : user.getValid()).setTokenInfo(tokenInfo);
        return JSONResult.ok(res);
    }

    @Override
    public JSONResult<AlipayAuthDto> alipayAuth(AlipayAuthVo req) {
        if(StringUtils.isBlank(req.getAutCode())){
            return JSONResult.error(CommonErrorEnum.WX_AUTH_CODE_ERROR);
        }
        //获取微信配置信息
//        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.WX_MINI_P);
//        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
//        String appid = WxParamMap.get(CommonEnum.APPID.name());
//        String secret = WxParamMap.get(CommonEnum.APP_SECRET.name());
        //1.调用支付宝sdk 获取 AccessToken 和userid
        String token = "";
        String userid = "";
        String openId = "";
        try{
           AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();
            //授权方式：authorization_code，表示换取使用用户授权码code换取授权令牌access_token。
            request.setGrantType("authorization_code");
            // 4. 填入前端传入的授权码authCode
            //授权码，用户对应用授权后得到。本参数在 grant_type 为 authorization_code 时必填
            request.setCode(req.getAutCode());
           AlipaySystemOauthTokenResponse response = AlipayConfig.getInstance().execute(request);
           if(response.isSuccess()){
               log.info("支付宝小程序授权:{}",JSONUtil.toJsonStr(response));
//               userid = response.getUserId();
               openId = response.getOpenId();
               token = response.getAccessToken();
           }
        }catch (Exception e){
            log.error("支付宝小程序授权失败：{}",e.getMessage());
        }
        //获取用户信息失败
        if (StrUtil.isEmpty(token) && StrUtil.isEmpty(openId))
            return JSONResult.error(CommonErrorEnum.GET_WX_AUTH_ERROR);
        //获取成功返回信息
        AlipayAuthDto res = AlipayAuthDto.create().setToken(token).setOpenid(openId);
        return JSONResult.ok(res);
    }

    @Override
    public JSONResult<AlipayPhoneDto> alipayPhone(AlipayPhoneVo req) {
        if(StringUtils.isBlank(req.getToken())){
            return JSONResult.error(CommonErrorEnum.ALIPAY_AUTH_PHONE_CODE_ERROR);
        }
//        if(StringUtils.isBlank(req.getOpenid())){
//            return JSONResult.error(CommonErrorEnum.ALIPAY_OPENID_ERROR);
//        }
        //获取微信配置信息
//        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.WX_MINI_P);
//        Map<String, String> WxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
//        String appid = WxParamMap.get(CommonEnum.APPID.name());
//        String secret = WxParamMap.get(CommonEnum.APP_SECRET.name());
        //1.获取 支付宝用户信息
        String nickName = "";
        String avatar = "";
        String openid = "";
        //https://opendocs.alipay.com/open/a74a7068_alipay.user.info.share?pathHash=af2476d4&scene=common
        try{
            AlipayUserInfoShareRequest request = new AlipayUserInfoShareRequest();
            AlipayUserInfoShareResponse response = AlipayConfig.getInstance().execute(request, req.getToken());
            if(response.isSuccess()){
                nickName = response.getNickName();
                avatar = response.getAvatar();
                openid = response.getOpenId();
                log.info("支付宝小程序获取用户信息:{}",JSONUtil.toJsonStr(response));
            }
        }catch (Exception e){
            log.error("支付宝小程序获取用户信息失败：{}",e.getMessage());
        }
        //2、解密 获取手机号

        String jsonStr = req.getJsonStr();
        String response = JSON.parseObject(jsonStr).getString("response");
        //1. 获取验签和解密所需要的参数
        Map<String, String> openapiResult = JSON.parseObject(jsonStr,new TypeReference<Map<String, String>>() {}, Feature.OrderedField);
        String signType = "RSA2";
        String charset = "UTF-8";
        String encryptType = "AES";
        String sign = openapiResult.get("sign");
        String content = openapiResult.get("response");
        //判断是否为加密内容
        boolean isDataEncrypted = !content.startsWith("{");
        boolean signCheckPass = false;
        //2. 验签
        String signContent = content;
//        String signVeriKey = "你的小程序对应的支付宝公钥（为扩展考虑建议用appId+signType做密钥存储隔离）";
//        String decryptKey = "你的小程序对应的加解密密钥（为扩展考虑建议用appId+encryptType做密钥存储隔离）";//如果是加密的报文则需要在密文的前后添加双引号

        //        //你的小程序对应的支付宝公钥（为扩展考虑建议用appId+signType做密钥存储隔离）
        String signVeriKey = AlipayConfig.ALIPAY_PUB_KEY;
//        //你的小程序对应的加解密密钥（为扩展考虑建议用appId+encryptType做密钥存储隔离）
//         String decryptKey = "XjPnHedjkscci24/nvFHoA==";
        String decryptKey = AlipayConfig.DECRYPT_KEY;

        if (isDataEncrypted) {
            signContent = "\"" + signContent + "\"";
        } try {
            signCheckPass = AlipaySignature.rsaCheck(signContent, sign, signVeriKey, charset, signType);
        } catch (AlipayApiException e) {
            // 验签异常, 日志
            log.info("验签失败：{}",e.getMessage());
        } if (!signCheckPass) {
            //验签不通过（异常或者报文被篡改），终止流程（不需要做解密）
            throw new ServiceException("验签失败");
        }
        //3. 解密
        String plainData = null;
        if (isDataEncrypted) {
            try {
                plainData = AlipayEncrypt.decryptContent(content, encryptType, decryptKey, charset);
            } catch (AlipayApiException e) {
                //解密异常, 记录日志
                log.info("解密异常：{}",e.getMessage());

            }} else {
            //{"code":"10000","msg":"Success","mobile":"19535569112"}
            plainData = content;
        }
        String phone = "";
        if(StringUtils.isNoneBlank(plainData)){
            JSONObject entries = JSONUtil.parseObj(plainData);
            if(entries.containsKey("msg") && entries.getStr("msg").equals("Success")){
                phone = entries.getStr("mobile");
            } else {
                log.info("解密手机号失败: {}", plainData);
            }
        }

//        String jsonStr = req.getJsonStr();
//        String response = JSON.parseObject(jsonStr).getString("response");
//        //1. 获取验签和解密所需要的参数
//        Map<String, String> openapiResult = JSON.parseObject(jsonStr,
//                new TypeReference<Map<String, String>>() {
//                }, Feature.OrderedField);
//        String signType = "RSA2";
//        String charset = "UTF-8";
//        String encryptType = "AES";
//        String sign = openapiResult.get("sign");
//        String content = openapiResult.get("response");
//
//        //如果密文的
//        boolean isDataEncrypted = !content.startsWith("{");
//        boolean signCheckPass = false;
//
//        //2. 验签
//        String signContent = content;
//        //你的小程序对应的支付宝公钥（为扩展考虑建议用appId+signType做密钥存储隔离）
//        String signVeriKey = AlipayConfig.ALIPAY_PUB_KEY;
//        //你的小程序对应的加解密密钥（为扩展考虑建议用appId+encryptType做密钥存储隔离）
//        String decryptKey = "XjPnHedjkscci24/nvFHoA==";
//
//        //如果是加密的报文则需要在密文的前后添加双引号
//        if (isDataEncrypted) {
//            signContent = "\"" + signContent + "\"";
//        }
//        try {
//            signCheckPass = AlipaySignature.rsaCheck(signContent, sign, signVeriKey, charset, signType);
//        } catch (AlipayApiException e) {
//            //验签异常, 日志
//            log.info("验签失败：{}",e.getMessage());
//        }
//        if(!signCheckPass) {
//            //验签不通过（异常或者报文被篡改），终止流程（不需要做解密）
//            throw new ServiceException("验签失败");
//        }
//
//        //3. 解密
//        String plainData = null;
//        if (isDataEncrypted) {
//            try {
//                plainData = AlipayEncrypt.decryptContent(content, encryptType, decryptKey, charset);
//            } catch (AlipayApiException e) {
//                log.info("解密异常：{}",e.getMessage());
//                //解密异常, 记录日志
//                throw new ServiceException("解密异常");
//            }
//        } else {
//            plainData = content;
//        }

        //微信获取不到手机号
        if (StrUtil.isEmpty(phone)) {
            return JSONResult.error(CommonErrorEnum.GET_USER_PHONE_ERROR);
        }
        //3.判断手机号在数据库是否存在
        User user = lambdaQuery().eq(User::getPhone, phone).eq(User::getIsDel, DeleteStatusEnum.NORMAL.value).one();
        if (user == null) {
            //新用户 注册进数据库
            user = new User();
            user.setNo(getUserNo());
            // user.setReg(1);
            //昵称随机
            user.setNickname(StringUtils.isBlank(nickName) ? String.format("昵称%s", RandomUtil.randomString(6)) : nickName);
            //头像默认
            user.setAvatar(avatar);
            // user.setOpenId(openid);
            user.setPhone(phone);
            // user.setChannelId("2");
            save(user);
            // 添加用户注册渠道表
            UserOpenInfo userOpenInfo = new UserOpenInfo();
            userOpenInfo.setUserId(user.getId());
            userOpenInfo.setReg(RegSourceEnum.ALIPAY.value());
            userOpenInfo.setOpenId(openid);
            userOpenInfo.setAddTime(LocalDateTime.now());
            iUserOpenInfoService.save(userOpenInfo);
        }
        //4.响应数据
        AlipayPhoneDto res = AlipayPhoneDto.create().setId(user.getId()).setPhone(user.getPhone());
        return JSONResult.ok(res);
    }

    /**
     *  第三方实名认证 三要素
     * @param phone 手机号
     * @param name 真实姓名
     * @param idCard 身份证号码
     * @return
     */
    private Boolean doRealNameVerification(String phone,String name,String idCard,Map<String, String> paramMap) throws Exception {
        String authCode = paramMap.get(CommonEnum.AUTO_CODE.name());
        com.aliyun.dytnsapi20200217.Client client = createClient(paramMap);
        com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationRequest threeElementsVerificationRequest = new com.aliyun.dytnsapi20200217.models.ThreeElementsVerificationRequest()
                .setAuthCode(authCode)
                .setInputNumber(phone)
                //可以设置数据传输模式 目前采用未加密模式。可以采用md5
                .setMask("NORMAL")
                .setCertCode(idCard)
                .setName(name);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            ThreeElementsVerificationResponse threeElementsVerificationResponse = client.threeElementsVerificationWithOptions(threeElementsVerificationRequest, runtime);
            log.info("阿里云实名认证三要素响应：{}",JSONUtil.toJsonStr(threeElementsVerificationResponse));

            ThreeElementsVerificationResponseBody body = threeElementsVerificationResponse.getBody();
            Integer statusCode = threeElementsVerificationResponse.getStatusCode();
            ThreeElementsVerificationResponseBody.ThreeElementsVerificationResponseBodyData data = body.getData();
            String code = body.getCode();
            if(code.equals("OK") && statusCode == HttpStatus.HTTP_OK && data.getIsConsistent() == 1){
                return true;
            }
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.info("实名认证错误：{}",error.getMessage());
            // 诊断地址
            log.info("实名认证错误诊断地址：{}",error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            log.info("实名认证错误：{}",error.getMessage());
            // 诊断地址
            log.info("实名认证错误诊断地址：{}",error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return false;
    }

    /**
     * <b>description</b> :
     * <p>使用AK&amp;SK初始化账号Client</p>
     * @return Client
     *
     * @throws Exception
     */
    private com.aliyun.dytnsapi20200217.Client createClient(Map<String, String> paramMap) throws Exception {
        String accessKeyId = paramMap.get(CommonEnum.ALI_KEY.name());
        String accessKeySecret = paramMap.get(CommonEnum.ALI_ACCESS_SECRET.name());
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(accessKeyId)
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(accessKeySecret);
        // Endpoint 请参考 https://api.aliyun.com/product/Dytnsapi
        config.endpoint = "dytnsapi.aliyuncs.com";
        return new com.aliyun.dytnsapi20200217.Client(config);
    }

    /**
     * 获取客户编码，8位
     * <AUTHOR> to 2024/8/22
     */
    private synchronized Integer getUserNo() {
        User maxUser = lambdaQuery().orderByDesc(User::getNo).last(" limit 1").one();
        if (null == maxUser) {
            return 10000000;
        } else {
            return maxUser.getNo() + 1;
        }
    }
}
