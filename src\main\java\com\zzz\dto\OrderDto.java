package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("用户订单实体")
public class OrderDto {

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "提货人姓名，RSA加密")
    private String userName;

    @ApiModelProperty(value = "提货人手机，RSA加密")
    private String userPhone;

    @ApiModelProperty(value = "1待支付2已支付待处理3待提取4已提取5退款中6已退款7已取消")
    private Integer orderState;

    @ApiModelProperty(value = "支付状态，0未支付，1已支付")
    private Integer payStatus;

    @ApiModelProperty(value = "提货人身份证，RSA加密")
    private String userCard;

    @ApiModelProperty(value = "提货网点id")
    private Integer branchId;

    @ApiModelProperty(value = "用途id ")
    private Integer purpose;

    @ApiModelProperty(value = "网点名称")
    private String branchName;

    @ApiModelProperty(value = "网点取货地址")
    private String branchAddr;

    @ApiModelProperty(value = "提取日期：2024-06-30")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate extractDate;

    @ApiModelProperty(value = "提取时间： 8月21日 周三 09:00-18:00 ")
    private String extractTime;

    @ApiModelProperty(value = "用途选择其他，此项必填")
    private String purposeDescri;

    @ApiModelProperty(value = "优惠券id")
    private Integer couponId;

    @ApiModelProperty(value = "优惠券抵扣金额")
    private BigDecimal couponAmount;

    @ApiModelProperty(value = "外币兑换总金额（公式：兑换外币金额数量1200USD*汇率7.0000（汇率的意思就是1USD卖人民币7.000元））")
    private BigDecimal orderTotalAmount;

    @ApiModelProperty(value = "支付方式：WX 微信")
    private String payType;

    @ApiModelProperty(value = " 实际金额，如微信支付（公式：外币换算人民币总额 +（加急费+零钱包）-优惠券）")
    private BigDecimal orderPayAmount;

    @ApiModelProperty(value = "加急费用")
    private BigDecimal urgentFees;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(value = "取货码")
    private String withdrawaCode;

    @ApiModelProperty(value = "零钱包ID，非必填")
    private Integer walletId;

    @ApiModelProperty(value = "零钱包数量，非必填")
    private Integer walletCount;

    @ApiModelProperty(value = "零钱包总额，人民币")
    private BigDecimal walletAmount;

    @ApiModelProperty(value = "零钱包外币面值")
    private BigDecimal walletParvalue;

    @ApiModelProperty(value = "零钱包外币总额")
    private BigDecimal walletTotalAmt;

    @ApiModelProperty(value = " 币种ID")
    private Integer curId;

    @ApiModelProperty(value = "币种代码")
    private String curCode;

    @ApiModelProperty(value = "币种名称")
    private String curName;

    @ApiModelProperty(value = "币种汇率")
    private BigDecimal curRate;

    @ApiModelProperty(value = "退款状态，1退款成功，2拒绝退款，3 审核通过=同意退款，4，待审核=客户已申请")
    private String refundStatus;

    @ApiModelProperty(value = "取消订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "申请退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime applyRefundTime;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime refendTime;

    @ApiModelProperty(value = "完成时间=提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @ApiModelProperty(value = "支付成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "支付有效时间(倒计时时);创建时间加上15分钟后的时间，只有待支付状态才有；时间戳转日期工具：https://www.beijing-time.org/shijianchuo/")
    private String payValidTime;

    @ApiModelProperty(value = "确认退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime agreeRefundTime;

    @ApiModelProperty(value = "退款审核备注，拒绝退款必填")
    private String refundRemark;

    @ApiModelProperty(value = "外币金额，数量；汇率基本单位整倍数")
    private BigDecimal usbAmt;

    @ApiModelProperty(value = "外币总金额")
    private BigDecimal usbTotalAmt;

}
