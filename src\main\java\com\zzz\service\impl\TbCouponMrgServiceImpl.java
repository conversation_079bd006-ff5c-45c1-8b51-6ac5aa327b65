package com.zzz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.BranchDto;
import com.zzz.dto.BranchPageDto;
import com.zzz.dto.CouponDto;
import com.zzz.dto.CouponPageDto;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbCouponReceive;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbCouponMrgMapper;
import com.zzz.service.ITbCouponMrgService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.service.ITbCouponReceiveService;
import com.zzz.util.CusNumberUtil;
import com.zzz.util.SpringBeanUtil;
import com.zzz.vo.CouponPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 平台优惠券 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class TbCouponMrgServiceImpl extends ServiceImpl<TbCouponMrgMapper, TbCouponMrg> implements ITbCouponMrgService {

    @Autowired
    private ITbCouponReceiveService tbCouponReceiveService;

    @Autowired
    private TbCouponMrgMapper tbCouponMrgMapper;

    @Override
    public CouponPageDto getCouponPage(CouponPageVo req) {
        if(CusNumberUtil.isNumber(req.getPage())){
            throw new ServiceException(CommonErrorEnum.PAGE_CODE_IS_NULL);
        }
        if(CusNumberUtil.isNumber(req.getSize())){
            throw new ServiceException(CommonErrorEnum.PAGE_SIZE_IS_NULL);
        }
        LambdaQueryWrapper<TbCouponMrg> queryWrapper = new LambdaQueryWrapper<>();
        //查询条件
        // queryWrapper.eq(TbCouponMrg::getExpired,0);
        queryWrapper.isNotNull(TbCouponMrg::getEndTime);
        queryWrapper.ge(TbCouponMrg::getEndTime, new Date());

        queryWrapper.eq(TbCouponMrg::getCouponStatus,1);

        //过滤已领取过的优惠券
        if(null != req.getUserId() && req.getUserId() > 0){
            //获取用户已领取优惠券列表
            List<TbCouponReceive> list = tbCouponReceiveService.getUserRecCouponList(req.getUserId());
            if(!list.isEmpty()){
                queryWrapper.notIn(TbCouponMrg::getId,list.stream().map(item->item.getCouponId()).collect(Collectors.toList()));
            }
        }

        //倒序
        queryWrapper.orderByDesc(TbCouponMrg::getId);
        //分页查询数据
        Page<TbCouponMrg> page = new Page<>(req.getPage(), req.getSize());
        IPage<TbCouponMrg> dataIPage = tbCouponMrgMapper.selectPage(page, queryWrapper);
        List<CouponDto> list = SpringBeanUtil.copyProperties(dataIPage.getRecords(), CouponDto.class);

        //返回查询结果
        return CouponPageDto.builder()
                .totalPage(dataIPage.getPages())
                .totalRecord(dataIPage.getTotal())
                .list(list)
                .build();
    }

    @Override
    public TbCouponMrg getCouponById(Integer coupenId) {
        return tbCouponMrgMapper.selectById(coupenId);
    }

    @Override
    @Transactional
    public void subCouponCountById(Integer coupenId) {
        TbCouponMrg couponById = getCouponById(coupenId);
        //扣减库存 TODO
        tbCouponMrgMapper.subCouponCountById(couponById.getId());
    }


}
