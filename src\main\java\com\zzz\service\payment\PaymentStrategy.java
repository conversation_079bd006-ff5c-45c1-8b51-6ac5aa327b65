package com.zzz.service.payment;

import com.zzz.dto.PaymentParamDto;
import com.zzz.entity.TbOrder;

import java.math.BigDecimal;

/**
 * 支付策略接口
 * 定义不同支付方式需要实现的方法
 */
public interface PaymentStrategy {

    /**
     * 获取支付类型
     * @return 支付类型
     */
    String getPaymentType();

    /**
     * 创建支付订单
     * @param order 订单信息
     * @param openId 用户openId
     * @return 支付参数
     */
    PaymentParamDto createPayment(TbOrder order, String openId);

    /**
     * 查询支付订单状态
     * @param orderNo 订单号
     * @param outOrderNo 外部订单号/交易号
     * @return 支付状态
     */
    boolean queryPaymentStatus(String orderNo, String outOrderNo);

    /**
     * 关闭支付订单
     * @param orderNo 订单号
     * @param outOrderNo 外部订单号/交易号
     * @return 是否成功
     */
    boolean closePayment(String orderNo, String outOrderNo);

    /**
     * 申请退款
     * @param orderNo 订单号
     * @param outOrderNo 外部订单号/交易号
     * @param refundAmount 退款金额
     * @param totalAmount 订单总金额
     * @param refundReason 退款原因
     * @return 是否成功
     */
    boolean refund(String orderNo, String outOrderNo, BigDecimal refundAmount, BigDecimal totalAmount, String refundReason);
}
