package com.zzz.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：微信获取手机号 响应
 * @ Version:     1.0
 */
@Data
@Accessors(chain = true)
public class WxPhoneDto implements Serializable {

    @ApiModelProperty(name = "id", value = "用户id", required = true,example = "1")
    private Integer id;

    @ApiModelProperty(name = "phone", value = "注册手机号", required = true,example = "13666666666")
    private String phone;

    public static WxPhoneDto create() {
        return new WxPhoneDto();
    }
}
