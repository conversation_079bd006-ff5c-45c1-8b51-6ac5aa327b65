package com.zzz.service.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付策略工厂
 * 用于根据支付类型获取对应的支付策略
 */
@Component
public class PaymentStrategyFactory {
    
    @Autowired
    private List<PaymentStrategy> paymentStrategies;
    
    private final Map<String, PaymentStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        for (PaymentStrategy strategy : paymentStrategies) {
            strategyMap.put(strategy.getPaymentType(), strategy);
        }
    }
    
    /**
     * 获取支付策略
     * @param paymentType 支付类型
     * @return 支付策略
     */
    public PaymentStrategy getStrategy(String paymentType) {
        return strategyMap.get(paymentType);
    }
}
