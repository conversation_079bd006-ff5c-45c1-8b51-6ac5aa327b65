<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.BbDistributionOrgMapper">

    <select id="getDistributionByPid" resultType="com.zzz.entity.BbDistributionOrg">
        SELECT
            *
        FROM
            bb_distribution_org distri
                LEFT JOIN bb_org_branch branch ON distri.id = branch.cus_id
        WHERE
            branch.id = #{outId}
    </select>

    <select id="getDistributionByBId" resultType="com.zzz.dto.BranchOrgDto">
        SELECT
            orgb.branch_code,orgb.branch_name,dorg.org_code,dorg.org_name
        FROM
            tb_branch branch
                LEFT JOIN bb_org_branch orgb ON branch.out_id = orgb.id
                LEFT JOIN bb_distribution_org dorg ON dorg.id = orgb.cus_id
        WHERE
            branch.id = #{branchId}
    </select>

</mapper>
