package com.zzz.controller.v1;

import com.zzz.dto.CurrencyDto;
import com.zzz.dto.CurrencyPageDto;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbcurrencyService;
import com.zzz.vo.CurrencyPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Api(tags = "币种管理")
@Slf4j
@RestController
@RequestMapping("/api/v1/currency")
public class CurrencyController {

    @Autowired
    private ITbcurrencyService iTbcurrencyService;

    /**
     * 热门币种
     */
    @ApiOperation("热门币种")
    @GetMapping("/get_hot")
    public JSONResult<List<CurrencyDto>> getCurrencyHot(){
        return JSONResult.ok(iTbcurrencyService.getCurrencyHot());
    }

    /**
     * 币种列表（分页）
     */
    @ApiOperation("币种列表（分页）")
    @PostMapping("/get_currency_page")
    public JSONResult<CurrencyPageDto> getCurrencyPage(@RequestBody CurrencyPageVo currencyPageVo){
        return JSONResult.ok(iTbcurrencyService.getCurrencyPage(currencyPageVo));
    }

    /**
     * 根据网点id获取币种列表
     */
    @ApiOperation("根据网点id获取币种列表")
    @GetMapping("/get_currency_list_by_id")
    public JSONResult<List<CurrencyDto>> getCurrencyListById(@RequestParam("bId") String bId){
        return JSONResult.ok(iTbcurrencyService.getCurrencyListById(bId));
    }

    /**
     * 根据币种ID 获取币种信息
     */
    @ApiOperation("获取币种详情(网点ID 和 币种代码 )")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currency", value = "币种代码", required = true, example = "USD"),
            @ApiImplicitParam(name = "branchId", value = "网点ID", required = false, example = "1")
    })
    @GetMapping("/get_currency_by_id")
    public JSONResult<CurrencyDto> getCurrencyByCur(
            @RequestParam("currency") String currency,
            @RequestParam(value = "branchId",required = false) Integer branchId
    ){
        return JSONResult.ok(iTbcurrencyService.getCurrencyByCur(currency,branchId));
    }

}
