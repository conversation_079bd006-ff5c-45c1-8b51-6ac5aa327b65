package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.entity.TbTags;
import com.zzz.entity.TbTagsTaggings;
import com.zzz.mapper.TbTagsTaggingsMapper;
import com.zzz.service.ITbTagsTaggingsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 网点绑定标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class TbTagsTaggingsServiceImpl extends ServiceImpl<TbTagsTaggingsMapper, TbTagsTaggings> implements ITbTagsTaggingsService {

    @Autowired
    private TbTagsTaggingsMapper tbTagsTaggingsMapper;

    @Override
    public List<TbTags> selectListBySiteId(Integer id) {
        //关联查询
        return tbTagsTaggingsMapper.selectListBySiteId(id);
//        return tbTagsTaggingsMapper.selectList(new LambdaQueryWrapper<TbTagsTaggings>()
//                .eq(TbTagsTaggings::getSiteId,id)
//        );
    }

    @Override
    public List<TbTagsTaggings> getSitesBytagId(String tagId) {
        return tbTagsTaggingsMapper.selectList(new LambdaQueryWrapper<TbTagsTaggings>()
                .eq(TbTagsTaggings::getTagId,tagId));
    }
}
