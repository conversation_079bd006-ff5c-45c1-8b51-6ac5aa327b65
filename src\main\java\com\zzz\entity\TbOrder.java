package com.zzz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_order")
@ApiModel(value = "TbOrder对象", description = "")
public class TbOrder implements Serializable {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@ApiModelProperty(value = "用户id")
	private Integer userId;

	@ApiModelProperty(value = "订单号")
	private String orderNumber;

	@ApiModelProperty(value = "提货人姓名")
	private String userName;

	@ApiModelProperty(value = "提货人手机")
	private String userPhone;

	@ApiModelProperty(value = "1待支付2已支付待处理3待提取4已提取5退款中6已退款7已取消")
	private Integer orderState;

	@ApiModelProperty(value = "支付状态，0未支付，1已支付")
	private Integer payStatus;

	@ApiModelProperty(value = "提取状态 1：待提取，2：已提取，3：已退款待退回，4：已退款已退回")
	private Integer extractStatus;

	@ApiModelProperty(value = "提货人身份证")
	private String userCard;

	@ApiModelProperty(value = "提货网点id")
	private Integer branchId;

	@ApiModelProperty(value = "用途id ")
	private Integer purpose;

	@ApiModelProperty(value = "网点名称")
	private String branchName;

	@ApiModelProperty(value = "网点取货地址")
	private String branchAddr;

	@ApiModelProperty(value = "提取日期：2024-06-30")
	private LocalDate extractDate;

	@ApiModelProperty(value = "提取时间： 8月21日 周三 09:00-18:00 ")
	private String extractTime;

	@ApiModelProperty(value = "用途选择其他，此项必填")
	private String purposeDescri;

	@ApiModelProperty(value = "优惠券id")
	private Integer couponId;

	@ApiModelProperty(value = "优惠券抵扣金额")
	private BigDecimal couponAmount;

	@ApiModelProperty(value = "外币兑换总金额")
	private BigDecimal orderTotalAmount;

	@ApiModelProperty(value = "支付方式：WX 微信")
	private String payType;

	@ApiModelProperty(value = "实际金额，如微信支付")
	private BigDecimal orderPayAmount;

	@ApiModelProperty(value = "外币金额，数量；汇率基本单位整倍数")
	private BigDecimal usbAmt;

	@ApiModelProperty(value = "加急费用")
	private BigDecimal urgentFees;

	@ApiModelProperty(value = "手续费")
	private BigDecimal fee;

	@ApiModelProperty(value = "订单利润")
	private BigDecimal orderProfit;

	@ApiModelProperty(value = "核销人员ID")
	private Integer operationUserid;

	@ApiModelProperty(value = "核销人名称")
	private String operationName;

	@ApiModelProperty(value = "取货码")
	private String withdrawaCode;

	@ApiModelProperty(value = "零钱包ID，非必填")
	private String walletId;

	@ApiModelProperty(value = "零钱包数量，非必填")
	private Integer walletCount;

	@ApiModelProperty(value = "零钱包外币面额")
	private BigDecimal walletParvalue;

	@ApiModelProperty(value = "零钱包总额")
	private BigDecimal walletAmount;

	@ApiModelProperty(value = "删除状态。1删除，0正常 ")
	private Integer isDel;

	@ApiModelProperty(value = "下单时间")
	private LocalDateTime createTime;

	@ApiModelProperty(value = "修改时间")
	private LocalDateTime updateTime;

	@ApiModelProperty(value = "退款状态，1退款成功，2拒绝退款，3同意退款，4，客户已申请")
	private String refundStatus;

	@ApiModelProperty(value = "退款时间")
	private LocalDateTime refendTime;

	@ApiModelProperty(value = "完成时间=提取时间")
	private LocalDateTime finishTime;

	@ApiModelProperty(value = "支付成功时间")
	private LocalDateTime payTime;

	@ApiModelProperty(value = " 币种ID")
	private Integer curId;

	@ApiModelProperty(value = "币种代码")
	private String curCode;

	@ApiModelProperty(value = "币种名称")
	private String curName;

	@ApiModelProperty(value = "币种汇率")
	private BigDecimal curRate;

	@ApiModelProperty(value = "取消订单时间")
	private LocalDateTime cancelTime;

	@ApiModelProperty(value = "申请退款时间")
	private LocalDateTime applyRefundTime;

	@ApiModelProperty(value = "确认退款时间")
	private LocalDateTime agreeRefundTime;

	@ApiModelProperty(value = "退款审核备注，拒绝退款必填")
	private String refundRemark;

	@ApiModelProperty(value = "系统备注")
	private String systemRemark;

	@ApiModelProperty(value = "外部订单号/交易号")
	private String outOrderNo;

	@ApiModelProperty(value = "冻结库存信息；第三方返回信息")
	private String freezStock;

	@ApiModelProperty(value = "订单所属渠道。0max.1bamboo")
	private String channelType;

}
