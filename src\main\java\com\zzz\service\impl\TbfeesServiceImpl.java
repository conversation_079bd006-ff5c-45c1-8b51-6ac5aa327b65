package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.entity.Tbfees;
import com.zzz.mapper.TbfeesMapper;
import com.zzz.service.ITbfeesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 手续费设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Service
public class TbfeesServiceImpl extends ServiceImpl<TbfeesMapper, Tbfees> implements ITbfeesService {

    @Autowired
    private TbfeesMapper tbfeesMapper;

    @Override
    public Tbfees getFeesByBranchId(Integer id) {
        return tbfeesMapper.selectOne(
                new LambdaQueryWrapper<Tbfees>()
                        .eq(Tbfees::getBranchId,id)
        );
    }
}
