package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-08-10
 **/
@Data
@ApiModel("支付宝授权参数")
public class AlipayAuthVo {

    @ApiModelProperty(name = "auth_code", value = "登录时获取的 auth_code，可通过my.getAuthCode获取", required = true, example = "0e1hcK000waxjS14Tn400ho09t4hcK0C")
    @NotBlank
    private String autCode;
}
