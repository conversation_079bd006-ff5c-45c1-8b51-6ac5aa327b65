package com.zzz.response;


import com.zzz.emuns.CommonErrorEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.io.Serializable;

/**
 * 自定义响应数据结构
 *
 * @param <T>
 */
@Getter
@Setter
@ToString
@Slf4j
public class JSONResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    // 响应业务状态
    @ApiModelProperty(name = "code", value = "响应码，返回200标识成功，否则为失败", required = true, example = "200")
    private Integer code;

    // 响应消息
    @ApiModelProperty(name = "msg", value = "响应消息，非200错误码，返回对应错误信息", required = true, example = "OK")
    private String msg;

    // 响应中的数据
    @ApiModelProperty(name = "data", value = "响应具体数据，JSON格式", required = true, example = "{}")
    private T data;

    public JSONResult(T data) {
        this.code = 200;
        this.msg = "OK";
        this.data = data;
    }

    public JSONResult(Integer error_code, String error_msg, T data) {
        this.code = error_code;
        this.msg = error_msg;
        this.data = data;
    }

    public static <T> JSONResult<T> ok(T data) {
        return new JSONResult(data);
    }

    public static <T> JSONResult<T> ok() {
        return new JSONResult(null);
    }

    public static JSONResult error(String error_msg) {
        return new JSONResult(HttpStatus.INTERNAL_SERVER_ERROR.value(), error_msg, null);
    }

    public static JSONResult error(Integer error_code, String error_msg) {
        return new JSONResult(error_code, error_msg, null);
    }

    public static JSONResult error(CommonErrorEnum commonErrorEnum) {
        return new JSONResult(commonErrorEnum.getErrorCode(), commonErrorEnum.getErrorMsg(), null);
    }

}
