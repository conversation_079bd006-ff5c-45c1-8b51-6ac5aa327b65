<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.TbCouponReceiveMapper">

    <select id="getUserCouponPage" resultType="com.zzz.dto.UserCouponDto">
        SELECT
            rec.id recId,
            rec.apply_state applyState,
            mrg.*
        FROM
            tb_coupon_receive rec
                LEFT JOIN tb_coupon_mrg mrg ON rec.coupon_id = mrg.id
        WHERE rec.apply_state = '2' AND rec.user_id = #{userId}
    </select>


    <select id="getCouponByRecId" resultType="com.zzz.entity.TbCouponMrg">
        SELECT
            mrg.*
        FROM
            tb_coupon_receive rec
                LEFT JOIN tb_coupon_mrg mrg ON rec.coupon_id = mrg.id
        WHERE
            rec.id = #{couponId} AND rec.apply_state = 2
    </select>

</mapper>
