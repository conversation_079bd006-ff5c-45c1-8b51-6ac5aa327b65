package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 网点汇率表
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value="Tbexchangerate对象", description="网点汇率表")
    public class Tbexchangerate implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "记录ID")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "网点编码")
    private String sitecode;

            @ApiModelProperty(value = "网点名称")
    private String sitename;

            @ApiModelProperty(value = "币种编码")
    private String currency;

            @ApiModelProperty(value = "币种名称")
    private String curname;

            @ApiModelProperty(value = "买入价")
    private BigDecimal buyprice;

            @ApiModelProperty(value = "卖出价")
    private BigDecimal saleprice;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime exdate;

            @ApiModelProperty(value = "状态；0停用、1可用")
    private Integer status;

            @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;


}
