package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订单支付参数")
public class PayOrderVo {
    @ApiModelProperty(value = "订单编号", required = true, example = "4644564")
    private String orderno;

    @ApiModelProperty(value = "支付方式", required = true, example = "WECHAT, ALIPAY, UNIONPAY")
    private String payType;
}