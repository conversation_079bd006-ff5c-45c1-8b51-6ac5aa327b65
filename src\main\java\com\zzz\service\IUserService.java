package com.zzz.service;

import com.zzz.dto.AlipayAuthDto;
import com.zzz.dto.AlipayPhoneDto;
import com.zzz.dto.LoginPhoneDto;
import com.zzz.dto.UserDto;
import com.zzz.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;
import com.zzz.vo.*;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
public interface IUserService extends IService<User> {

    /**
     * 微信授权
     * @param req
     * @return
     */
    JSONResult wxAuth(WxAuthVo req);

    /**
     * 微信获取手机号
     * @param req
     * @return
     */
    JSONResult wxPhone(WxPhoneVo req);

    /**
     * 手机号登录
     * @param req
     * @return
     */
    JSONResult loginPhone(LoginPhoneVo req,Integer regSource);

    /**
     * 首次登录 验证手机号
     * @param req
     * @return
     */
    JSONResult validPhone(SmsValidVo req,Integer regSource);

    UserDto getUserDetail();

    JSONResult doRealnameAuth(RealNameAuthVo realNameAuthVo) throws Exception;

    JSONResult<LoginPhoneDto> loginH5(LoginH5Vo req);

    JSONResult<AlipayAuthDto> alipayAuth(AlipayAuthVo req);

    JSONResult<AlipayPhoneDto> alipayPhone(AlipayPhoneVo req);
}
