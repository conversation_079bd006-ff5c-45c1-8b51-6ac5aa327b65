package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("用户实名认证参数")
public class RealNameAuthVo {

    @ApiModelProperty(value = "姓名，RSA加密传输",required = true,example = "张三")
    private String realname;

    @ApiModelProperty(value = "身份证号码，RSA加密传输",required = true,example = "****************")
    private String idcard;
}
