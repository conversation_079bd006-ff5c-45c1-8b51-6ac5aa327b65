package com.zzz.mapper;

import com.zzz.dto.BranchOrgDto;
import com.zzz.entity.BbDistributionOrg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * <p>
 * 客户表（Bamboo） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface BbDistributionOrgMapper extends BaseMapper<BbDistributionOrg> {

    BbDistributionOrg getDistributionByPid(@Param("outId") String outId);

    BranchOrgDto getDistributionByBId(@Param("branchId") Integer branchId);
}
