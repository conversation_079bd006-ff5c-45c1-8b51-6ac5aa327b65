package com.zzz.emuns;

/**
 * packageName com.zzz.entity.vo
 *订单状态枚举
 * <AUTHOR>
 * @version JDK 11
 * @date 2024/6/17
 */
public enum OrderStatusEnum {
 
    /**
     * 待付款
     */
    WAIT_PAYMENT(1),
 
    /**
     * 已支付待处理
     */
    WAIT_DELIVER(2),
 
    /**
     * 待提取
     */
    WAIT_RECEIVE(3),
 
    /**
     * 已提取,已完成
     */
    FINISH(4),
 
    /**
     * 退款中
     */
    REFUND_PROGRESS(5),
    /**
     * 已退款
     */
    REFUNDED(6),
    /**
     * 已取消
     */
    CANCEL(7),
    ;
    /**
     * 订单状态
     */
    public final int value;
 
    OrderStatusEnum(int value) {
        this.value = value;
    }
 
    public static Object getByValue(Integer status) {
        for(OrderStatusEnum OrderStatusEnum:OrderStatusEnum.values()){
            if(OrderStatusEnum.value==status){
                return OrderStatusEnum;
            }
        }
        return null;
    }
}
