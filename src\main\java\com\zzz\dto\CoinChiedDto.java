package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-23
 **/
@Data
@ApiModel("零钱包包含面额实体")
public class CoinChiedDto {


    @ApiModelProperty(value = "主键，唯一")
    private Integer id;

    @ApiModelProperty(value = "零钱包ID")
    private Integer coinId;

    @ApiModelProperty(value = "面额")
    private Integer aclValue;

    @ApiModelProperty(value = "数量")
    private Integer num;

    @ApiModelProperty(value = "备注")
    private String remark;

}
