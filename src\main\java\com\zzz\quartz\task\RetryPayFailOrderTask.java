package com.zzz.quartz.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.MaxAPI;
import com.zzz.component.max.response.QueryOrderResponse;
import com.zzz.emuns.ChannelTypeEnum;
import com.zzz.emuns.OrderPayStatusEnum;
import com.zzz.emuns.OrderStatusEnum;
import com.zzz.entity.TbOrder;
import com.zzz.service.ITbCouponReceiveService;
import com.zzz.service.ITbOrderImgService;
import com.zzz.service.ITbOrderService;
import com.zzz.util.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 重试支付失败订单
 * <AUTHOR> to 2024/10/13
 */
@Slf4j
public class RetryPayFailOrderTask {

    /**
     * 获取支付失败订单，每10s执行一次
     * <AUTHOR> to 2024/9/15
     */
    public void run() {
        ITbOrderService tbOrderService = SpringContextUtils.getContext().getBean(ITbOrderService.class);
        ITbOrderImgService iTbOrderImgService = SpringContextUtils.getContext().getBean(ITbOrderImgService.class);
        ITbCouponReceiveService iTbCouponReceiveService = SpringContextUtils.getContext().getBean(ITbCouponReceiveService.class);
        MaxAPI maxAPI = SpringContextUtils.getContext().getBean(MaxAPI.class);
        // TODO:目前只对max订单做反查处理
        List<TbOrder> orderList = tbOrderService.list(Wrappers.<TbOrder>lambdaQuery()
                .eq(TbOrder::getChannelType, ChannelTypeEnum.MAX.value())
                .eq(TbOrder::getPayStatus, OrderPayStatusEnum.PAY_FAIL.value)
        );
        if (CollUtil.isNotEmpty(orderList)) {
            for (TbOrder tbOrder : orderList) {
                try {
                    // 查询max订单信息 TODO: 如果有在线支付，还需要查询在线支付状态
                    ResponseData responseData = maxAPI.queryOrder(tbOrder.getOrderNumber());
                    if (null != responseData && "200".equals(responseData.getCode())) {
                        QueryOrderResponse queryOrderResponse = JSONUtil.toBean(responseData.getData(), QueryOrderResponse.class);
                        // 判断max订单是否成功：onlineStatus为0（待打包），wsOptype为0（正常）
                        if ("0".equals(queryOrderResponse.getOnlineStatus()) && "0".equals(queryOrderResponse.getWsOptype())) {
                            // 修改订单状态
                            tbOrder.setPayStatus(OrderPayStatusEnum.PAY.value);
                            tbOrder.setOrderState(OrderStatusEnum.WAIT_DELIVER.value);
                            tbOrder.setPayTime(LocalDateTime.now());
                            tbOrder.setUpdateTime(LocalDateTime.now());
                            tbOrderService.updateById(tbOrder);

                            // 修改水单图片
                            iTbOrderImgService.addOrUpdateMaxIce(tbOrder.getOrderNumber(), queryOrderResponse.getIcedownloadurl(),
                                    queryOrderResponse.getIceimgurl(), queryOrderResponse.getIcepdfurl());

                            // 使用优惠券
                            iTbCouponReceiveService.useById(tbOrder.getCouponId());
                        }
                    }
                } catch (Exception e) {
                    log.error("反查订单失败, tbOrder:{}, e: {}", JSONUtil.toJsonStr(tbOrder), e.getMessage(), e);
                }
            }
        }
    }
}
