package com.zzz.controller.v1;

import com.zzz.dto.UserCouponPageDto;
import com.zzz.dto.UserDto;
import com.zzz.dto.UserInformationDto;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbCouponReceiveService;
import com.zzz.service.IUserInformationService;
import com.zzz.service.IUserService;
import com.zzz.vo.RealNameAuthVo;
import com.zzz.vo.UserCouponPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  用户信息管理
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Api(tags = "用户信息")
@Slf4j
@RestController
@RequestMapping("/api/v1/user")
public class UserController {

    @Autowired
    private IUserService iUserService;

    @Autowired
    private ITbCouponReceiveService iTbCouponReceiveService;

    @Autowired
    private IUserInformationService iUserInformationService;

    /**
     * 获取用户基础信息
     */
    @ApiOperation("获取用户基础信息")
    @GetMapping("/detail")
    public JSONResult<UserDto> getUserDetail(){
        return JSONResult.ok(iUserService.getUserDetail());
    }

    /**
     * 实名认证
     */
    @ApiOperation(value="实名验证接口",notes="（临时方案）暂未与第三方接口对接验证；")
    @PostMapping("/do_realname_auth")
    public JSONResult doRealnameAuth(@RequestBody RealNameAuthVo realNameAuthVo) throws Exception {
        return iUserService.doRealnameAuth(realNameAuthVo);
    }

    /**
     * 获取当前人员实名认证信息
     */
    @ApiOperation("获取当前人员实名认证信息")
    @PostMapping("/get_realname")
    public JSONResult<UserInformationDto> getRealNameInfo()  {
        return JSONResult.ok(iUserInformationService.getRealNameInfo());
    }

    /**
     * 获取用户领取优惠券列表
     */
    @ApiOperation("获取用户已领取优惠券列表")
    @PostMapping("/get_user_coupon_page")
    public JSONResult<UserCouponPageDto> getUserCouponPage(@RequestBody UserCouponPageVo userCouponPageVo){
        return JSONResult.ok(iTbCouponReceiveService.getUserCouponPage(userCouponPageVo));
    }


    /**
     * 用户领取优惠券 按规则做限制领取
     */
    @ApiOperation("用户领取优惠券")
    @GetMapping("/receive")
    public JSONResult doReceive(@RequestParam("coupenId") Integer coupenId){
        return iTbCouponReceiveService.doReceive(coupenId);
    }

    /**
     * 更新用户信息 TODO
     */

}
