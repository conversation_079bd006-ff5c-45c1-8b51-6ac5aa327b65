package com.zzz.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.zzz.annotation.RsaRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 10:23 2024/6/7
 * @ Description：rsa解密方法封装
 * @ Version:     1.0
 */
@Slf4j
@Component
public class RsaDecryptUtil {

    @Value("${server.rsa.privateKeyStr}")
    private String privateKeyStr;

    @Value("${server.rsa.publicKeyStr}")
    private String publicKeyStr;

    /**
     * 对标记了@RsaRequest注解的属性值进行解密
     *
     * @param obj
     */
    public void process(Object obj) {
        // 获取类的所有字段
        Field[] fields = obj.getClass().getDeclaredFields();
        // 遍历所有字段
        for (Field field : fields) {
            // 检查字段是否标记有自定义注解
            if (field.isAnnotationPresent(RsaRequest.class)) {
                try {
                    // 对属性值进行rsa解密
                    field.setAccessible(true);
                    String fieldValue = field.get(obj).toString();
                    //前端对值进行公钥加密，我们进行私钥解密
                    RSA rsa = new RSA(privateKeyStr, publicKeyStr);
                    byte[] decrypt = rsa.decrypt(fieldValue, KeyType.PrivateKey);
                    String decryptStr = StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);
                    field.set(obj, decryptStr);
                } catch (IllegalAccessException e) {
                    // field.set(clazz, "")，修改属性值失败
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    /**
                     * 处理一些特殊情况
                     * 例如，原属性值，不是密文，或者解密失败，则不进行解密动作
                     */
                    log.info("------@RsaRequest,特殊情况不进行解密，e:{}", e.getMessage());
                }
            }
        }
    }
}
