package com.zzz.service;

import com.zzz.dto.CommonInfoDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.entity.CommonInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ICommonInfoService extends IService<CommonInfo> {

    List<CommonInfo> getBaseInfoByCode(CommonEnum code);

    /**
     * 获取支付相关协议列表
     *
     * @return 支付相关协议列表
     */
    List<CommonInfoDto> queryAgreementList();

    /**
     * 获取支付相关协议详情
     *
     * @param id 编号
     * @return 支付相关协议详情
     */
    CommonInfoDto getAgreementDetail(Integer id);
}
