package com.zzz.emuns;

/**
 * 用户注册来源枚举
 * <AUTHOR> to 2024/7/18
 */
public enum RegSourceEnum {
    /**
     * 用户注册来源。1 微信小程序 2 支付宝小程序 3 H5
     */
    WX(1, "微信小程序"),
    ALIPAY(2, "支付宝小程序"),
    H5(3, "H5"),

    ;

    private final Integer value;
    private final String label;

    RegSourceEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据指定value解析成枚举常量
     */
    public static RegSourceEnum resolve(Integer value, RegSourceEnum dv) {
        for (RegSourceEnum s : values()) {
            if (s.value.equals(value)) {
                return s;
            }
        }
        return dv;
    }

    public static RegSourceEnum resolve(Integer value) {
        return resolve(value, null);
    }

    /**
     * 判断 value 值是否是枚举里的值
     */
    public boolean matches(Integer value) {
        return this.value.equals(value);
    }

    public Integer value() {
        return value;
    }

    public String label() {
        return label;
    }
}
