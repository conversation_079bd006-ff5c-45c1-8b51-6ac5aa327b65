package com.zzz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zzz.component.bamboo.BamBooAPI;
import com.zzz.component.bamboo.request.DeliveryDateRequest;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.dto.BranchDto;
import com.zzz.dto.BranchPageDto;
import com.zzz.dto.BranchUrgentDto;
import com.zzz.dto.TagsDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.emuns.StatusPlusEnum;
import com.zzz.entity.*;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbBranchMapper;
import com.zzz.response.JSONResult;
import com.zzz.service.*;
import com.zzz.util.CusNumberUtil;
import com.zzz.util.DistanceUtil;
import com.zzz.util.SpringBeanUtil;
import com.zzz.vo.BranchPageVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 网点 服务实现类 test
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class TbBranchServiceImpl extends ServiceImpl<TbBranchMapper, TbBranch> implements ITbBranchService {

    @Autowired
    private TbBranchMapper tbBranchMapper;

    @Autowired
    private ITbcurrencyService iTbcurrencyService;

    @Autowired
    private ITbTagsTaggingsService tbTagsTaggingsService;

    @Autowired
    private ITbBranchUrgentService iTbBranchUrgentService;

    @Autowired
    private IBbDistributionOrgService iBbDistributionOrgService;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Override
    public BranchPageDto getBranchPage(BranchPageVo req) {
        LambdaQueryWrapper<TbBranch> queryWrapper = new LambdaQueryWrapper<>();
        //1、查询条件
        if(StringUtils.isNoneBlank(req.getTagId() )){
            //查询已绑定指定标签的网点
            List<TbTagsTaggings> tglist = tbTagsTaggingsService.getSitesBytagId(req.getTagId());
            if(tglist.isEmpty()){
                throw new ServiceException(CommonErrorEnum.LIST_IS_NULL);
            }
            List<Integer> sitelist = tglist.stream().map(i->i.getSiteId()).collect(Collectors.toList());
            queryWrapper.in(TbBranch::getId,sitelist);
        }
        queryWrapper.eq(TbBranch::getOnlineFlag,1);
//        queryWrapper.eq(TbBranch::getLongitude,req.getLongitude());
//        queryWrapper.eq(TbBranch::getLatitude,req.getLatitude());

        //根据有对应兑换币种获取网点ID 集合
        List<Tbcurrency> currencyIdsByCurName = null;
        if(StringUtils.isNoneBlank(req.getCurrency())){
            currencyIdsByCurName = iTbcurrencyService.getCurrencyIdsByCurName(req.getCurrency());
            if(currencyIdsByCurName.isEmpty()){
                throw new ServiceException(CommonErrorEnum.LIST_IS_NULL);
            }
            //获取有此币种的网点id
            List<Integer> ids = currencyIdsByCurName.stream().map(item->item.getBranchId()).collect(Collectors.toList());
            queryWrapper.in(TbBranch::getId,ids);
        }
        queryWrapper.eq(TbBranch::getIsDel,0);
        //倒序
//        queryWrapper.orderByDesc(TbBranch::getId);
        //2、分页查询数据
        Page<TbBranch> page = new Page<>(req.getPage(), req.getSize());
        IPage<TbBranch> dataIPage = tbBranchMapper.selectPage(page, queryWrapper);
        List<BranchDto> list = SpringBeanUtil.copyProperties(dataIPage.getRecords(), BranchDto.class);
        if(currencyIdsByCurName !=null && !currencyIdsByCurName.isEmpty()){
            //添加汇率
            setRateToBranch(list,currencyIdsByCurName);
        }
        //3、 数据处理
        //计算距离
        setDistanceToBranch(list,req);
        //给网点设置标签集合
        setTagsToBranch(list);

        list.sort(Comparator.comparing(branch -> branch.getDistance()));


        //4、返回查询结果
        return BranchPageDto.builder()
                .totalPage(dataIPage.getPages())
                .totalRecord(dataIPage.getTotal())
                .list(list)
                .build();
    }


    @Override
    public TbBranch getBranchById(Integer branchId) {
        if(CusNumberUtil.isNumber(branchId)){
            throw new ServiceException(CommonErrorEnum.BRANCH_ID_IS_NULL);
        }
        return tbBranchMapper.selectOne(new LambdaQueryWrapper<TbBranch>().eq(TbBranch::getId,branchId));
    }

    @Override
    public JSONResult<List<BranchUrgentDto>> getUrgentListByBranchid(Integer id) {
        //1、必传，非空校验
        if(CusNumberUtil.isNumber(id)){
            throw new ServiceException(CommonErrorEnum.BRANCH_ID_IS_NULL);
        }

        //2、查询网点数据
        TbBranch tbBranch = tbBranchMapper.selectById(id);
//        Integer urgentStatus = tbBranch.getUrgentStatus();
//        if(urgentStatus == 0){
//            return JSONResult.error(CommonErrorEnum.BRANCH_CLOSE_URGENT);
//        }

        //bamboo 加急时间段
        if (StringUtils.isNoneBlank(tbBranch.getType()) && "1".equals(tbBranch.getType())) {
            List<CommonInfo> listComm = iCommonInfoService.getBaseInfoByCode(CommonEnum.URGENT_TIME_P);
            Map<String, String> configMap = listComm.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));

            List<BranchUrgentDto> list = new ArrayList<>();
            // DeliveryDateRequest request = new DeliveryDateRequest();
            // BbDistributionOrg bbDistributionOrg = iBbDistributionOrgService.getDistributionByPid(tbBranch.getOutId());
            // request.setCustomerCode(bbDistributionOrg.getOrgCode());
            // ResponseData responseData = BamBooAPI.queryDeliveryDateList(request);
            // if (null == responseData) {
            //     throw new ServiceException(CommonErrorEnum.BAMBOO_BRANCH_NOT_EXTRACT_DATE);
            // }
            // List<String> extractList = JSONArray.parseArray(responseData.getData()).toJavaList(String.class);
            List<String> extractList = Lists.newArrayList("2024-11-07","2024-11-08","2024-11-09","2024-11-10");
            extractList.sort(Comparator.comparing(DateUtil::parseDate));
            // 处理 bamboo 不可提取日期
            String unExtractDateStr = configMap.get(CommonEnum.UN_EXTRACT_DATE.name());
            if (StrUtil.isNotBlank(unExtractDateStr)) {
                Integer unExtractDate = Convert.toInt(unExtractDateStr);
                Iterator<String> iterator = extractList.iterator();
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    if (unExtractDate >= 0) {
                        iterator.remove();
                        unExtractDate--;
                    }
                }
            }
            // 处理加急费：加急费如果有不可提取日期，需要往后延
            String urgent = configMap.get(CommonEnum.URGENT_TIME.name());
            String urgentFee = configMap.get(CommonEnum.URGENT_FEE.name());

            for (String str : extractList) {
                BranchUrgentDto branchUrgentDto = new BranchUrgentDto();
                branchUrgentDto.setId(-1);
                branchUrgentDto.setBranchId(id);
                branchUrgentDto.setFee(new BigDecimal("0.00"));
                branchUrgentDto.setDate(LocalDate.parse(DateUtil.formatDate(DateUtil.parseDate(str))));
                list.add(branchUrgentDto);
            }

            // 加急
            for (int j = 0; j <= Math.min(Integer.parseInt(urgent), list.size() - 1); j++) {
                list.get(j).setFee(new BigDecimal(urgentFee));
            }

            return JSONResult.ok(list);
        }

        //5、返回结果
        return JSONResult.ok(SpringBeanUtil.copyProperties(getUrgentList(tbBranch),BranchUrgentDto.class));
    }

    private List<BranchUrgentDto> getUrgentList(TbBranch tbBranch) {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.URGENT_TIME_P);
        List<BranchUrgentDto> urgentList = new ArrayList<>();
        if(!list.isEmpty()){
            Map<String, String> wxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
            String extract = wxParamMap.get(CommonEnum.EXTRACT_TIME.name());
            String urgent = wxParamMap.get(CommonEnum.URGENT_TIME.name());
            String urgentFee = wxParamMap.get(CommonEnum.URGENT_FEE.name());
            if(StringUtils.isNoneBlank(extract)){
                //遍历生成可提取时间
                String[] split = extract.split("-");
                int start = Integer.parseInt(split[0]);
                int end = Integer.parseInt(split[1]);
                BigDecimal fee = new BigDecimal(0);
                if(StringUtils.isNoneBlank(urgentFee)){
                    fee = new BigDecimal(urgentFee);
                }
                for(int i=start;i<=end;i++){
                    BranchUrgentDto branchUrgentDto = new BranchUrgentDto();
                    branchUrgentDto.setId(i);
                    branchUrgentDto.setBranchId(tbBranch.getId());
                    branchUrgentDto.setFee(new BigDecimal(0));
                    DateTime dateTime = DateUtil.offsetDay(new Date(), i);
                    branchUrgentDto.setDate(LocalDate.parse(DateUtil.format(dateTime, "yyyy-MM-dd")));
                    urgentList.add(branchUrgentDto);
                }
                // 排除不可提取时间
                List<TbBranchUrgent> branchUrgentList = iTbBranchUrgentService.list(Wrappers.<TbBranchUrgent>lambdaQuery()
                        .eq(TbBranchUrgent::getStatus, StatusPlusEnum.ENABLE.value())
                        .eq(TbBranchUrgent::getBranchId, tbBranch.getId())
                        .between(TbBranchUrgent::getDate,
                                DateUtil.beginOfMonth(DateUtil.parse(DateUtil.now(), "yyyy-MM")),
                                DateUtil.endOfYear(DateUtil.offsetMonth(new Date(), 1))
                        )
                );
                Set<String> notExtractSet = new HashSet<>();
                if (CollUtil.isNotEmpty(branchUrgentList)) {
                    notExtractSet = branchUrgentList.stream().map(o -> o.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).collect(Collectors.toSet());
                }
                Iterator<BranchUrgentDto> iterator = urgentList.iterator();
                while (iterator.hasNext()) {
                    BranchUrgentDto next = iterator.next();
                    // 不可提取日期过滤
                    if (notExtractSet.contains(next.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
                        iterator.remove();
                    }
                }
                // 加急
                for (int j = 0; j <= Integer.parseInt(urgent); j++) {
                    urgentList.get(j).setFee(fee);
                }
            }
        }
        return urgentList;
    }

    @Override
    @Transactional
    public void addBranch(Integer id, String branchCode, String branchName,
                          String contactPerson, String contactTelephone, int i) {
        TbBranch tbBranch = new TbBranch();
        tbBranch.setSitecode(branchCode);
        tbBranch.setSitename(branchName);
        tbBranch.setSitemanager(contactPerson);
        tbBranch.setSitetel(contactTelephone);
        tbBranch.setUrgentFutureDay(7);
        tbBranch.setUrgentStatus(1);
        tbBranch.setStartTime("07:00");
        tbBranch.setEndTime("19:00");
        tbBranch.setOutId(id+"");
        tbBranch.setType(i+"");
        tbBranch.setLatitude(0.0);
        tbBranch.setLongitude(0.0);
        tbBranch.setCurrencyPercent(1);
        tbBranch.setSiteaddr(branchName);
        tbBranchMapper.insert(tbBranch);
    }

    @Override
    public List<TbBranch> getBranchList(Integer type) {
        return tbBranchMapper.selectList(new LambdaQueryWrapper<TbBranch>().eq(TbBranch::getType,type));
    }


    /**
     * 给网点添加标签集合
     * @param list
     */
    private void setTagsToBranch(List<BranchDto> list) {
        list.forEach(item->{
            List<TbTags> tagsList = tbTagsTaggingsService.selectListBySiteId(item.getId());
            if(!tagsList.isEmpty()){
                //绑定标签
                List<TagsDto> tagList = tagsList.stream().map(tag->{
                    TagsDto tagsDto = new TagsDto();
                    tagsDto.setId(tag.getId());
                    tagsDto.setName(tag.getName());
                    return tagsDto;
                }).collect(Collectors.toList());
                item.setTagsList(tagList);
            }
        });
    }

    /**
     * 网点距离计算 返回单位:km
     * @param list
     * @param req
     */
    private void setDistanceToBranch(List<BranchDto> list, BranchPageVo req) {
        list.forEach(item->{
            //计算经纬度
            BigDecimal distance = DistanceUtil.getDistanceBigDecimalOneDecimalPlace(new BigDecimal(req.getLatitude()), new BigDecimal(req.getLongitude()),
                    new BigDecimal(item.getLatitude()), new BigDecimal(item.getLongitude()));
            //转千米
            BigDecimal resultVal = NumberUtil.round(NumberUtil.div(distance, 1000),3);
            item.setDistance(resultVal);
        });
    }

    /**
     * 网点增加 对应币种汇率
     * @param list
     * @param currencyList
     */
    private void setRateToBranch(List<BranchDto> list, List<Tbcurrency> currencyList) {
        Map<Integer,BigDecimal>  map = currencyList.stream().collect(Collectors.toMap(Tbcurrency::getBranchId,Tbcurrency::getMarketPrice));
        list.forEach(item->{
            item.setCurrate(map.get(item.getId()));
        });
    }
}
