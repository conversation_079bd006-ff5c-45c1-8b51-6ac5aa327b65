package com.zzz.websocket.context;

import lombok.Data;

import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 11:54 2024/4/11
 * @ Description：websocket请求上下文
 * @ example：{"id":"","token":"","module":"SocketController","method":"test","data":{}}
 * @ Version:     1.0
 */
@Data
public class ReqWebSocket implements Serializable {

    //用户id
    private String id;

    //用户token
    private String token;

    //模块名称，对应Class 名称
    private String module;

    //方法名称
    private String method;

    //请求数据data
    private String data;
}
