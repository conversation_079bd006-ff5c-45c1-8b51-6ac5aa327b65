package com.zzz.component.max.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *  创建订单参数
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-18
 **/
@Data
public class CreateOrderRequest {

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String personName;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String docno;

    /**
     * 客户联系电话
     */
    @ApiModelProperty("客户联系电话")
    private String linktel;


    /**
     * 客户支付币种：外币币种
     */
    @TableField("CURRENCY")
    private String currency;

    /**
     * 客户第三方支付金额  微信您支付宝支付金额  实际支付
     */
    @ApiModelProperty("客户第三方支付金额")
    private BigDecimal cardamt;

    /**
     * 兑换外币折美元金额  币种数量
     */
    @ApiModelProperty("兑换外币折美元金额")
    private BigDecimal usbAmt;

    /**
     * 购汇种类(ICETYPE=2时，不能为NULL)
     */
    @ApiModelProperty("购汇种类")
    private String buyexchgtype;

    /**
     * 店铺编码
     */
    @ApiModelProperty("店铺编码")
    private String sitecode;


    /**
     *  零钱包锁定单号
     */
    @ApiModelProperty("零钱包锁定单号")
    private String coinno;

    /**
     * 客户第三方支付币种：CNY(C)银行卡、CNY(W)微信、CNY(A)支付宝、CNY(E)数字人民币、N无第三方支付
     */
    @ApiModelProperty("客户第三方支付币种")
    private String summary;

    /**
     * 提取码
     */
    @ApiModelProperty("提取码")
    private String extracCode;

    /**
     * 预约提取时间
     */
    @ApiModelProperty("预约提取时间")
    private String extracTime;


    /**
     * 优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal disAmount;
}
