package com.zzz.emuns;

/**
 * <p>
 *  通用枚举
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
public enum CommonEnum {

    /**
     *  父标识
     */
    WX_MINI_P,
    SERVER_CONTENT_P,
    STEALTH_CONTENT_P,
    ICE_APPLICATION_P,
    ALI_CONFIG_P,
    ALI_SMS_TEMPLATE_P,
    ALI_OPEN_SMS_P,
    RAS_PRI_P,
    EXCHANGE_NITICE_P,
    BANNER_P,
    BUSINESS_TIME_P,
    URGENT_TIME_P,
    ORDER_P,
    /**
     * 子标识
     */
    APPID,
    APP_SECRET,
    SERVER_CONTENT,
    STEALTH_CONTENT,
    ICE_APPLICATION,
    ALI_KEY,
    ALI_ACCESS_SECRET,
    ALI_SMS1,
    ALI_SMS2,
    ALI_SMS3,
    ALI_OPEN_SMS,
    RAS_PRI,
    EXCHANGE_NITICE,
    BANNER,
    AUTO_CODE,
    BUSINESS_TIME,
    TIME_TO_END,
    EXTRACT_TIME,
    URGENT_TIME,
    URGENT_FEE,
    UN_EXTRACT_DATE,
    CANCEL_PAY_TIME,
    NO_EXTRACT_REFUND_TIME,
    NO_EXCEED_USD
}
