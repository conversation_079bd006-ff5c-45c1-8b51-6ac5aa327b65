package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 币种交易表
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tbcurrency_transaction")
    @ApiModel(value="TbcurrencyTransaction对象", description="币种交易表")
    public class TbcurrencyTransaction implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

            @ApiModelProperty(value = "币种id")
    private Integer currencyId;

            @ApiModelProperty(value = "通济隆价格")
    private BigDecimal actualPrice;

            @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

            @ApiModelProperty(value = "创建人，导入人")
    private String createby;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "状态；1启用、0停用")
    private Integer status;


}
