package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：发送短信统一 请求入参
 * @ Version:     1.0
 */
@Data
@ApiModel("短信发送参数")
public class SmsSendVo implements Serializable {

    @ApiModelProperty(name = "phone", value = "手机号", required = true,example = "13666666666")
    @NotBlank
    private String phone;

    @ApiModelProperty(name = "typeConstant", value = "短信类型（code：短信验证码，loginH5：h5登录）", required = true,example = "code")
    @NotBlank
    private String typeConstant;
}
