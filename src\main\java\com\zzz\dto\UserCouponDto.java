package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("用户已领取优惠券信息实体")
public class UserCouponDto {

    @ApiModelProperty(value = "用户领取券ID")
    private Integer recId;

    @ApiModelProperty(value = "原优惠券id")
    private Integer id;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "金额")
    private BigDecimal couponPrice;

    @ApiModelProperty(value = "类型 1立减 2满减 ")
    private String couponType;

    @ApiModelProperty(value = "满减金额")
    private BigDecimal cutPrice;

//    @ApiModelProperty(value = "开始时间")
//    private LocalDateTime startTime;
//
//    @ApiModelProperty(value = "结束时间")
//    private LocalDateTime endTime;


    @ApiModelProperty(value = "优惠券使用状态：0锁定1使用2未使用")
    private String applyState;

    @ApiModelProperty(value = "可领券用户类型")
    private String couponCustomerType;

    @ApiModelProperty(value = "限领数量")
    private String numberLimit;

    @ApiModelProperty(value = "优惠卷说明")
    private String remarks;

}
