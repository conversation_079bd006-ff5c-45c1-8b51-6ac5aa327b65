package com.zzz.component.chinaums.open.api.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created by ZHANGWEI on 2016/12/2.
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiField {
    String key() default "";
    String name() default "";
    boolean required() default false;
    int length() default -1;
    int minLength() default -1;
    int maxLength() default -1;
    String index() default "0";
    String desc() default "";
}
