package com.zzz.config.rabbitmq;

import lombok.Getter;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 14:24 2022/11/15
 * @ Description：
 * @ Version:     1.0
 */
@Getter
public enum EnumsRabbitQueue {

    // 登录日志队列
    TEST_QUEUE("ykp_test_message_queue",
            "ykp_test_delayed_message_exchange",
            "ykp_test_message_delayed_key", 1000l);

    // 队列名
    private String name;

    // 交换机名称 用作转发延时队列
    private String exchange;

    // 延时key名称
    private String delayKey;

    // 有效时间 单位ms
    private Long ttl;

    EnumsRabbitQueue(String name, String exchange, String delayKey, Long ttl) {
        this.name = name;
        this.exchange = exchange;
        this.delayKey = delayKey;
        this.ttl = ttl;
    }

}
