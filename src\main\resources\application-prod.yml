spring:
  flyway:
    enabled: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: *********************************************************************************************************************************************************************************************************************
    username: plusdb_user
    password: mlnqWzfbekzrbtaX5qtacfi2
  redis:
    #数据库索引
    database: 11
    host: *************
    port: 6379
    password: AuDdQd.puEZpXgNthP6CjYjPb
    #连接超时时间
    timeout: 5000
    lettuce:
      pool:
        max-idle: 20000
        min-idle: 2
        max-active: 36000
        max-wait: 5000
  ## rabbitmq配置 - start
  rabbitmq:
    host: *************
    port: 5672
    username: prod-plus-user
    password: Au2Qd.puPLUSpTua90Cj88j
    virtual-host: prod-plus
  #    listener:
  #      simple:
  #        #ack策略
  #        acknowledge-mode: none
  #        #消息确认策略
  #    publisher-confirm-type: correlated
mybatis-plus:
  # mapper.xml扫描
  mapper-locations: classpath*:mapper/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  configuration:
    # 开启自动驼峰命名规则
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    # 打印SQL语句,此处需注释掉，否则影响到logback日志正常打印输出
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#saToken相关
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # jwt加密key
  jwt-secret-key: saToken2024zzz
#knife4j文档相关
knife4j:
  enable: true
  openapi:
    title: travelex-plus接口文档
    description: "`我是测试`,**你知道吗**
    # aaa"
    email: <EMAIL>
    concat: travelex-plus
    url: https://#
    version: v1.0
    group:
      test1:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.zzz.controller
###################server模块相关
server:
  service:
    on-off: true
  #rsa加密相关
  rsa:
    #私钥
    privateKeyStr: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOiwkdi2AY7e3GUTk4xDbMQ3FoudRwyigIPgEscZqZJU0hoG1Y+GX13eJKQl27V++MEDiGT4wW+KuvAaNNKxFtvW6SMj8niXxOJfXwcTH/RrTUAdSDPjtTE8T0iz3l3AiwmSVSORdUvZ/V5AieShbtOIDAmmL8yVWAqqBSpgn3SDAgMBAAECgYEArztZekQGhFtcylmlwfl8zX9W4Sk+OkueULHMMlgnFWX/G06OQZnF8+C4400HWnUWS/SnILMcqS1mfi6eHRTzyodAn9cdLilNkvXvxomcS2UrdWxz1aYmx8IPDVHgxQZCi1mXkHZE2BpXCoILI3ZXaLBX4VkjL8C5QyXpbkeS3cECQQD02g9tGmEA7Rhw2dbBh878SApu1NdclN0USQrQ95pXK8DPYyljAvUujy3JqCnOI4H+VqmdYCJY+R3RuTwQcWQxAkEA80jAPwIkUM8n/uBVIQTPGo+YoSJcR2ggvQBs1Zn4VadFi3scf+WSRc+TiogxrvBnz/AQCPQircZC9ssS9Z568wJANG+ajB/xFDSyXkCYmDuQXUDEUzXrpR0QN27pDRVpjS+GjCNNUrHodf8drCkCQu4hwakni9dxo6I9+5LyxQazkQJBALQJS5OVTzCqDOwWMYls2KLMT2Q3XFjPmyKjFOeBuQAR+Go/IhkX2AZnlcT4AuR2Liz4d7VYje1Rgi7Gm3I+5LcCQAxBUwD6C1HmBEs/0965MyCEsO0ycq7WqOiGtKu1sWqJ/Nab73dYAADT/tXFWZp62tlvbvwh+W/PDKvDHN4ZmbQ=
    #公钥
    publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDosJHYtgGO3txlE5OMQ2zENxaLnUcMooCD4BLHGamSVNIaBtWPhl9d3iSkJdu1fvjBA4hk+MFvirrwGjTSsRbb1ukjI/J4l8TiX18HEx/0a01AHUgz47UxPE9Is95dwIsJklUjkXVL2f1eQInkoW7TiAwJpi/MlVgKqgUqYJ90gwIDAQAB
# S3配置
aws:
  region: 北京
  accessKey: ********************
  secretKey: WwkHzppJZoqejNmzjXVD9m7P5L6FMOt3NCWmS9hw
  bucketName: hongshanshu-test
#自定义消息队列配置，发送锁定库存消息-》延迟exchange-》lock.queue-》死信exchange-》release.queue
mqconfig:
  #延迟队列，不能被监听消费
  order_close_delay_queue: order.close.delay.queue
  #延迟队列的消息过期后转发的队列
  order_close_queue: order.close.queue
  #交换机
  order_event_exchange: order.event.exchange
  #进入延迟队列的路由key
  order_close_delay_routing_key: order.close.delay.routing.key
  #消息过期，进入释放队列的key,进入死信队列的key
  order_close_routing_key: order.close.routing.key
  #消息过期时间,毫秒,测试改为15分钟
  ttl: 900000
# 第三方系统请求地址
api:
  # max系统请求地址
  maxBaseUrl: https://172.31.40.105:18102
  # bamboo 系统请求地址
  bambooBaseUrl: https://172.31.112.223
