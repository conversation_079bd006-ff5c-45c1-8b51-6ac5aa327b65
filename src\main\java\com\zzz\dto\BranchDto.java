package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-20
 **/
@Data
@ApiModel("网点信息实体")
public class BranchDto {

    @ApiModelProperty("唯一ID")
    private Integer id;

    @ApiModelProperty(value = "网点编码")
    private String sitecode;

    @ApiModelProperty(value = "网点名称")
    private String sitename;

    @ApiModelProperty(value = "网点所属机构编码")
    private String corpcode;

    @ApiModelProperty(value = "网点地址")
    private String siteaddr;

    @ApiModelProperty(value = "网点负责人")
    private String sitemanager;

    @ApiModelProperty(value = "网点电话")
    private String sitetel;

    @ApiModelProperty(value = "是否接收在线订单；1接收、0不接收")
    private Integer onlineFlag;

    @ApiModelProperty(value = "外管接口-网点金融机构标识码")
    private String wsOrgcode;

    @ApiModelProperty(value = "网点接口开启状态；1开启、0关闭")
    private Integer wsFlag;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "营业开始时间")
    private String startTime;

    @ApiModelProperty(value = "营业结束时间")
    private String endTime;

    /**
     * 距离
     */
    @ApiModelProperty(value = "距离，单位：千米")
    private BigDecimal distance;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率；币种列表，有传 币种代码 此参数才有值")
    private BigDecimal currate;

    //绑定标签集合
    @ApiModelProperty(value = "标签集合")
    private List<TagsDto> tagsList;
}
