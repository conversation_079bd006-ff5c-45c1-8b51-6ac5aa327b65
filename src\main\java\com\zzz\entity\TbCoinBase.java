package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 零钱宝规格表
    * </p>
*
* <AUTHOR>
* @since 2024-11-13
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_coin_base")
    @ApiModel(value="TbCoinBase对象", description="零钱宝规格表")
    public class TbCoinBase implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "规格代码")
    private String bizcode;

            @ApiModelProperty(value = "币种")
    private String currency;

            @ApiModelProperty(value = "规格名称")
    private String name;

            @ApiModelProperty(value = "单价；388元/份")
    private BigDecimal price;

            @ApiModelProperty(value = "面值")
    private BigDecimal parValue;

            @ApiModelProperty(value = "库存")
    private Integer quantity;

            @ApiModelProperty(value = "可用状态，0可用，1禁用")
    private Integer state;

            @ApiModelProperty(value = "温馨提示")
    private String remarks;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

            @ApiModelProperty(value = "删除状态。1删除，0正常 ")
    private Integer isDel;


}
