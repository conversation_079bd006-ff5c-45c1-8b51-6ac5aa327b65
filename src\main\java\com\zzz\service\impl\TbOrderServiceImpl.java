package com.zzz.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.component.bamboo.BamBooAPI;
import com.zzz.component.bamboo.request.CancelOrderRequest;
import com.zzz.component.bamboo.request.FreezeStockRequest;
import com.zzz.component.bamboo.request.OrderItem;
import com.zzz.component.bamboo.request.OrderRequest;
import com.zzz.component.bamboo.request.QueryProductPageRequest;
import com.zzz.component.bamboo.response.OrderResponse;
import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.MaxAPI;
import com.zzz.component.max.request.CheckUserRequest;
import com.zzz.component.max.request.CreateOrderRequest;
import com.zzz.component.max.request.LockCoinStockRequest;
import com.zzz.component.max.response.CreateOrderResponse;
import com.zzz.config.ums.UmsPayConfig;
import com.zzz.constants.PaymentConstants;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.BranchOrgDto;
import com.zzz.dto.CurrencyDto;
import com.zzz.dto.OrderDto;
import com.zzz.dto.OrderPageDto;
import com.zzz.dto.PaymentParamDto;
import com.zzz.dto.PersonalMaxPurchaseLimitDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.emuns.DeleteStatusEnum;
import com.zzz.emuns.OrderPayStatusEnum;
import com.zzz.emuns.OrderPayTypeEnum;
import com.zzz.emuns.OrderStatusEnum;
import com.zzz.emuns.RedisKeyEnum;
import com.zzz.emuns.RegSourceEnum;
import com.zzz.entity.BbDistributionOrg;
import com.zzz.entity.BbProduct;
import com.zzz.entity.CommonInfo;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.TbBranch;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbOrder;
import com.zzz.entity.TbOrderPurpose;
import com.zzz.entity.UserInformation;
import com.zzz.entity.UserOpenInfo;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbOrderMapper;
import com.zzz.mq.OrderMessage;
import com.zzz.response.JSONResult;
import com.zzz.service.IBbDistributionOrgService;
import com.zzz.service.IBbProductService;
import com.zzz.service.ICommonInfoService;
import com.zzz.service.ITbBranchService;
import com.zzz.service.ITbCoinChildService;
import com.zzz.service.ITbCoinPurseService;
import com.zzz.service.ITbCouponReceiveService;
import com.zzz.service.ITbOrderImgService;
import com.zzz.service.ITbOrderPurposeService;
import com.zzz.service.ITbOrderService;
import com.zzz.service.ITbcurrencyService;
import com.zzz.service.ITbfeesService;
import com.zzz.service.ITbmonusdrateService;
import com.zzz.service.IUmsPayService;
import com.zzz.service.IUserInformationService;
import com.zzz.service.IUserOpenInfoService;
import com.zzz.service.payment.PaymentStrategy;
import com.zzz.service.payment.PaymentStrategyFactory;
import com.zzz.util.CusNumberUtil;
import com.zzz.util.SpringBeanUtil;
import com.zzz.vo.ContinuePayOrderVo;
import com.zzz.vo.CreateOrderVo;
import com.zzz.vo.OrderPageVo;
import com.zzz.vo.PayOrderVo;
import com.zzz.vo.QueryPersonalMaxPurchaseLimitVo;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
public class TbOrderServiceImpl extends ServiceImpl<TbOrderMapper, TbOrder> implements ITbOrderService {

    @Autowired
    private TbOrderMapper tbOrderMapper;

    @Autowired
    private ITbCouponReceiveService iTbCouponReceiveService;

    @Autowired
    private IUserInformationService iUserInformationService;

    @Autowired
    private ITbBranchService iTbBranchService;

    @Autowired
    private ITbOrderPurposeService iTbOrderPurposeService;

    @Autowired
    private ITbcurrencyService iTbcurrencyService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ICommonInfoService iCommonInfoService;

    @Autowired
    private IBbProductService iBbProductService;

    @Autowired
    private IBbDistributionOrgService iBbDistributionOrgService;

    @Resource
    private ITbmonusdrateService iTbmonusdrateService;

    @Resource
    private ITbOrderImgService iTbOrderImgService;

    @Resource
    private MaxAPI maxAPI;

    @Resource
    private BamBooAPI bamBooAPI;

    @Autowired
    private IUmsPayService umsPayService;

    @Autowired
    private UmsPayConfig umsPayConfig;

    @Autowired
    private IUserOpenInfoService iUserOpenInfoService;

    @Autowired
    private PaymentStrategyFactory paymentStrategyFactory;

    /**
     * 非对称加密密钥  用户信息加密
     */
    @Value("${server.rsa.publicKeyStr}")
    private String publicKeyStr;
    @Value("${server.rsa.privateKeyStr}")
    private String privateKeyStr;

    /***
     * 订单号全局生成工具 雪花算法
     */
    private final Snowflake snowflake = IdUtil.getSnowflake(1, 1);

    @Override
    public JSONResult<OrderPageDto> getOrderPage(OrderPageVo req) {
        //1、必传，非空校验
        if(CusNumberUtil.isNumber(req.getPage())){
            throw new ServiceException(CommonErrorEnum.PAGE_CODE_IS_NULL);
        }
        if(CusNumberUtil.isNumber(req.getSize())){
            throw new ServiceException(CommonErrorEnum.PAGE_SIZE_IS_NULL);
        }
        //2、当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);

        LambdaQueryWrapper<TbOrder> queryWrapper = new LambdaQueryWrapper<>();
        //查询条件

        //指定查询用户
        queryWrapper.eq(TbOrder::getUserId,reqContextUser.getId());
        queryWrapper.eq(TbOrder::getIsDel,0);
        //倒序
        queryWrapper.orderByDesc(TbOrder::getCreateTime);

        //3.分页查询数据
        Page<TbOrder> page = new Page<>(req.getPage(), req.getSize());
        IPage<TbOrder> dataIPage = tbOrderMapper.selectPage(page, queryWrapper);
        // 数据为空
        if(dataIPage.getTotal() == 0){
            return JSONResult.ok();
        }
        //4.数据转换为前端实体对象
        List<OrderDto> list = SpringBeanUtil.copyProperties(dataIPage.getRecords(), OrderDto.class);

        //用户数据加密
        setUserInfoToRsa(list);

        //加有效期倒计时
        setpayValidTime(list);

        //5.返回查询结果
        return JSONResult.ok(
                OrderPageDto.builder()
                .totalPage(dataIPage.getPages())
                .totalRecord(dataIPage.getTotal())
                .list(list)
                .build()
        );
    }



    @Override
    public JSONResult<OrderDto> getOrderDetailByNo(String orderno) {
        //1.必传 数据校验
        if(StringUtils.isBlank(orderno)){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }
        //2.查找订单
        TbOrder tbOrder = tbOrderMapper.selectOne(
                new LambdaUpdateWrapper<TbOrder>()
                        .eq(TbOrder::getOrderNumber,orderno)
                        .eq(TbOrder::getIsDel,0)
        );
        if(null == tbOrder){
            return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
        }

        //3.用户数据 加密处理
        RSA rsa = new RSA(privateKeyStr, publicKeyStr);
        OrderDto item = SpringBeanUtil.copyProperties(tbOrder, OrderDto.class);
        item.setUserName(rsa.encryptBase64(item.getUserName(), KeyType.PublicKey));
        item.setUserPhone(rsa.encryptBase64(item.getUserPhone(), KeyType.PublicKey));
        item.setUserCard(rsa.encryptBase64(item.getUserCard(), KeyType.PublicKey));
        // Integer walletId = tbOrder.getWalletId();
        // double total = tbCoinChildService.selectSumAmtById(walletId);

        //4.其他数据设置
        //零钱包外币金额
        item.setWalletTotalAmt(NumberUtil.mul(item.getWalletParvalue(), item.getWalletCount()));
        //外币总金额
        item.setUsbTotalAmt(NumberUtil.add(item.getWalletTotalAmt(),item.getUsbAmt()));

        //5.返回数据
        return JSONResult.ok(item);
    }

    /**
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public JSONResult createOrder(CreateOrderVo req) {
        //1. 营业时间判断
        //checkBusinessTime(req.getExtractDate(), true);

        log.info("1.创建订单请求参数：{}", JSONUtil.toJsonStr(req));
        //雪花算法生成订单号
        String orderno = IdUtil.getSnowflakeNextIdStr();
        //当前登录用户信息
        ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        log.info("2.创建订单请求用户信息：{}", JSONUtil.toJsonStr(reqContextUser));
        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),reqContextUser.getId());
        RLock lock = this.redissonClient.getLock(aclKey);
        boolean flag = false;
        try{
            flag = lock.tryLock(0,5, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("===========加锁失败=========");
        }
        //加锁
        if(flag){
            // 测试频繁操作
//            try{
//                Thread.sleep(3000l);
//            }catch (Exception e){
//                log.error("===========加锁失败=========");
//            }
            try{
                //订单对象
                TbOrder tbOrder = new TbOrder();
                tbOrder.setOrderNumber(orderno);
                //1、基础信息校验  数据设置
                checkOrderBaseInfo(req,tbOrder);

                //2、创建订单
                tbOrder.setUserId(reqContextUser.getId());
                tbOrder.setOrderState(OrderStatusEnum.WAIT_PAYMENT.value);
                tbOrder.setPurpose(req.getPurpose());
                tbOrder.setCouponId(req.getCouponId());
                tbOrder.setCreateTime(LocalDateTime.now());
                tbOrder.setIsDel(DeleteStatusEnum.NORMAL.value);
                tbOrder.setPayStatus(OrderPayStatusEnum.NO_PAY.value);
                int insert = tbOrderMapper.insert(tbOrder);
                log.info("创建订单：{}", insert);
                //后期根据数据量 考虑分表 订单主表 和 子表 ；且考虑保存数据的 历史信息；如：钱包数据，券数据 TODO

                //发送延迟消息 关单服务
                CommonInfo cancelCommonInfo = iCommonInfoService.getOne(Wrappers.<CommonInfo>lambdaQuery().eq(CommonInfo::getCodeValue, CommonEnum.CANCEL_PAY_TIME.name()));
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setOrderNo(orderno);
                orderMessage.setUserId(Long.valueOf(reqContextUser.getId()));
                rabbitTemplate.convertAndSend("order.event.exchange", "order.close.delay.routing.key", orderMessage, message -> {
                    String ttl = StrUtil.isBlank(cancelCommonInfo.getAclValue()) ? "15" : cancelCommonInfo.getAclValue();
                    message.getMessageProperties().setExpiration(Integer.toString(Convert.toInt(ttl) * 1000 * 60));
                    return message;
                });
                log.info("发送消息MQ成功：{}", orderno);
                //1、发送消息 ，通知max 系统创建订单   TODO

                //3.锁库存 第三方接口 bamboo TODO 预备扣库存
                String channelType = tbOrder.getChannelType();
                if("1".equals(channelType)){
                    lockStockBamboo(tbOrder,req);
                }
                //使用零钱包
                if("0".equals(channelType)){
                    if(StrUtil.isNotBlank(tbOrder.getWalletId()) && tbOrder.getWalletCount() > 0) {
                        TbBranch tbBranch = iTbBranchService.getBranchById(req.getBranchId());
                        CurrencyDto currencyById = iTbcurrencyService.getCurrencyById(req.getCurId());
                        // 选择了零钱包并且 库存足够锁定库存；

                        ResponseData responseData = maxAPI.queryCoinDetail(currencyById.getCurrency(), tbBranch.getSitecode());
                        if (null != responseData && "200".equals(responseData.getCode())) {
                            //锁定库存
                            LockCoinStockRequest request = new LockCoinStockRequest();
                            request.setCoincode(tbOrder.getWalletId());
                            request.setSitecode(tbBranch.getSitecode());
                            request.setOutno("COIN" + RandomUtil.randomNumbers(12));
                            request.setCurcode(currencyById.getCurrency());
                            request.setLockcount(req.getWalletCount());
                            ResponseData lockCoinStock = maxAPI.lockCoinStock(request);
                            if (null != lockCoinStock && lockCoinStock.getCode().equals("200")) {
                                //锁定成功
                                tbOrderMapper.update(null,
                                        new LambdaUpdateWrapper<TbOrder>()
                                                .eq(TbOrder::getOrderNumber, tbOrder.getOrderNumber())
                                                .set(TbOrder::getFreezStock, JSONUtil.toJsonStr(request))
                                );
                            } else {
                                throw new ServiceException(CommonErrorEnum.BAMBOO_LOCK_STOCK_FAIL);
                            }
                        }
                    }
                }

            }catch (Exception e) {
            	log.error(e.getMessage(),e);
            	throw new ServiceException(e.getMessage());
			}finally {
                // 解锁前检查当前线程是否持有该锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("-------------------释放redisson锁---------------------");
            }
        }else{
            return JSONResult.error(CommonErrorEnum.REPEAT_SUBMIT_ORDER);
        }
        return JSONResult.ok(orderno);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public JSONResult payOrder(PayOrderVo req) {
        //1. 营业时间判断
        checkBusinessTime(null, false);
        if(StringUtils.isBlank(req.getOrderno())){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }
        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),req.getOrderno());
        RLock lock = this.redissonClient.getLock(aclKey);
        boolean flag = false;
        try{
            flag = lock.tryLock(0,5, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("===========加锁失败=========");
        }
        //加锁
        if(flag){
            // TODO 提取码
            String withdrawaCode = RandomUtil.randomNumbers(10);
            try{
                //查找订单
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,req.getOrderno())
                );
                if(null == tbOrder){
                    return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
                }
                //待支付订单 才可以支付
                if(tbOrder.getOrderState() != OrderStatusEnum.WAIT_PAYMENT.value ){
                    return JSONResult.error(CommonErrorEnum.ORDER_STATUS_PAY_ERROR);
                }

                // 设置支付类型
                if (StringUtils.isNotBlank(req.getPayType())) {
                    tbOrder.setPayType(req.getPayType());
                    tbOrder.setUpdateTime(LocalDateTime.now());
                    tbOrder.setWithdrawaCode(withdrawaCode);
                    tbOrderMapper.updateById(tbOrder);
                }


                //直接去 bamboo下的那
                if ("1".equals(tbOrder.getChannelType())) {
                    createOrderBamboo(tbOrder);
                }
                //max
                if ("0".equals(tbOrder.getChannelType())) {
                    log.error("max创建订单请求参数1:{}", tbOrder);
                    Map<String, Object> resMap = createdOrderMax(tbOrder);
                    boolean flagMax = Boolean.parseBoolean(String.valueOf(resMap.get("flag")));
                    if (!flagMax) {
                        // TODO  订单直接标记失败 且进行退款操作；
                        tbOrderMapper.update(null,
                                new LambdaUpdateWrapper<TbOrder>()
                                        .eq(TbOrder::getOrderNumber, tbOrder.getOrderNumber())
                                        .set(TbOrder::getOrderState, 6)
                                        .set(TbOrder::getPayStatus, 2)
                                        .set(TbOrder::getExtractStatus, 4)
                                        .set(TbOrder::getRefundStatus, 1)
                                        .set(TbOrder::getRefundRemark, "第三方服务下单失败")
                                        .set(TbOrder::getRefendTime, LocalDateTime.now())
                                        .set(TbOrder::getAgreeRefundTime, LocalDateTime.now())
                                        .set(TbOrder::getApplyRefundTime, LocalDateTime.now())
                        );
                        //水单创建失败
                        return JSONResult.error(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(), String.valueOf(resMap.get("msg")));
                    }
                }
                // 获取支付配置
                PaymentParamDto paymentParams = packPayParam(tbOrder);
                return JSONResult.ok(paymentParams);
            }
//            catch (Exception e) {
//                // 支付失败订单需要反扫，重试  加入异常导致事物失效 不做处理
//                tbOrderMapper.update(null, Wrappers.<TbOrder>lambdaUpdate()
//                        .set(TbOrder::getPayStatus, OrderPayStatusEnum.PAY_FAIL.value)
//                        .set(TbOrder::getWithdrawaCode, withdrawaCode)
//                        .set(TbOrder::getUpdateTime, LocalDateTime.now())
//                        .eq(TbOrder::getOrderNumber, req.getOrderno())
//                );
//            }
            finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("-------------------释放redisson锁---------------------");
            }
        }

        // 获取订单信息
        TbOrder tbOrder = tbOrderMapper.selectOne(
                new LambdaUpdateWrapper<TbOrder>()
                        .eq(TbOrder::getOrderNumber, req.getOrderno())
        );
        if (tbOrder == null) {
            return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
        }

        // 获取支付配置
        PaymentParamDto paymentParams = packPayParam(tbOrder);
        return JSONResult.ok(paymentParams);
    }



    /**
     * 一个单号重新发起支付请求，
     * 设计到本系统订单号 和 第三方系统订单号的对应关系处理，后期对接需要对应表 TODO
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public JSONResult continuePayOrder(ContinuePayOrderVo req) {
        //1. 营业时间判断
        checkBusinessTime(null, false);
        if(StringUtils.isBlank(req.getOrderno())){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }
        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),req.getOrderno());
        RLock lock = this.redissonClient.getLock(aclKey);
        boolean flag = false;
        try{
            flag = lock.tryLock(0,5, TimeUnit.SECONDS);
        }catch (Exception e){
            log.error("===========加锁失败=========");
        }
        //加锁
        if(flag){
            try{
                //查找订单
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,req.getOrderno())
                );
                if(null == tbOrder){
                    return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
                }
                //待支付订单 才可以支付
                if(tbOrder.getOrderState() != OrderStatusEnum.WAIT_PAYMENT.value ){
                    return JSONResult.error(CommonErrorEnum.ORDER_STATUS_PAY_ERROR);
                }
                // 更新订单更新时间
                tbOrder.setUpdateTime(LocalDateTime.now());
                tbOrderMapper.updateById(tbOrder);

                //直接去 bamboo下的那
                if(tbOrder.getChannelType().equals("1")){
                    createOrderBamboo(tbOrder);
                }
                //max
                if(tbOrder.getChannelType().equals("0")){
                    Map<String,Object> resMap = createdOrderMax(tbOrder);
                    boolean flagMax = Boolean.parseBoolean(String.valueOf(resMap.get("flag")));
                    if(!flagMax){
                        // TODO  订单直接标记失败 且进行退款操作；
                        tbOrderMapper.update(null,
                                new LambdaUpdateWrapper<TbOrder>()
                                        .eq(TbOrder::getOrderNumber,tbOrder.getOrderNumber())
                                        .set(TbOrder::getOrderState,6)
                                        .set(TbOrder::getPayStatus,2)
                                        .set(TbOrder::getExtractStatus,4)
                                        .set(TbOrder::getRefundStatus,1)
                                        .set(TbOrder::getRefundRemark,"第三方服务下单失败")
                                        .set(TbOrder::getRefendTime,LocalDateTime.now())
                                        .set(TbOrder::getAgreeRefundTime,LocalDateTime.now())
                                        .set(TbOrder::getApplyRefundTime,LocalDateTime.now())
                        );
                        //水单创建失败
                        return JSONResult.error(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(),String.valueOf(resMap.get("msg")));
                    }
                }
                //使用优惠券
                iTbCouponReceiveService.useById(tbOrder.getCouponId());

                // 获取支付配置
                PaymentParamDto paymentParams = packPayParam(tbOrder);
                return JSONResult.ok(paymentParams);
            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }

        // 如果没有获取到锁，直接获取订单信息并返回支付参数
        TbOrder tbOrder = tbOrderMapper.selectOne(
                new LambdaUpdateWrapper<TbOrder>()
                        .eq(TbOrder::getOrderNumber, req.getOrderno())
        );
        if (tbOrder == null) {
            return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
        }

        // 获取支付配置
        PaymentParamDto paymentParams = packPayParam(tbOrder);
        return JSONResult.ok(paymentParams);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONResult cancelOrder(String orderno) {
        //1. 营业时间判断
        checkBusinessTime(null, false);
        if(StringUtils.isBlank(orderno)){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }

        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),orderno);
        RLock lock = this.redissonClient.getLock(aclKey);
        //加锁
        if(lock.tryLock()){
            try{
                //查找订单
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,orderno)
                                .eq(TbOrder::getIsDel,DeleteStatusEnum.NORMAL.value)
                );
                if(null == tbOrder){
                    return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
                }
                //订单状态校验：待支付或已支付待处理可取消
                if(tbOrder.getOrderState() != OrderStatusEnum.WAIT_PAYMENT.value
                        && tbOrder.getOrderState() != OrderStatusEnum.WAIT_DELIVER.value){
                    return JSONResult.error(CommonErrorEnum.ORDER_STATUS_ERROR);
                }

                // 如果订单已支付，需要先退款
                boolean needRefund = (tbOrder.getPayStatus() == OrderPayStatusEnum.PAY.value);
                boolean refundSuccess = true;

                if (needRefund) {
                    log.info("订单已支付，开始执行退款操作，订单号：{}", orderno);

                    // 获取支付类型
                    String payType = tbOrder.getPayType();
                    if (StringUtils.isBlank(payType)) {
                        payType = PaymentConstants.PaymentType.WECHAT;
                    }

                    // 获取支付策略并执行退款
                    PaymentStrategy paymentStrategy = paymentStrategyFactory.getStrategy(payType);
                    if (paymentStrategy != null) {
                        refundSuccess = paymentStrategy.refund(
                            tbOrder.getOrderNumber(),
                            tbOrder.getOutOrderNo(),
                            tbOrder.getOrderPayAmount(),
                            tbOrder.getOrderPayAmount(),
                            "用户取消订单"
                        );

                        if (refundSuccess) {
                            // 退款成功，更新退款相关字段
                            tbOrder.setRefundStatus(PaymentConstants.RefundStatus.SUCCESS);
                            tbOrder.setRefundRemark("取消订单自动退款");
                            tbOrder.setApplyRefundTime(LocalDateTime.now());
                            tbOrder.setAgreeRefundTime(LocalDateTime.now());
                            tbOrder.setRefendTime(LocalDateTime.now());
                            log.info("订单退款成功，订单号：{}", orderno);
                        } else {
                            // 退款失败，设置退款状态为失败
                            tbOrder.setRefundStatus(PaymentConstants.RefundStatus.FAIL);
                            tbOrder.setRefundRemark("取消订单退款失败");
                            tbOrder.setApplyRefundTime(LocalDateTime.now());
                            log.error("订单退款失败，订单号：{}", orderno);
                        }
                    } else {
                        log.error("未找到支付策略，支付类型：{}", payType);
                        refundSuccess = false;
                    }
                }

                //取消订单
                tbOrder.setOrderState(OrderStatusEnum.CANCEL.value);
                tbOrder.setUpdateTime(LocalDateTime.now());
                tbOrder.setCancelTime(LocalDateTime.now());

                // 如果需要退款但退款失败，添加备注
                if (needRefund && !refundSuccess) {
                    tbOrder.setSystemRemark("订单已取消，但退款失败，请联系客服处理");
                }

                tbOrderMapper.updateById(tbOrder);

                // 释放相关资源（库存、优惠券）
                //releaseOrderResources(tbOrder);

                // 如果退款失败，返回错误信息
                if (needRefund && !refundSuccess) {
                    return JSONResult.error(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(), "订单已取消，但退款失败，请联系客服处理");
                }

            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }
        return JSONResult.ok();
    }

    @Override
    public JSONResult delOrder(String orderno) {
        if(StringUtils.isBlank(orderno)){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }
        int i = 0 ;
        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),orderno);
        RLock lock = this.redissonClient.getLock(aclKey);
        //加锁
        if(lock.tryLock()){
            try{
                //查找订单
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,orderno)
                                .eq(TbOrder::getIsDel,DeleteStatusEnum.NORMAL.value)
                );
                if(null == tbOrder){
                    return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
                }
                //已取消 已退款  已提取
                if(tbOrder.getOrderState() != OrderStatusEnum.FINISH.value
                        && tbOrder.getOrderState() != OrderStatusEnum.REFUNDED.value
                        && tbOrder.getOrderState() != OrderStatusEnum.CANCEL.value){
                    return JSONResult.error(CommonErrorEnum.ORDER_STATUS_ERROR);
                }
                tbOrder.setIsDel(DeleteStatusEnum.DELETE_SUCCESS.value);
                tbOrder.setUpdateTime(LocalDateTime.now());
                i = tbOrderMapper.updateById(tbOrder);
            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }
        return JSONResult.ok(i);
    }

    @Override
    public JSONResult refundOrder(String orderno) {
        if(StringUtils.isBlank(orderno)){
            return JSONResult.error(CommonErrorEnum.ORDERNO_IS_NULL);
        }
        int i = 0;
        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),orderno);
        RLock lock = this.redissonClient.getLock(aclKey);
        //加锁
        if(lock.tryLock()){
            try{
                //查找订单
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,orderno)
                                .eq(TbOrder::getIsDel,DeleteStatusEnum.NORMAL.value)
                );
                if(null == tbOrder){
                    return JSONResult.error(CommonErrorEnum.ORDER_IS_NULL);
                }
                // 已取消  已支付 -> 可退款
                if(tbOrder.getPayStatus() != OrderPayStatusEnum.PAY.value){
                    return JSONResult.error(CommonErrorEnum.REFUND_STATUS_ERROR);
                }

                if(tbOrder.getPayStatus() == OrderPayStatusEnum.PAY.value
                        && tbOrder.getOrderState() != OrderStatusEnum.CANCEL.value){
                    return JSONResult.error(CommonErrorEnum.REFUND_STATUS2_ERROR);
                }

                // 调用支付策略进行退款
                String payType = tbOrder.getPayType();
                if (StringUtils.isBlank(payType)) {
                    payType = PaymentConstants.PaymentType.WECHAT;
                }

                PaymentStrategy paymentStrategy = paymentStrategyFactory.getStrategy(payType);
                if (paymentStrategy == null) {
                    log.error("未找到支付策略，支付类型：{}", payType);
                    return JSONResult.error(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(), "未找到对应的支付策略");
                }
                
                // 执行退款操作
                boolean refundResult = paymentStrategy.refund(
                    tbOrder.getOrderNumber(),
                    tbOrder.getOutOrderNo(),
                    tbOrder.getOrderPayAmount(),
                    tbOrder.getOrderPayAmount(),
                    "用户申请退款"
                );

                if (refundResult) {
                    //设置 退款状态 - 退款成功
                    tbOrder.setOrderState(OrderStatusEnum.REFUNDED.value);
                    tbOrder.setUpdateTime(LocalDateTime.now());
                    tbOrder.setApplyRefundTime(LocalDateTime.now());
                    tbOrder.setAgreeRefundTime(LocalDateTime.now());
                    tbOrder.setRefendTime(LocalDateTime.now());
                    //退款成功
                    tbOrder.setRefundStatus(PaymentConstants.RefundStatus.SUCCESS);
                    tbOrder.setRefundRemark("退款成功");

                    // 释放库存和优惠券
                    // releaseOrderResources(tbOrder);

                    log.info("订单退款成功，订单号：{}", orderno);
                } else {
                    //设置 退款状态 - 退款失败，保持退款中状态
                    tbOrder.setOrderState(OrderStatusEnum.REFUND_PROGRESS.value);
                    tbOrder.setUpdateTime(LocalDateTime.now());
                    tbOrder.setApplyRefundTime(LocalDateTime.now());
                    //退款失败
                    tbOrder.setRefundStatus(PaymentConstants.RefundStatus.FAIL);
                    tbOrder.setRefundRemark("退款失败，请联系客服");

                    log.error("订单退款失败，订单号：{}", orderno);
                }

                i = tbOrderMapper.updateById(tbOrder);

                if (!refundResult) {
                    return JSONResult.error(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(), "退款失败，请联系客服");
                }

            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        } else {
            return JSONResult.error(CommonErrorEnum.REPEAT_SUBMIT_ORDER);
        }
        return JSONResult.ok(i);
    }

    @Override
    public boolean closeOrder(OrderMessage orderMessage) {
        String orderNo = orderMessage.getOrderNo();
        Long userId = orderMessage.getUserId();

        //加入分布式锁  防止订单重复提交
        String aclKey = String.format(RedisKeyEnum.CREATE_ORDER_KEY.getKey(),orderNo);
        RLock lock = this.redissonClient.getLock(aclKey);
        //加锁
        if(lock.tryLock()){
            try{

                // 1.查询数据库订单是否支付
                TbOrder tbOrder = tbOrderMapper.selectOne(
                        new LambdaUpdateWrapper<TbOrder>()
                                .eq(TbOrder::getOrderNumber,orderNo)
                                .eq(TbOrder::getUserId,userId)
                );
                //订单不存在
                if(null == tbOrder){
                    log.warn("订单不存在");
                    return true;
                }
                //已支付
                if(tbOrder.getOrderState() == OrderStatusEnum.WAIT_DELIVER.value){
                    log.warn("订单已支付成功");
                    return true;
                }

                // 2.已创建 未支付
                if(tbOrder.getOrderState() == OrderStatusEnum.WAIT_PAYMENT.value){
                    // 3.去微信支付平台二次确认 TODO

                    //4. 微信返回结果判断
                    if(true){
                        //如果为空，则未支付成功，本地取消订单
                        tbOrder.setOrderState(OrderStatusEnum.CANCEL.value);
                        tbOrder.setUpdateTime(LocalDateTime.now());
                        tbOrder.setCancelTime(LocalDateTime.now());
                        tbOrder.setSystemRemark("超时支付，取消订单");
                        tbOrderMapper.updateById(tbOrder);

                        // 回滚 优惠券 库存等系统数据；TODO

                        //第三方关单服务

                        //Bamboo 取消订单 回滚库存
                        // TODO 取消bamboo订单
                        if(tbOrder.getChannelType().equals("1")){
                            cannelOrderBamboo(tbOrder);
                        }
                        if(tbOrder.getChannelType().equals("0")){
                            cancelOrderMax(tbOrder);
                        }

                        log.info("未支付成功，本地取消订单:{}",orderMessage);
                    }else{
                        //已支付 支付成功
                        tbOrder.setOrderState(OrderStatusEnum.WAIT_DELIVER.value);
                        tbOrder.setUpdateTime(LocalDateTime.now());
                        tbOrder.setPayTime(LocalDateTime.now());
                        tbOrderMapper.updateById(tbOrder);
                        tbOrder.setSystemRemark("系统反查，支付成功");
                        log.warn("支付成功，但是微信回调通知失败，需要排查问题:{}",orderMessage);
                    }
                }else{
                    //非创建状态 消息为已读
                    return true;
                }
            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }

        return false;
    }



    /**
     *  订单支付成功异步操作  TODO
     * @param orderPayTypeEnum
     * @param plainBody
     * @return
     */
    @Override
    @Transactional
    public boolean callback(OrderPayTypeEnum orderPayTypeEnum, String plainBody) {
        //1.格式化数据
        JSONObject jsonObject = JSONObject.fromObject(plainBody);
        //2.获取订单号
        String orderNo = "";
        //3.加锁 防止重复通知
        String aclKey = String.format(RedisKeyEnum.ORDER_NOTIFY_KEY.getKey(),orderNo);
        RLock lock = this.redissonClient.getLock(aclKey);
        //加锁
        if(lock.tryLock()){
            try{
                //4.查询订单 是否存在

                //5.根据支付类型判断 支付状态码

                // 6.  更新订单状态 以及其他信息 如 标记 优惠券扣减成功， 库存扣减成功；

                //2、只有用户支付成功才可以在max系统提交订单到外管局；并等待结果；

                //处理成功，返回成功
                return true;
            }finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }
        return false;
    }

    /**
     * 校验订单基础信息
     * @param req
     */
    private void checkOrderBaseInfo(CreateOrderVo req,TbOrder tbOrder) {
        //必填
        if(null != req.getRealId() && req.getRealId() <= 0){
            throw new ServiceException(CommonErrorEnum.REALNAME_IS_NULL);
        }
        if(null == req.getCurId() || req.getCurId() <= 0){
            throw new ServiceException(CommonErrorEnum.ORDER_CURRENCY_IS_NULL);
        }
        if(null == req.getBranchId() ||  req.getBranchId() <= 0){
            throw new ServiceException(CommonErrorEnum.ORDER_BRANCH_IS_NULL);
        }
        if(null == req.getPurpose() ||  req.getPurpose() <= 0){
            throw new ServiceException(CommonErrorEnum.ORDER_PUTPOSE_IS_NULL);
        }
        if(null ==  req.getOrderTotalAmount() || req.getOrderTotalAmount() <= 0){
            throw new ServiceException(CommonErrorEnum.ORDER_AMOUNT_IS_NULL);
        }
        if(null ==  req.getOrderPayAmount() || req.getOrderPayAmount() <= 0){
            throw new ServiceException(CommonErrorEnum.ORDER_PAY_AMOUNT_IS_NULL);
        }
        if(StringUtils.isBlank(req.getPayType())){
            throw new ServiceException(CommonErrorEnum.ORDER_PAY_TYPE_IS_NULL);
        }
        //提取时间
        if(StringUtils.isBlank(req.getExtractDate()) || StringUtils.isBlank(req.getExtractTime())){
            throw new ServiceException(CommonErrorEnum.ORDER_TIME_IS_NULL);
        }

        //用户数据 规范校验 如手机号 证件号等；正则校验


        //  ######   数据库校验数据
        //实名信息
        UserInformation userInformation = iUserInformationService.getById(req.getRealId());
        if(null == userInformation){
            throw new ServiceException(CommonErrorEnum.REALNAME_IS_NULL);
        }
        log.info("3.创建订单-实名信息获取：{}", userInformation.getId());

        //网点信息
        Integer branchId = req.getBranchId();
        TbBranch tbBranch = iTbBranchService.getBranchById(branchId);
        if(null == tbBranch){
            throw new ServiceException(CommonErrorEnum.BRANCH_IS_NULL);
        }
        // 是否接收在线订单；1接收、0不接收
        if (1 != tbBranch.getOnlineFlag()) {
            throw new ServiceException(CommonErrorEnum.BRANCH_NOT_ONLINE_FLAG);
        }
        log.info("4.创建订单-网点信息获取：{}", userInformation.getId());
        //用途 选择其他 其他选项必填；
        Integer purpose = req.getPurpose();
        TbOrderPurpose tbOrderPurpose = iTbOrderPurposeService.getPurposeById(purpose);

        if(null == tbOrderPurpose){
            throw new ServiceException(CommonErrorEnum.PURPOSE_IS_NULL);
        }

        // 后续改成 判断代码 TODO
        if(tbOrderPurpose.getPurposeName().equals("其他") && StringUtil.isBlank(req.getPurposeDescri())){
            throw new ServiceException(CommonErrorEnum.PURPOSE_OTHER_IS_NULL);
        }
        log.info("5.创建订单-用途信息：{},{}", JSONUtil.toJsonStr(tbOrderPurpose),req.getPurposeDescri());

        //获取手续费  暂无用
//        Tbfees tbfees = iTbfeesService.getFeesByBranchId(tbBranch.getId());
//        if((null != req.getFee() && req.getFee() >0) && null != tbfees){
//            //计算手续费
//            tbOrder.setFee(new BigDecimal(req.getFee()));
//        }

        //获取币种信息
        Integer curId = req.getCurId();
        CurrencyDto currencyById = iTbcurrencyService.getCurrencyById(curId);
        if(null == currencyById){
            throw new ServiceException(CommonErrorEnum.CURRENCY_IS_NULL);
        }


        //调用第三方接口 max 做用户购汇信息校验
        CheckUserRequest request = new CheckUserRequest();
        request.setAmount(BigDecimal.valueOf(req.getUsbAmt()));
        request.setCurrency(currencyById.getCurrency());
        request.setDocno(userInformation.getCard());
        request.setPersonName(userInformation.getName());
        ResponseData responseDataCheckUser = maxAPI.checkUser(request);
        log.info("max用户信息校验：{}",JSONUtil.toJsonStr(responseDataCheckUser));
        //
        if(!responseDataCheckUser.getCode().equals("200")){
            throw new ServiceException(Integer.valueOf(responseDataCheckUser.getCode()),responseDataCheckUser.getMessage());
        }

        if(tbBranch.getType().equals("1")){
            //如果是bammboo的网点，判断库存
            checkStockBamboo(tbBranch,currencyById,req);
        }


        log.info("6.创建订单-币种信息：{}", JSONUtil.toJsonStr(currencyById));

        BigDecimal couponAmount = new BigDecimal(0.00);
        TbCouponMrg couponById = null;
        //优惠券 选择优惠券才判断 用户领取的优惠券
        if(null != req.getCouponId() && req.getCouponId() != 0){
            Integer couponId = req.getCouponId();
            couponById = iTbCouponReceiveService.getCouponByRecId(couponId);
            if(null == couponById){
                throw new ServiceException(CommonErrorEnum.COUPEN_IS_NULL);
            }
            log.info("6.创建订单-优惠券：{}", JSONUtil.toJsonStr(couponById));
            //锁定优惠券
            iTbCouponReceiveService.lockById(req.getCouponId());
            couponAmount = couponById.getCouponPrice();
            //设置优惠券
            tbOrder.setCouponId(req.getCouponId());
            tbOrder.setCouponAmount(couponAmount);
        }

        //####设置信息
        tbOrder.setUserName(userInformation.getName());
        tbOrder.setUserPhone(userInformation.getPhone());
        tbOrder.setUserCard(userInformation.getCard());
        tbOrder.setBranchId(tbBranch.getId());
        tbOrder.setBranchName(tbBranch.getSitename());
        tbOrder.setBranchAddr(tbBranch.getSiteaddr());
        tbOrder.setPurpose(tbOrderPurpose.getId());
        tbOrder.setPurposeDescri(req.getPurposeDescri());
        tbOrder.setChannelType(tbBranch.getType());

        //时间格式化 处理 苹果特殊日期格式 2024-7-22
        LocalDate localDate = null;
        try {
            // 创建一个SimpleDateFormat对象，指定想要的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            // 创建一个Date对象，表示要格式化的日期
            // 注意：这里的日期是示例 "2024-7-10"，你可以根据需要修改这个值
            Date date = sdf.parse(req.getExtractDate());
            localDate = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();

        } catch (Exception e) {
            e.printStackTrace();
        }
        tbOrder.setExtractDate(localDate);
        tbOrder.setExtractTime(req.getExtractTime());

        //
        tbOrder.setOrderTotalAmount(new BigDecimal(req.getOrderTotalAmount()));
        if(null != req.getUrgentFees()){
            tbOrder.setUrgentFees(new BigDecimal(req.getUrgentFees()));
        }

        //零钱包总额
        BigDecimal walletTotalAmount = new BigDecimal(0.00);
//        TbCoinPurse byId = null;
        //零钱包判断
        String walletId = req.getWalletId();
        if(null != walletId && req.getWalletCount() > 0){
//            byId = tbCoinPurseService.getCoinByBizId(walletId);
            // 远程查询网点零钱包最大可购买数量
            ResponseData siteCoinResponseData = maxAPI.querySiteCoinBuyNum(walletId, tbBranch.getSitecode());
            if(null != siteCoinResponseData && "200".equals(siteCoinResponseData.getCode())){
                String data = siteCoinResponseData.getData();
                cn.hutool.json.JSONObject entries = JSONUtil.parseObj(data);
                if(entries.getInt("quantity") < req.getWalletCount()){
                    throw new ServiceException(CommonErrorEnum.MAX_SITE_COIN_BUY_NUM_ERROR.getErrorCode(),
                            String.format(CommonErrorEnum.MAX_SITE_COIN_BUY_NUM_ERROR.getErrorMsg(), entries.getStr("quantity")));
                }
            }

            //远程查询零钱包库存
            ResponseData responseData = maxAPI.queryCoinDetail(currencyById.getCurrency(), tbBranch.getSitecode());
            if(null != responseData && "200".equals(responseData.getCode())){
                String data = responseData.getData();
                cn.hutool.json.JSONObject entries = JSONUtil.parseObj(data);
                if(entries.getInt("quantity") < req.getWalletCount()){
                    throw new ServiceException(CommonErrorEnum.MAX_COIN_STOCK_ERROR);
                }

                //            if(null != byId){
                log.info("7.创建订单-零钱包：{}", JSONUtil.toJsonStr(responseData));
                BigDecimal price = entries.getBigDecimal("price");
                walletTotalAmount = NumberUtil.mul(req.getWalletCount(),price);
                tbOrder.setWalletId(walletId);
                tbOrder.setWalletCount(req.getWalletCount());
                tbOrder.setWalletParvalue(entries.getBigDecimal("parvalue"));
                tbOrder.setWalletAmount(walletTotalAmount);
//            }

            }

        }
        //币种信息
        tbOrder.setCurId(currencyById.getId());
        tbOrder.setCurCode(currencyById.getCurrency());
        tbOrder.setCurName(currencyById.getCurname());
        tbOrder.setCurRate(currencyById.getMarketPrice());
        tbOrder.setUsbAmt(new BigDecimal(req.getUsbAmt()));
        tbOrder.setOrderPayAmount(new BigDecimal(req.getOrderPayAmount()));
        //金额 前后端计价对比 如果价格有误需要提示前端刷新重新下单 TODO
        Double orderTotalAmount = req.getOrderTotalAmount();
        Double orderPayAmount = req.getOrderPayAmount();

        Double usbAmt = req.getUsbAmt();
        Double urgentFees = req.getUrgentFees();

        BigDecimal actualPrice = currencyById.getActualPrice();
        BigDecimal marketPrice = currencyById.getMarketPrice();

        //###   系统计算
        //兑换外币金额
        BigDecimal sysAmount = NumberUtil.div(NumberUtil.mul(NumberUtil.mul(marketPrice,currencyById.getRateUnit()), new BigDecimal(usbAmt)),currencyById.getRateUnit());
        //四舍五入 后2位
        BigDecimal sysOrderTotalAmount = NumberUtil.round(sysAmount.doubleValue(), 2, RoundingMode.HALF_UP);
        //实付金额
        BigDecimal sysPayAmount = NumberUtil.round(NumberUtil.sub(NumberUtil.add(sysOrderTotalAmount,urgentFees,walletTotalAmount),couponAmount),2, RoundingMode.HALF_UP);
        //验价格
        if(sysOrderTotalAmount.doubleValue() != orderTotalAmount || sysPayAmount.doubleValue() != orderPayAmount){
            throw new ServiceException(CommonErrorEnum.VERIFICATION_AMOUNT_ERROR);
        }
        // 订单利润 = （(售卖汇率-基础汇率） * 汇率单位 * 外币兑换数量  / 汇率单位） + 加急费 - 优惠券
        BigDecimal profit = NumberUtil.div(
                NumberUtil.mul(NumberUtil.mul(marketPrice.subtract(actualPrice), currencyById.getRateUnit()), new BigDecimal(usbAmt)),
                currencyById.getRateUnit()
        ).add(Convert.toBigDecimal(urgentFees)).subtract(couponAmount);
        BigDecimal orderProfit = NumberUtil.round(profit.doubleValue(), 2, RoundingMode.HALF_UP);
        tbOrder.setOrderProfit(orderProfit);

        // 校验最大兑换金额
        // CommonInfo commonInfo = iCommonInfoService.getOne(Wrappers.<CommonInfo>lambdaQuery()
        //         .eq(CommonInfo::getCodeValue, CommonEnum.NO_EXCEED_USD.name()));
        // if (null != commonInfo) {
        //     Tbmonusdrate tbmonusdrate = iTbmonusdrateService.getOne(Wrappers.<Tbmonusdrate>lambdaQuery()
        //             .eq(Tbmonusdrate::getMonth, DateUtil.format(new Date(), "yyyyMM"))
        //             .eq(Tbmonusdrate::getCurrency, currencyById.getCurrency()));
        //     if (null != tbmonusdrate) {
        //         // 其他币种最大兑换金额 = 等值美元金额 / 美元折算价 * 100
        //         BigDecimal maxUnit = NumberUtil.div(Convert.toBigDecimal(commonInfo.getAclValue()), tbmonusdrate.getStatrate(), 2).multiply(new BigDecimal("100"));
        //         if (maxUnit.compareTo(sysAmount) < 0) {
        //             throw new ServiceException(CommonErrorEnum.MAX_AMOUNT_ERROR);
        //         }
        //     }
        // }

        //校验结束 形成订单信息
        log.info("7.创建订单-校验成功：{}-{}", tbOrder.getOrderNumber(),JSONUtil.toJsonStr(tbOrder));
    }

    /**
     * 待支付订单设置 支付有效时间
     * @param list
     */

    private void setpayValidTime(List<OrderDto> list) {
        list.forEach(item->{
            // TODO  设置成枚举
            if(item.getOrderState() == OrderStatusEnum.WAIT_PAYMENT.value){
                //创建时间15分钟内有效
                LocalDateTime localDateTime = item.getCreateTime();
                // 创建一个LocalDateTime实例

                // 转换为ZonedDateTime
                ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());

                // 转换为Instant
                java.time.Instant instant = zonedDateTime.toInstant();

                // 转换为Date
                Date date = DateUtils.addMinutes(Date.from(instant), 15);
                item.setPayValidTime(date.getTime()+"");
            }
        });
    }

    /**
     * 数据RSA加密 姓名 手机号 证件号
     * @param list
     */
    private void setUserInfoToRsa(List<OrderDto> list) {
        //加密处理
        RSA rsa = new RSA(privateKeyStr, publicKeyStr);
        // 加密传输
        list.forEach(item->{
            item.setUserName(rsa.encryptBase64(item.getUserName(), KeyType.PublicKey));
            item.setUserPhone(rsa.encryptBase64(item.getUserPhone(), KeyType.PublicKey));
            item.setUserCard(rsa.encryptBase64(item.getUserCard(), KeyType.PublicKey));
        });
    }

    /**
     * 生成支付参数
     * 参考 @payinfo\demo\webPay\Pay.java 实现
     * @param order 订单信息
     * @return 支付参数
     */
    private PaymentParamDto packPayParam(TbOrder order) {
        try {
            // 获取当前登录用户的ID
            ReqContextUser reqContextUser = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
            Integer userId = reqContextUser.getId();

            // 从UserOpenInfo表中获取用户的微信openId
            UserOpenInfo userOpenInfo = iUserOpenInfoService.getOne(
                    new LambdaQueryWrapper<UserOpenInfo>()
                            .eq(UserOpenInfo::getUserId, userId)
                            .eq(UserOpenInfo::getReg, RegSourceEnum.WX.value())
                            .eq(UserOpenInfo::getIsDel, DeleteStatusEnum.NORMAL.value)
            );

            // 获取openId，如果没有找到用户的微信openId，使用默认值
            String openId = userOpenInfo != null ? userOpenInfo.getOpenId() : "";

            // 获取支付类型，默认为微信支付
            String payType = order.getPayType();
            if (StringUtils.isBlank(payType)) {
                payType = PaymentConstants.PaymentType.WECHAT;
            }

            // 根据支付类型获取支付策略
            PaymentStrategy paymentStrategy = paymentStrategyFactory.getStrategy(payType);
            if (paymentStrategy == null) {
                // 如果没有找到对应的支付策略，使用微信支付
                paymentStrategy = paymentStrategyFactory.getStrategy(PaymentConstants.PaymentType.WECHAT);
            }

            // 调用支付策略创建支付订单
            return paymentStrategy.createPayment(order, openId);
        } catch (Exception e) {
            log.error("生成支付参数异常", e);
            throw new ServiceException(CommonErrorEnum.SYSTEM_ERROR.getErrorCode(), "生成支付参数失败：" + e.getMessage());
        }
    }

    /**
     * 查询币种库存
     */
    private void checkStockBamboo(TbBranch tbBranch,CurrencyDto currencyDto,CreateOrderVo req){
        //调取第三获取库存信息
        QueryProductPageRequest queryProductPageRequest = new QueryProductPageRequest();
        BbProduct bbProduct = iBbProductService.getProductByCid(currencyDto.getId());
        queryProductPageRequest.setPage(1);
        queryProductPageRequest.setLimit(2);
        queryProductPageRequest.setCodes(Arrays.asList(bbProduct.getCode()));

        //查询是否有牌价信息 没有的 不让交易
        String outId = tbBranch.getOutId();
        BbDistributionOrg distributionByPid = iBbDistributionOrgService.getDistributionByPid(outId);
        queryProductPageRequest.setCustomerCode(distributionByPid.getOrgCode());

        queryProductPageRequest.setQuerySettlePrice(1);
        List<ProductResultResponse> productResultResponses = bamBooAPI.queryProductList(queryProductPageRequest);
        if(productResultResponses.isEmpty()){
            throw new ServiceException(CommonErrorEnum.CURRENCY_IS_NULL);
        }
        ProductResultResponse productResultResponse = productResultResponses.get(0);
        BigDecimal settlePrice = productResultResponse.getSettlePrice();
        if(null == settlePrice){
            throw new ServiceException(CommonErrorEnum.BAMBOO_SETTLEPRICE_IS_NULL);
        }
        String currencyFaceValue = productResultResponse.getCurrencyFaceValue();
        Integer unit = currencyDto.getUnit();
        //步值是否匹配
        if(Integer.parseInt(currencyFaceValue) != unit){
            throw new ServiceException(CommonErrorEnum.BAMBOO_CUR_FACEVALUE_ERROR);
        }
        BigDecimal availableStockNum = productResultResponse.getVirtualAvailableStockNum();
        Double usbAmt = req.getUsbAmt();
        Integer rateUnit = currencyDto.getUnit();
        BigDecimal count = NumberUtil.div(usbAmt, rateUnit);
        //判断库存
        if(NumberUtil.sub(availableStockNum,count).doubleValue() < 0){
            //库存不足
            throw new ServiceException(CommonErrorEnum.BAMBOO_CUR_STOCK_ERROR);
        }
    }

    /**
     * 锁定库存
     */
    private void lockStockBamboo(TbOrder tbOrder,CreateOrderVo req) {
        BbProduct bbProduct = iBbProductService.getProductByCid(req.getCurId());
        Double usbAmt = req.getUsbAmt();
        //锁定数量
        BigDecimal count = NumberUtil.div(usbAmt, NumberUtil.toBigDecimal(bbProduct.getCurrencyFaceValue()));
        List<FreezeStockRequest> requestList = new ArrayList<>();
        FreezeStockRequest freezeStockRequest = new FreezeStockRequest();
        freezeStockRequest.setBillCode("TJL"+tbOrder.getOrderNumber());
        freezeStockRequest.setMpCode(bbProduct.getCode());
        freezeStockRequest.setStoreCode(bbProduct.getStoreCode());
        freezeStockRequest.setStockNum(count.intValue());
        freezeStockRequest.setWarehouseCode(bbProduct.getWarehouseCode());
        requestList.add(freezeStockRequest);
        ResponseData responseData = bamBooAPI.freezeStock(requestList);
        if(responseData!= null &&  responseData.getCode().equals("0") ){
            //冻结成功 信息保存到数据库 对应订单
            tbOrderMapper.update(null,
                    new LambdaUpdateWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber,tbOrder.getOrderNumber())
                            .set(TbOrder::getFreezStock,JSONUtil.toJsonStr(freezeStockRequest))
            );
        }else{
            //锁定高库存失败
            throw new ServiceException(CommonErrorEnum.BAMBOO_LOCK_STOCK_FAIL);
        }
    }

    /**
     * 创建 Bamboo 订单
     * @param tbOrder
     */
    private void createOrderBamboo(TbOrder tbOrder) {
        //1.根据币种信息去第三查询对应币种信息
        BbProduct bbProduct = iBbProductService.getProductByCid(tbOrder.getCurId());

        //2.查询客户信息
        BranchOrgDto branchOrgDto = iBbDistributionOrgService.getDistributionByBId(tbOrder.getBranchId());

        //4.下单 第三方接口
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setOutOrderCode(tbOrder.getOrderNumber());

        orderRequest.setMerchantName(bbProduct.getMerchantName());//
        orderRequest.setMerchantCode(bbProduct.getMerchantCode());//
        orderRequest.setStoreName(bbProduct.getStoreName());//
        orderRequest.setStoreCode(bbProduct.getStoreCode());//

        orderRequest.setCustomerName(branchOrgDto.getOrgName());
        orderRequest.setCustomerCode(branchOrgDto.getOrgCode());

        orderRequest.setBranchCode(branchOrgDto.getBranchCode());
        orderRequest.setBranchName(branchOrgDto.getBranchName());

        orderRequest.setOrderCreateTime(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        orderRequest.setExpectDeliverDate(tbOrder.getExtractDate().format(formatter));
        orderRequest.setOrderDeliveryMethodId("24");

        //商品信息
        List<OrderItem> itemList = new ArrayList<>();
        OrderItem orderItem = new OrderItem();
        orderItem.setProductCname(bbProduct.getChineseName());
        orderItem.setCode(bbProduct.getCode());
        String currencyFaceValue = bbProduct.getCurrencyFaceValue();
        BigDecimal usbAmt = tbOrder.getUsbAmt();
        orderItem.setProductItemNum(NumberUtil.div(usbAmt,new BigDecimal(currencyFaceValue)));
        orderItem.setCurrencyCode(bbProduct.getCurrencyCode());
        orderItem.setCurrencyFaceValue(new BigDecimal(bbProduct.getCurrencyFaceValue()));
        orderItem.setCurrencyFaceAmount(new BigDecimal(bbProduct.getCurrencyFaceAmount()));
        orderItem.setWarehouseCode(bbProduct.getWarehouseCode());
        orderItem.setWarehouseName(bbProduct.getWarehouseName());
        String freezStock = tbOrder.getFreezStock();
        if(StringUtils.isNoneBlank(freezStock)){
            cn.hutool.json.JSONObject entries = JSONUtil.parseObj(freezStock);
            orderItem.setBillCode(entries.getStr("billCode"));
        }
        itemList.add(orderItem);
        orderRequest.setItemList(itemList);
        OrderResponse order = bamBooAPI.createOrder(orderRequest);
        if(order != null){
            //外部订单号入库
            String orderCode = order.getOrderCode();
            tbOrderMapper.update(null,
                    new LambdaUpdateWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber,tbOrder.getOrderNumber())
                            .set(TbOrder::getOutOrderNo,orderCode)
            );
        }else{
            throw new ServiceException(CommonErrorEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 取消订单
     * @param tbOrder
     */
    private void cannelOrderBamboo(TbOrder tbOrder) {

        //查询订单状态 TODO
//        OrderResultResponse orderResultResponse = BamBooAPI.queryOrder();

        //取消订单
        String outOrderNo = tbOrder.getOutOrderNo();
        if(StringUtils.isNoneBlank(outOrderNo)){
            cn.hutool.json.JSONObject entries = JSONUtil.parseObj(outOrderNo);
            if(entries.containsKey("orderCode")){
                CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
                cancelOrderRequest.setOrderCode(entries.getStr("orderCode"));
                cancelOrderRequest.setOrderCsCancelReason("不需要了");
                cancelOrderRequest.setOrderCancelDate(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd HH:mm:ss"));
                cancelOrderRequest.setOrderCanceOperate("plus");
                ResponseData responseData = bamBooAPI.cancelOrder(cancelOrderRequest);
                if(responseData !=null && responseData.getCode().equals("0")){
                    log.info("bamboo取消订单成功：{}",tbOrder.getOutOrderNo());
                }else{
                    Integer errorCode = CommonErrorEnum.BAMBOO_COMMON_ERROR.getErrorCode();
                    String errorMsg = String.format(CommonErrorEnum.BAMBOO_COMMON_ERROR.getErrorMsg(),responseData.getMessage());
                    //失败提示
                    throw new ServiceException(errorCode,errorMsg);
                }
            }

        }
        //解锁库存
        String freezStock = tbOrder.getFreezStock();
        if(StringUtils.isNoneBlank(freezStock)){
            //解锁库存
            cn.hutool.json.JSONObject entries = JSONUtil.parseObj(freezStock);
            List<FreezeStockRequest> requestList = new ArrayList<>();
            FreezeStockRequest freezeStockRequest = new FreezeStockRequest();
            freezeStockRequest.setBillCode(entries.getStr("billCode"));
            freezeStockRequest.setMpCode(entries.getStr("mpCode"));
            freezeStockRequest.setStoreCode(entries.getStr("storeCode"));
            freezeStockRequest.setStockNum(entries.getInt("stockNum"));
            freezeStockRequest.setWarehouseCode(entries.getStr("warehouseCode"));
            requestList.add(freezeStockRequest);
            ResponseData responseData = bamBooAPI.unFreezeStock(requestList);
            if(responseData !=null){
                log.info("bamboo解锁库存成功：{}",tbOrder.getOutOrderNo());
            }
        }
    }

    /**
     * 营业时间判断
     * @param extractDate：提取日期
     * @param isExtract：是否需要判断提取日期
     */
    private void checkBusinessTime(String extractDate, boolean isExtract) {
        List<CommonInfo> list = iCommonInfoService.getBaseInfoByCode(CommonEnum.BUSINESS_TIME_P);
        //营业时间
        if(!list.isEmpty()){
            Map<String, String> wxParamMap = list.stream().collect(Collectors.toMap(CommonInfo::getCodeValue, CommonInfo::getAclValue));
            String time = wxParamMap.get(CommonEnum.BUSINESS_TIME.name());
            String startTime = time.split("-")[0];
            String endTime = time.split("-")[1];
            // 定义营业开始和结束时间
            LocalTime openTime = LocalTime.of(Integer.valueOf(startTime.split(":")[0]), Integer.valueOf(startTime.split(":")[1]));
            LocalTime closeTime = LocalTime.of(Integer.valueOf(endTime.split(":")[0]), Integer.valueOf(endTime.split(":")[1]));

            // 判断当前时间是否在营业时间内
            LocalTime nowTime = LocalTime.now();
//            boolean isOpen = nowTime.isAfter(openTime) && nowTime.isBefore(closeTime);
//            if(!isOpen){
//                throw new ServiceException(CommonErrorEnum.NON_BUSINESS_HOURS_ERROR);
//            }

            if (isExtract) {
                // 营业时间结束xx分钟不能再选当天提取
                String timeToEnd = wxParamMap.get(CommonEnum.TIME_TO_END.name());

                boolean isAfter = nowTime.plusMinutes(Convert.toLong(timeToEnd)).isAfter(closeTime);
                if (isAfter) {
                    // 判断提取时间是否是当天
                    LocalDate nowDate = LocalDate.now();
                    LocalDate newExtractDate = LocalDate.parse(extractDate);
                    if (newExtractDate.isBefore(nowDate)) {
                        throw new ServiceException(CommonErrorEnum.EXTRACT_DATE_ERROR);
                    }
                    if (newExtractDate.isEqual(nowDate)) {
                        throw new ServiceException(CommonErrorEnum.SAME_TODAY_IS_NOT_EXTRACT_DATE);
                    }
                }
            }
        }
    }

    //####  Max
    //创建订单
    private Map<String,Object> createdOrderMax(TbOrder tbOrder){
        Map<String,Object> res  = new HashMap<>();

        CreateOrderRequest request = new CreateOrderRequest();
        TbOrderPurpose purposeById = iTbOrderPurposeService.getPurposeById(tbOrder.getPurpose());
        UserInformation userInformation = iUserInformationService.getRealNameByUid(tbOrder.getUserId());
        TbBranch branchById = iTbBranchService.getBranchById(tbOrder.getBranchId());

        request.setPersonName(tbOrder.getUserName());
        request.setDocno(userInformation.getCard());
        request.setLinktel(userInformation.getPhone());
        request.setCurrency(tbOrder.getCurCode());
        request.setCardamt(tbOrder.getOrderPayAmount());
        request.setUsbAmt(tbOrder.getUsbAmt());
        request.setBuyexchgtype(purposeById.getBizCode());
        request.setSitecode(branchById.getSitecode());
        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(tbOrder.getFreezStock());
        request.setCoinno(entries.getStr("outno"));
        request.setSummary("W");//默认微信支付
        request.setExtracCode(tbOrder.getWithdrawaCode());
        request.setExtracTime(DateUtil.format(tbOrder.getExtractDate().atStartOfDay(),"yyyy-MM-dd"));
        if(null != tbOrder.getCouponAmount()){
            request.setDisAmount(tbOrder.getCouponAmount());
        }
        ResponseData order = maxAPI.createOrder(request);
        log.error("max创建订单响应参数:{}",order);
        if(null != order && order.getCode().equals("200")){
            CreateOrderResponse createOrderResponse = JSONUtil.toBean(order.getData(), CreateOrderResponse.class);
            //外部订单号入库
            String orderCode = createOrderResponse.getIceno();
            tbOrderMapper.update(null, new LambdaUpdateWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber,tbOrder.getOrderNumber())
                            .set(TbOrder::getOutOrderNo,orderCode)
            );
            // 存入max水单地址
            iTbOrderImgService.addOrUpdateMaxIce(tbOrder.getOrderNumber(), createOrderResponse.getIcedownloadurl(),
                    createOrderResponse.getIceimgurl(), createOrderResponse.getIcepdfurl());
            res.put("flag",true);
            return res;
        }
        //发生异常 提示错误
        String msg = "系统异常，自动取消订单";
        if (order != null) {
            msg = String.format(CommonErrorEnum.MAX_COMMON_ERROR.getMsg(),order.getMessage());
        }
        res.put("flag",false);
        res.put("msg",msg);
        return res;
//        throw new ServiceException(CommonErrorEnum.MAX_COMMON_ERROR.getErrorCode(),msg);
    }
    //撤销订单
    private void cancelOrderMax(TbOrder tbOrder){
        //订单没有形成只能取消库存
//        ResponseData responseData = MaxAPI.cancelOrder(tbOrder.getOrderNumber());
//        if(null != responseData && responseData.getCode().equals("200")){
            //取消成功 如果有零钱包 要解锁
            if(StringUtils.isNoneBlank(tbOrder.getFreezStock())){
                cn.hutool.json.JSONObject entries = JSONUtil.parseObj(tbOrder.getFreezStock());
                LockCoinStockRequest request = new LockCoinStockRequest();
                request.setCoincode(tbOrder.getWalletId());
                request.setSitecode(entries.getStr("sitecode"));
                request.setCurcode(entries.getStr("curcode"));
                request.setOutno(entries.getStr("outno"));
                request.setLockcount(entries.getInt("lockcount"));
                ResponseData unLockCoinStock = maxAPI.unLockCoinStock(request);
                if(unLockCoinStock !=null && unLockCoinStock.getCode().equals("200")){
                    log.info("max解锁库存成功：{}",tbOrder.getOutOrderNo());
                }else{
                    throw new ServiceException(CommonErrorEnum.SYSTEM_ERROR);
                }
            }
//        }else{
//            throw new ServiceException(CommonErrorEnum.SYSTEM_ERROR);
//        }
    }



	@Override
	public void applyPayCheck(String orderNo) {
		// TODO Auto-generated method stub

	}

    @Override
    public PersonalMaxPurchaseLimitDto queryPersonalMaxPurchaseLimit(QueryPersonalMaxPurchaseLimitVo request) {
        // 查询max 接口
        ResponseData responseData = maxAPI.queryPersonalMaxPurchaseLimit(request);
        if (!responseData.getCode().equals("200")) {
            throw new ServiceException(Integer.valueOf(responseData.getCode()), responseData.getMessage());
        }
        // 对象转换
        return JSONUtil.toBean(responseData.getData(), new TypeReference<PersonalMaxPurchaseLimitDto>() {
        }, true);
    }
}
