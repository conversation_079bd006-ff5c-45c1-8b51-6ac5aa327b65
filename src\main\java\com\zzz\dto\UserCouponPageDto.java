package com.zzz.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@Builder
@ApiModel("用户已领取优惠券分页实体")
public class UserCouponPageDto {

    private Long totalPage;

    private Long totalRecord;

    private List<UserCouponDto> list;

}
