package com.zzz.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-23
 **/
@Data
@ApiModel("网点加急排期实体")
public class BranchUrgentDto {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "网点ID")
    private Integer branchId;

    @ApiModelProperty(value = "加急费，单位元")
    private BigDecimal fee;

    @ApiModelProperty(value = "加急时间：2024-06-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    @ApiModelProperty(value = "设置时间")
    private Date createDate;

    @ApiModelProperty(value = "有效状态，0有效，1失效；")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;
}
