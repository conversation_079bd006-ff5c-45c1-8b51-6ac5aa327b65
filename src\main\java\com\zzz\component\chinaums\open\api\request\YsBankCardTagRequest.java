package com.zzz.component.chinaums.open.api.request;

import com.zzz.component.chinaums.open.api.OpenApiRequest;
import com.zzz.component.chinaums.open.api.annotation.ApiField;
import com.zzz.component.chinaums.open.api.response.YsBankCardTagResponse;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2017/5/3
 * Time: 18:21
 * 所属模块：
 * 功能说明：盐商科技用户标签查询
 */
public class YsBankCardTagRequest  implements OpenApiRequest<YsBankCardTagResponse> {

    @ApiField(key = "data",required = true,desc = "json格式字符")
    private Object data;

    public Class<YsBankCardTagResponse> responseClass() {
        return YsBankCardTagResponse.class;
    }

    public String apiVersion() {
        return "v1";
    }

    public String apiMethodName() {
        return "盐商科技用户标签查询";
    }

    public String serviceCode() {
        return "/datacenter/smartverification/ysbankcardtag/query";
    }

    public boolean needToken() {
        return true;
    }
    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
