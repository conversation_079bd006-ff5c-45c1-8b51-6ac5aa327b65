package com.zzz.controller;


import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.zzz.annotation.RedissonLock;
import com.zzz.constants.ProjectConstant;
import com.zzz.entity.ReqContextUser;
import com.zzz.exception.ServiceException;
import com.zzz.mq.OrderMessage;
import com.zzz.response.JSONResult;
import com.zzz.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-23
 */
@ApiIgnore
@Api(tags = "测试相关",hidden = true)
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisUtil redisUtil;

//    @Autowired
//    private RabbitMqSender rabbitMqSender;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @ApiOperation(value = "测试")
    @GetMapping("test")
    public JSONResult test() {
        //登录
        StpUtil.login(1000);
        //登录用户id
        Object loginId = StpUtil.getLoginId();
        log.info("loginId:{}", loginId);
        //是否登录
        boolean isLogin = StpUtil.isLogin();
        log.info("isLogin:{}", isLogin);
        //获取token
        String token = StpUtil.getTokenValueByLoginId(1000);
        log.info("token:{}", token);
        //解析token
        JWT jwt = JWTUtil.parseToken(token);
        log.info("jwt-sub:{}", jwt.getPayload("sub"));
        //验证token
        boolean verify = JWTUtil.verify(token, "saToken20124zzz".getBytes());
        log.info("verify:{}", verify);
        return JSONResult.ok();
    }

    @ApiOperation(value = "登录")
    @GetMapping("login")
    public JSONResult login(@RequestParam String id) {
        ReqContextUser reqContextUser = ReqContextUser.create().setId(Integer.valueOf(id));
        // 第1步，先登录上
        StpUtil.login(id);
        // 第2步，获取 Token  相关参数
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        // 第3步，把用户其他信息存入session中
        StpUtil.getSession().set(ProjectConstant.USER, reqContextUser);
        // 第4步，返回给前端
        return JSONResult.ok(tokenInfo);
    }

    @ApiOperation(value = "登出")
    @GetMapping("logout")
    public JSONResult logout() {
        // 登出
        StpUtil.logout();
        return JSONResult.ok();
    }

    @ApiOperation(value = "redisson")
    @SneakyThrows
    @GetMapping("redisson")
    public JSONResult redisson() {
        RLock lock = this.redissonClient.getLock("redissonKey");
        if (lock.tryLock()) {
            try {
                log.info("-------------------获得redisson锁---------------------");
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                Thread.sleep(3000);
                stopWatch.stop();
                log.info("抽奖处理时长:{}", stopWatch.getTotalTimeMillis());
            } finally {
                lock.unlock();
                log.info("-------------------释放redisson锁---------------------");
            }
        }
        return JSONResult.ok();
    }

    @SneakyThrows
    @ApiOperation(value = "redisson注解")
    @GetMapping("redisson注解")
    @RedissonLock(key = "#uid", waitTime = 10000)
    public JSONResult redissonLock(@RequestParam String uid) {
        log.info("uid:", uid);
        Thread.sleep(5000);
        return JSONResult.ok();
    }

    @GetMapping("redis")
    public JSONResult redis() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "zzz");
        redisUtil.set("redisKey", jsonObject);
        Object object = redisUtil.get("redisKey");
        log.info("object:{}", object);
        return JSONResult.ok();
    }

    @GetMapping("exception")
    public JSONResult exception() {
        if (1 == 1) throw new ServiceException(5333, "测试抛异常");
        return JSONResult.ok();
    }

    @ApiOperation(value = "rabbitmq")
    @GetMapping("rabbitmq")
    public JSONResult rabbitmq() {
//        Map<String, Object> param = new HashMap<>();
//        param.put("id", "222");
//        rabbitMqSender.sendDelayedMessage(EnumsRabbitQueue.TEST_QUEUE, param);
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderNo("************");
        orderMessage.setUserId(18L);
        rabbitTemplate.convertAndSend("order.event.exchange","order.close.delay.routing.key",orderMessage);

        return JSONResult.ok();
    }


}
