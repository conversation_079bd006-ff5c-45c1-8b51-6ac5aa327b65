package com.zzz.controller.v1;


import com.zzz.dto.BannerDto;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 小程序轮播表 前端控制器
 * </p>
 * <AUTHOR>
 * @since 2024-08-29
 */
@Api(tags = "轮播管理")
@Slf4j
@RestController
@RequestMapping("/api/v1/banner")
public class TbBannerController {
    @Resource
    private ITbBannerService tbBannerService;

    /**
     * 轮播列表
     */
    @ApiOperation("轮播列表")
    @GetMapping("/get_banner_list")
    public JSONResult<List<BannerDto>> getBannerList() {
        return JSONResult.ok(tbBannerService.getBannerList());
    }

}
