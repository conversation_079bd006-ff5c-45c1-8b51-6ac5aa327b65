package com.zzz.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-08-10
 **/
@Data
public class AlipayPhoneVo {

    @ApiModelProperty(name = "token", value = "用户唯一token", required = true,example = "authusrBae7b39371a40419488f190aa141cbX25")
    @NotBlank
    private String token;


    @ApiModelProperty(name = "jsonStr", value = "手机号授权字符串", required = true,example = "{\"response\": \"\",\"sign\": \"\"}")
    @NotBlank
    private String jsonStr;
}
