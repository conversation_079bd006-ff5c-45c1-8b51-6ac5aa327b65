package com.zzz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zzz.dto.CurrencyDto;
import com.zzz.entity.Tbcurrency;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zzz.vo.CurrencyPageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 币种 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TbcurrencyMapper extends BaseMapper<Tbcurrency> {

    IPage<CurrencyDto> selectCurrencyPage(Page<CurrencyDto> page, @Param("req") CurrencyPageVo req);

    List<CurrencyDto> getCurrencyHot();

    List<CurrencyDto> getCurrencyListById(@Param("bId") String bId);
}
