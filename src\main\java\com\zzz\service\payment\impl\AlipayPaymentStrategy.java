package com.zzz.service.payment.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.zzz.config.ums.UmsPayConfig;
import com.zzz.constants.PaymentConstants;
import com.zzz.dto.PaymentParamDto;
import com.zzz.entity.TbOrder;
import com.zzz.exception.ServiceException;
import com.zzz.service.payment.PaymentStrategy;
import com.zzz.util.HttpRequestUtil;
import com.zzz.util.UmsPayUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝支付策略实现
 */
@Slf4j
@Service
public class AlipayPaymentStrategy implements PaymentStrategy {

    @Autowired
    private UmsPayConfig umsPayConfig;

    @Override
    public String getPaymentType() {
        return PaymentConstants.PaymentType.ALIPAY;
    }

    @Override
    public PaymentParamDto createPayment(TbOrder order, String userId) {
        try {
            // 生成商户订单号
            String merOrderId = UmsPayUtil.generateMerOrderId(umsPayConfig.getSourceCode(), 7);

            // 构建支付宝下单接口请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("merOrderId", merOrderId);
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());
            reqBody.put("instMid", PaymentConstants.InstMid.ALIPAY_MINI);
            reqBody.put("tradeType", PaymentConstants.TradeType.MINI);
            reqBody.put("totalAmount", order.getOrderPayAmount().multiply(new BigDecimal("100")).intValue());
            reqBody.put("orderDesc", "外币兑换-订单号: " + order.getOrderNumber());
            reqBody.put("notifyUrl", umsPayConfig.getNotifyUrl());
            reqBody.put("returnUrl", umsPayConfig.getReturnUrl());
            reqBody.put("attachedData", order.getOrderNumber());

            // 如果有支付宝用户ID，则添加到请求体中
            if (userId != null && !userId.isEmpty()) {
                reqBody.put("userId", userId);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建返回参数
            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", order.getOrderNumber());

            // 发送支付宝下单请求
            try {
                // 构建完整的请求URL
                String fullUrl = umsPayConfig.getAliPayUrl();

                // 构建请求头
                String authorization = "OPEN-BODY-SIG appId=" + umsPayConfig.getAppId() +
                        ",timestamp=" + timestamp +
                        ",nonce=" + nonce +
                        ",signature=" + signature;

                log.info("支付宝下单请求URL: {}", fullUrl);
                log.info("支付宝下单请求头: {}", authorization);
                log.info("支付宝下单请求体: {}", reqBodyJson);

                // 发送请求并获取响应
                String response = HttpRequestUtil.sendPostRequest(fullUrl, authorization, reqBodyJson);
                log.info("支付宝下单响应: {}", response);

                // 解析响应
                if (response != null) {
                    JSONObject responseJson = JSONObject.fromObject(response);
                    if (responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"))) {
                        // 下单成功，获取支付参数
                        log.info("支付宝下单成功，订单号: {}", order.getOrderNumber());

                        // 保存外部交易号
                        String targetOrderId = responseJson.optString("targetOrderId", "");
                        result.put("targetOrderId", targetOrderId);

                        // 获取支付参数
                        if (responseJson.containsKey("redirectUrl")) {
                            // 获取支付宝支付链接
                            String redirectUrl = responseJson.getString("redirectUrl");
                            log.info("支付宝支付链接: {}", redirectUrl);

                            // 构建支付参数DTO
                            return PaymentParamDto.builder()
                                    .orderNo(order.getOrderNumber())
                                    .outOrderNo(targetOrderId)
                                    .payUrl(redirectUrl)
                                    .payParams(JSONObject.fromObject(result).toString())
                                    .payType(PaymentConstants.PaymentType.ALIPAY)
                                    .build();
                        } else {
                            log.error("支付宝下单成功，但未返回redirectUrl参数，订单号: {}", order.getOrderNumber());
                            result.put("errMsg", "支付宝下单成功，但未返回redirectUrl参数");
                        }
                    } else {
                        // 下单失败
                        String errMsg = responseJson.containsKey("errMsg") ? responseJson.getString("errMsg") : "未知错误";
                        log.error("支付宝下单失败: {}", errMsg);
                        result.put("errMsg", errMsg);
                    }
                }
            } catch (Exception e) {
                log.error("发送支付宝下单请求异常", e);
                result.put("errMsg", "发送支付宝下单请求异常: " + e.getMessage());
            }

            // 如果到这里，说明下单失败或者没有获取到redirectUrl参数
            // 返回错误信息
            return PaymentParamDto.builder()
                    .orderNo(order.getOrderNumber())
                    .payParams(JSONObject.fromObject(result).toString())
                    .payType(PaymentConstants.PaymentType.ALIPAY)
                    .build();
        } catch (Exception e) {
            log.error("支付宝支付下单异常", e);
            throw new ServiceException("支付宝支付下单失败: " + e.getMessage());
        }
    }

    @Override
    public boolean queryPaymentStatus(String orderNo, String outOrderNo) {
        log.info("开始查询支付宝支付状态，订单号: {}, 外部订单号: {}", orderNo, outOrderNo);
        try {
            // 构建查询请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());

            // 优先使用外部订单号查询
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号查询: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号查询: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建请求头
            String authorization = "OPEN-BODY-SIG appId=" + umsPayConfig.getAppId() +
                    ",timestamp=" + timestamp +
                    ",nonce=" + nonce +
                    ",signature=" + signature;

            log.info("支付宝支付查询请求URL: {}", umsPayConfig.getQueryUrl());
            log.info("支付宝支付查询请求头: {}", authorization);
            log.info("支付宝支付查询请求体: {}", reqBodyJson);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getQueryUrl(), authorization, reqBodyJson);
            log.info("支付宝支付查询响应: {}", response);

            // 解析响应
            if (response != null) {
                JSONObject responseJson = JSONObject.fromObject(response);
                if (responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"))) {
                    // 查询成功
                    String status = responseJson.optString("status", "");
                    log.info("支付宝支付查询成功，支付状态: {}", status);
                    boolean isPaid = "PAID".equals(status) || "SUCCESS".equals(status);
                    log.info("支付宝支付状态判断结果: {}", isPaid ? "已支付" : "未支付");
                    return isPaid;
                } else {
                    String errCode = responseJson.optString("errCode", "");
                    String errMsg = responseJson.optString("errMsg", "");
                    log.error("支付宝支付查询失败，错误码: {}, 错误信息: {}", errCode, errMsg);
                }
            } else {
                log.error("支付宝支付查询响应为空");
            }

            return false;
        } catch (Exception e) {
            log.error("支付宝支付查询异常，订单号: {}, 外部订单号: {}", orderNo, outOrderNo, e);
            return false;
        }
    }

    @Override
    public boolean closePayment(String orderNo, String outOrderNo) {
        log.info("开始关闭支付宝支付，订单号: {}, 外部订单号: {}", orderNo, outOrderNo);
        try {
            // 构建关闭请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());

            // 优先使用外部订单号关闭
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号关闭: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号关闭: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建请求头
            String authorization = "OPEN-BODY-SIG appId=" + umsPayConfig.getAppId() +
                    ",timestamp=" + timestamp +
                    ",nonce=" + nonce +
                    ",signature=" + signature;

            log.info("支付宝支付关闭请求URL: {}", umsPayConfig.getCloseUrl());
            log.info("支付宝支付关闭请求头: {}", authorization);
            log.info("支付宝支付关闭请求体: {}", reqBodyJson);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getCloseUrl(), authorization, reqBodyJson);
            log.info("支付宝支付关闭响应: {}", response);

            // 解析响应
            if (response != null) {
                JSONObject responseJson = JSONObject.fromObject(response);
                boolean isSuccess = responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"));
                if (isSuccess) {
                    log.info("支付宝支付关闭成功，订单号: {}", orderNo);
                } else {
                    String errCode = responseJson.optString("errCode", "");
                    String errMsg = responseJson.optString("errMsg", "");
                    log.error("支付宝支付关闭失败，错误码: {}, 错误信息: {}", errCode, errMsg);
                }
                return isSuccess;
            } else {
                log.error("支付宝支付关闭响应为空");
            }

            return false;
        } catch (Exception e) {
            log.error("支付宝支付关闭异常，订单号: {}, 外部订单号: {}", orderNo, outOrderNo, e);
            return false;
        }
    }

    @Override
    public boolean refund(String orderNo, String outOrderNo, BigDecimal refundAmount, BigDecimal totalAmount, String refundReason) {
        log.info("开始支付宝退款，订单号: {}, 外部订单号: {}, 退款金额: {}, 总金额: {}, 退款原因: {}",
                orderNo, outOrderNo, refundAmount, totalAmount, refundReason);
        try {
            // 生成退款订单号
            String refundOrderId = UmsPayUtil.generateMerOrderId(umsPayConfig.getSourceCode(), 7);
            log.info("生成退款订单号: {}", refundOrderId);

            // 构建退款请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());
            reqBody.put("instMid", PaymentConstants.InstMid.ALIPAY_MINI);
            reqBody.put("refundAmount", refundAmount.multiply(new BigDecimal("100")).intValue());
            reqBody.put("refundOrderId", refundOrderId);
            reqBody.put("refundDesc", refundReason);

            // 优先使用外部订单号退款
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号退款: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号退款: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建请求头
            String authorization = "OPEN-BODY-SIG appId=" + umsPayConfig.getAppId() +
                    ",timestamp=" + timestamp +
                    ",nonce=" + nonce +
                    ",signature=" + signature;

            log.info("支付宝退款请求URL: {}", umsPayConfig.getRefundUrl());
            log.info("支付宝退款请求头: {}", authorization);
            log.info("支付宝退款请求体: {}", reqBodyJson);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getRefundUrl(), authorization, reqBodyJson);
            log.info("支付宝支付退款响应: {}", response);

            // 解析响应
            if (response != null) {
                JSONObject responseJson = JSONObject.fromObject(response);
                boolean isSuccess = responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"));
                if (isSuccess) {
                    log.info("支付宝退款成功，订单号: {}, 退款订单号: {}", orderNo, refundOrderId);
                } else {
                    String errCode = responseJson.optString("errCode", "");
                    String errMsg = responseJson.optString("errMsg", "");
                    log.error("支付宝退款失败，错误码: {}, 错误信息: {}", errCode, errMsg);
                }
                return isSuccess;
            } else {
                log.error("支付宝退款响应为空");
            }

            return false;
        } catch (Exception e) {
            log.error("支付宝支付退款异常，订单号: {}, 外部订单号: {}, 退款金额: {}", orderNo, outOrderNo, refundAmount, e);
            return false;
        }
    }
}
