package com.zzz.entity;

    import java.math.BigDecimal;

    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableName;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 零钱宝整钱包
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_coin_purse")
    @ApiModel(value="TbCoinPurse对象", description="零钱宝整钱包")
    public class TbCoinPurse implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "单价；388元/份")
    private BigDecimal price;

            @ApiModelProperty(value = "库存")
    private Integer quantity;

            @ApiModelProperty(value = "温馨提示")
    private String remarks;

    @ApiModelProperty(value = "详情介绍")
    private String detail;

            @ApiModelProperty(value = "网点id")
    private Integer branchId;

            @ApiModelProperty(value = "1零钱包2整钱包")
    private Integer type;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

            @ApiModelProperty(value = "状态0正常1关闭")
    private Integer state;

            @ApiModelProperty(value = "币种id")
    private Integer exchangerateId;

    @ApiModelProperty(value = "max零钱包id")
    private String outBizid;

}
