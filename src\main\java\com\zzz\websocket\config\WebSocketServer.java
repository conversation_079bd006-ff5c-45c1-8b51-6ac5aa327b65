package com.zzz.websocket.config;

import cn.hutool.json.JSONUtil;
import com.zzz.websocket.context.ReqWebSocket;
import com.zzz.websocket.context.ResWebSocket;
import com.zzz.websocket.util.SemaphoreUtils;
import com.zzz.websocket.util.WebSocketUsersUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.lang.reflect.Method;
import java.util.concurrent.Semaphore;

@Slf4j
@Component
@ServerEndpoint("/ws")    // 指定websocket 连接的url
public class WebSocketServer {

    /**
     * 默认最多允许同时在线人数1000
     */
    public static int socketMaxOnlineCount = 1000;

    private static Semaphore socketSemaphore = new Semaphore(socketMaxOnlineCount);

    @OnOpen
    public void onOpen(Session session) throws Exception {
        log.info("-----------进入@OnOpen-----------");
        boolean semaphoreFlag = false;
        // 尝试获取信号量
        semaphoreFlag = SemaphoreUtils.tryAcquire(socketSemaphore);
        if (!semaphoreFlag) {
            // 未获取到信号量
            log.error("当前在线人数超过限制数-{}", socketMaxOnlineCount);
            WebSocketUsersUtil.sendMessageToUserByText(session, "当前在线人数超过限制数：" + socketMaxOnlineCount);
            session.close();
        } else {
            // 获取到信号量
            // 添加用户
            WebSocketUsersUtil.put(session.getId(), session);
            log.info("建立连接 - {}", session);
            log.info("当前人数 - {}", WebSocketUsersUtil.getUsers().size());
        }
    }

    @OnClose
    public void onClose(Session session) {
        log.info("-----------进入@OnClose-----------");
        // 移除用户
        WebSocketUsersUtil.remove(session.getId());
        // 获取到信号量则需释放
        SemaphoreUtils.release(socketSemaphore);
        log.info("关闭连接 - {}", session);
    }

    @OnMessage
    public String onMsg(String message, Session session) {
        log.info("-----------进入@OnMessage-----------");
        log.info("从客户端：{} 收到内容为-->:{}", session.getId(), message);
        ReqWebSocket req = JSONUtil.toBean(JSONUtil.parseObj(message), ReqWebSocket.class);
        log.info("------req:{}", req);
        //根据传参 执行对应 方法
        ResWebSocket res = execute(req);
        log.info("------res:{}", res);
        return JSONUtil.toJsonStr(res);
    }

    /**
     * 抛出异常时处理
     */
    @OnError
    public void onError(Session session, Throwable exception) throws Exception {
        log.info("-----------进入@OnError-----------");
        if (session.isOpen()) {
            // 关闭连接
            session.close();
        }
        String sessionId = session.getId();
        log.info("连接异常 - {}", sessionId);
        log.info("异常信息 - {}", exception);
        // 移出用户
        WebSocketUsersUtil.remove(sessionId);
        // 获取到信号量则需释放
        SemaphoreUtils.release(socketSemaphore);
    }

    @SneakyThrows
    private ResWebSocket execute(ReqWebSocket req) {
        // 根据类名加载类
        Class<?> clazz = Class.forName("com.zzz.websocket.controller." + req.getModule());

        // 方法名和参数类型
        Class<?>[] parameterTypes = {ReqWebSocket.class};

        // 创建实例
        Object instance = clazz.getDeclaredConstructor().newInstance();

        // 获取方法
        Method method = clazz.getDeclaredMethod(req.getMethod(), parameterTypes);

        // 调用方法
        ResWebSocket res = (ResWebSocket) method.invoke(instance, req);
        return res;
    }
}
