package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.entity.BbProduct;
import com.zzz.mapper.BbProductMapper;
import com.zzz.service.IBbProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 商品表（Bamboo） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class BbProductServiceImpl extends ServiceImpl<BbProductMapper, BbProduct> implements IBbProductService {

    @Autowired
    private BbProductMapper bbProductMapper;

    @Override
    @Transactional
    public void addProduct(ProductResultResponse item) {
        Integer i = bbProductMapper.selectCount(
                new LambdaQueryWrapper<BbProduct>()
                        .eq(BbProduct::getId,item.getId())
        );
        if(i > 0)return;
        BbProduct bbProduct = SpringBeanUtil.copyProperties(item,BbProduct.class);
        bbProductMapper.insert(bbProduct);
    }

    @Override
    public BbProduct getProductByCid(Integer id) {
        return bbProductMapper.getProductByCid(id);
    }
}
