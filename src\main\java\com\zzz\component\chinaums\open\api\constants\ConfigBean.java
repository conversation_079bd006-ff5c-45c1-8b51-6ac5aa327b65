package com.zzz.component.chinaums.open.api.constants;

/**
 * Created with IntelliJ IDEA.
 * User: ZHANGWEI
 * Date: 2016/12/4
 * Time: 21:22
 * 所属模块：
 * 功能说明：
 */
public class ConfigBean {
    /*
    字符集格式
     */
    private String charSet = "UTF-8";
    /*
    是否是生产环境
     */
    private boolean isProd = false;

    private String version = "v2";
    /**
     * token申请失效后，重试次数
     */
    private int tokenAcquireReties = 3;
    /**
     * token提前申请的时间
     */
    private int tokenAcquireAheadInterval = 600;
    /*
    获取token servicecode
     */
    private String tokenServiceCode="/token/access";

    public String getCharSet() {
        return charSet;
    }

    public void setCharSet(String charSet) {
        this.charSet = charSet;
    }

    public boolean isProd() {
        return isProd;
    }

    public void setProd(boolean prod) {
        isProd = prod;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getTokenAcquireReties() {
        return tokenAcquireReties;
    }

    public void setTokenAcquireReties(int tokenAcquireReties) {
        this.tokenAcquireReties = tokenAcquireReties;
    }

    public int getTokenAcquireAheadInterval() {
        return tokenAcquireAheadInterval;
    }

    public void setTokenAcquireAheadInterval(int tokenAcquireAheadInterval) {
        this.tokenAcquireAheadInterval = tokenAcquireAheadInterval;
    }

    public String getTokenServiceCode() {
        return tokenServiceCode;
    }

    public void setTokenServiceCode(String tokenServiceCode) {
        this.tokenServiceCode = tokenServiceCode;
    }
}
