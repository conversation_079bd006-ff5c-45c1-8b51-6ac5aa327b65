package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 平台优惠券
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_coupon_mrg")
    @ApiModel(value="TbCouponMrg对象", description="平台优惠券")
    public class TbCouponMrg implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "id")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "优惠券名称")
    private String couponName;

            @ApiModelProperty(value = "金额")
    private BigDecimal couponPrice;

            @ApiModelProperty(value = "类型 1立减 2满减 ")
    private String couponType;

            @ApiModelProperty(value = "满减金额")
    private BigDecimal cutPrice;

            @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

            @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

            @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer couponStatus;

            @ApiModelProperty(value = "可领券用户类型")
    private String couponCustomerType;

            @ApiModelProperty(value = "限领数量")
    private String numberLimit;

            @ApiModelProperty(value = "过期状态  0未过期  1已过期（弃用，使用结束时间判断）")
    private Integer expired;

            @ApiModelProperty(value = "优惠卷说明")
    private String remarks;


}
