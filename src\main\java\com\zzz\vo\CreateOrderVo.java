package com.zzz.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("创建订单参数")
public class CreateOrderVo {


    @ApiModelProperty(value = " 币种ID，通过查询币种详情获取",required = true,example = "1")
    private Integer curId;

    /**
     * 实名认证接口返回ID
     */
    @ApiModelProperty(value = "实名认证ID，通过查询个人认证详情获取id",required = true,example = "1")
    private Integer realId;

    @ApiModelProperty(value = "提货网点id，当前选中的网点",required = true,example = "1")
    private Integer branchId;

    @ApiModelProperty(value = "用途id，用途列表获取",required = true,example = "1")
    private Integer purpose;

    @ApiModelProperty(value = "用途选择其他，此项必填",example = "其他")
    private String purposeDescri;

    @ApiModelProperty(value = "优惠券id，可选；用户已领优惠券列表",example = "1")
    private Integer couponId;

    @ApiModelProperty(value = "优惠券抵扣金额，前端计算，后端会验价",example = "300.00")
    private Double couponAmount;

    @ApiModelProperty(value = "外币兑换总金额，采用四舍五入，保留2位（公式：兑换外币金额数量1200USD*汇率7.0000（汇率的意思就是1USD卖人民币7.000元））",example = "300.00")
    private Double orderTotalAmount;

    @ApiModelProperty(value = "支付方式；WX：微信小程序" ,example = "WX")
    private String payType;

    @ApiModelProperty(value = " 实际金额，采用四舍五入，保留2位,如微信支付（公式：外币换算人民币总额 +（加急费+零钱包）-优惠券）")
    private Double orderPayAmount;

    @ApiModelProperty(value = "外币金额，数量；汇率基本单位整倍数",required = true,example = "100")
    private Double usbAmt;

    @ApiModelProperty(value = "加急费用；可选，通过获取加急详情获取",example = "50.00")
    @TableField("urgentFees")
    private Double urgentFees;

    @ApiModelProperty(value = "提取日期，格式：2024-06-30",required = true ,example = "2024-06-22")
    @TableField("extractDate")
    private String extractDate;

    @ApiModelProperty(value = "提取时间，示例为标准格式" ,required = true,example = "8月21日 周三 09:00-18:00")
    private String extractTime;

    @ApiModelProperty(value = "手续费，可选，通过获取手续费详情接口获取",example = "1.00")
    private Double fee;

    @ApiModelProperty(value = "零钱包ID，非必填",example = "1")
    private String walletId;

    @ApiModelProperty(value = "零钱包数量，非必填",example = "1")
    private Integer walletCount;
}
