package com.zzz.service;

import com.zzz.entity.TbOrder;
import com.zzz.vo.PayOrderVo;

import java.util.Map;

/**
 * UMS支付服务接口
 */
public interface IUmsPayService {

    /**
     * 生成支付参数
     * @param order 订单信息
     * @return 支付参数
     */
    Map<String, Object> generatePayParams(TbOrder order);

    /**
     * 处理支付回调
     * @param params 回调参数
     * @return 处理结果
     */
    boolean handlePayCallback(Map<String, String> params);
}
