package com.zzz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "User对象", description = "用户表")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户编码")
    private Integer no;

    @ApiModelProperty(value = "注册渠道 1 微信小程序 2 支付宝小程序 3 H5（弃用）")
    private Integer reg;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "头像地址")
    private String avatar;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "小程序用户唯一标识。包括但不限于微信、支付宝小程序")
    private String openId;

    @ApiModelProperty(value = "手机号是否验证 0 否 1 是")
    private Integer valid;

    @ApiModelProperty(value = "是否删除(NULL:已删除,0:未删除)")
    @TableLogic(value = "0", delval = "NULL")
    private Boolean isDel;

    @ApiModelProperty(value = "添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date addTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(update = "now()")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;


    @ApiModelProperty(value = "是否实名 0未实名 1以实名")
    private Integer realName;

    @ApiModelProperty(value = "渠道id，与 user_channel 表中 id 关联")
    private String channelId;

}
