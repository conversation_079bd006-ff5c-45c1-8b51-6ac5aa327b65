# SECS-II Communication Log - Corrected Sequence
# Host connected with equipment
[2025-05-29 14:30:15.123] INFO Host connected with equipment

# Establish Communication Request
[2025-05-29 14:30:16.234] SEND S1F13 → Establish Communication Request
                               <L,0>.
[2025-05-29 14:30:16.345] RECV ← S1F14 COMMACK=0
                               <L,2
                                   <B,1 '0'>
                                   <L,2
                                       <A,3 'AIS'>
                                       <A,3 '1.0'>
                                   >
                               >.

# Host Requests Online
[2025-05-29 14:30:17.456] SEND S1F17 → Host Requests Online
[2025-05-29 14:30:17.567] RECV ← S1F18 Acknowledge
                               <B,1 '0'>.

# [Do the follow scenario If S1F18 ONLACK = 0 or 2 else ignore and keep original mode.]
# Report Control Mode Change (Online Local or Online Remote)
[2025-05-29 14:30:18.678] RECV ← S6F11 Report Control Mode Change
                               <L,3
                                   <U4,1 '166'>
                                   <U4,1 '60001'>
                                   <L,1
                                       <L,2
                                           <U4,1 '60001'>
                                           <L,2
                                               <A,14 '20241219143018'>
                                               <A,12 'ONLINE_LOCAL'>
                                           >
                                       >
                                   >
                               >.
[2025-05-29 14:30:18.789] SEND S6F12 → Host reply Ack
                               <B,1 '0' [ACKC6]>.

# Start Dynamic Event
[2025-05-29 14:30:19.890] INFO Start Dynamic Event

# Disable All Event
[2025-05-29 14:30:20.001] SEND S2F37 → Disable All Event
                               <L,2
                                   <BOOLEAN,1 '0' [CEED]>
                                   <L,0 [CEIDCOUNT]>
                               >.
[2025-05-29 14:30:20.112] RECV ← S2F38 Acknowledge
                               <B,1 '0'>.

# Delete All Report Link
[2025-05-29 14:30:21.223] SEND S2F35 → Delete All Report Link
                               <L,2
                                   <U2,1 '0' [DATAID]>
                                   <L,0 [CEIDCOUNT]>
                               >.
[2025-05-29 14:30:21.334] RECV ← S2F36 Acknowledge
                               <B,1 '0'>.

# Delete All Report
[2025-05-29 14:30:22.445] SEND S2F33 → Delete All Report
                               <L,2
                                   <U2,1 '0' [DATAID]>
                                   <L,0 [RPTIDCOUNT]>
                               >.
[2025-05-29 14:30:22.556] RECV ← S2F34 Acknowledge
                               <B,1 '0'>.

# Define New Report
[2025-05-29 14:30:23.667] SEND S2F33 → Define New Report
                               <L,2
                                   <U2,1 '7777' [DATAID]>
                                   <L,11 [RPTIDCOUNT]
                                       <L,2
                                           <U2,1 '60001' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60002' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60002' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60003' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60003' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60004' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60004' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60005' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60005' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60005' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60007' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60005' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60008' [RPTID]>
                                           <L,3 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60005' [VID]>
                                               <A,5 '60012' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60016' [RPTID]>
                                           <L,3 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60010' [VID]>
                                               <A,5 '60011' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60009' [RPTID]>
                                           <L,2 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60005' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60020' [RPTID]>
                                           <L,4 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60006' [VID]>
                                               <A,5 '60010' [VID]>
                                               <A,5 '60020' [VID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60021' [RPTID]>
                                           <L,5 [VIDCOUNT]
                                               <A,5 '60001' [VID]>
                                               <A,5 '60006' [VID]>
                                               <A,5 '60010' [VID]>
                                               <A,5 '60021' [VID]>
                                               <A,5 '60024' [VID]>
                                           >
                                       >
                                   >
                               >.
[2025-05-29 14:30:23.778] RECV ← S2F34 Acknowledge
                               <B,1 '0'>.

# Define New Link Event
[2025-05-29 14:30:24.889] SEND S2F35 → Define New Link Event
                               <L,2
                                   <U2,1 '111' [DATAID]>
                                   <L,11 [CEIDCOUNT]
                                       <L,2
                                           <U2,1 '60001' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60001' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60002' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60002' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60003' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60003' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60004' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60004' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60005' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60005' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60007' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60007' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60008' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60008' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60009' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60009' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60016' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60016' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60020' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60020' [RPTID]>
                                           >
                                       >
                                       <L,2
                                           <U2,1 '60021' [CEID]>
                                           <L,1 [RPTIDCOUNT]
                                               <U2,1 '60021' [RPTID]>
                                           >
                                       >
                                   >
                               >.
[2025-05-29 14:30:24.990] RECV ← S2F36 Acknowledge
                               <B,1 '0'>.
# Enable New Event
[2025-05-29 14:30:26.101] SEND S2F37 → Enable New Event
                               <L,2
                                   <BOOLEAN,1 '1' [CEED]>
                                   <L,11 [CEIDCOUNT]
                                       <U2,1 '60001' [CEID]>
                                       <U2,1 '60002' [CEID]>
                                       <U2,1 '60003' [CEID]>
                                       <U2,1 '60004' [CEID]>
                                       <U2,1 '60005' [CEID]>
                                       <U2,1 '60007' [CEID]>
                                       <U2,1 '60008' [CEID]>
                                       <U2,1 '60009' [CEID]>
                                       <U2,1 '60016' [CEID]>
                                       <U2,1 '60020' [CEID]>
                                       <U2,1 '60021' [CEID]>
                                   >
                               >.
[2025-05-29 14:30:26.212] RECV ← S2F38 Acknowledge
                               <B,1 '0'>.

# End Dynamic Event
[2025-05-29 14:30:27.323] INFO End Dynamic Event

# 開啟所有報警 Turn on all alarms
[2025-05-29 14:30:28.434] SEND S5F3 → Turn on all alarms
                               <L,2
                                   <B,1 '1' [ALED]>
                                   <U2,1 '0' [ALID]>
                               >.
[2025-05-29 14:30:28.545] RECV ← S5F4 Enable/Disable Alarm Acknowledge
                               <B,1 '0'>.

# 關鍵參數收集
[2025-05-29 14:30:29.656] INFO 關鍵參數收集
[2025-05-29 14:30:30.767] SEND S1F3 → SVID…
                               <L,35 [SVIDCOUNT]
                                   <U4,1 '60005' [SVID]>
                                   <U4,1 '60006' [SVID]>
                                   <U4,1 '60007' [SVID]>
                                   <U4,1 '60008' [SVID]>
                                   <U4,1 '60013' [SVID]>
                                   <U4,1 '60014' [SVID]>
                                   <U4,1 '60015' [SVID]>
                                   <U4,1 '60016' [SVID]>
                                   <U4,1 '60017' [SVID]>
                                   <U4,1 '60022' [SVID]>
                                   <U4,1 '60023' [SVID]>
                                   <U4,1 '60024' [SVID]>
                                   <U4,1 '60025' [SVID]>
                                   <U4,1 '60026' [SVID]>
                                   <U4,1 '60027' [SVID]>
                                   <U4,1 '60028' [SVID]>
                                   <U4,1 '60029' [SVID]>
                                   <U4,1 '60030' [SVID]>
                                   <U4,1 '60031' [SVID]>
                                   <U4,1 '60032' [SVID]>
                                   <U4,1 '60033' [SVID]>
                                   <U4,1 '60034' [SVID]>
                                   <U4,1 '60035' [SVID]>
                                   <U4,1 '60036' [SVID]>
                                   <U4,1 '60037' [SVID]>
                                   <U4,1 '60038' [SVID]>
                                   <U4,1 '60039' [SVID]>
                                   <U4,1 '60040' [SVID]>
                                   <U4,1 '60041' [SVID]>
                                   <U4,1 '60042' [SVID]>
                                   <U4,1 '60043' [SVID]>
                                   <U4,1 '60044' [SVID]>
                                   <U4,1 '60045' [SVID]>
                                   <U4,1 '60046' [SVID]>
                                   <U4,1 '60047' [SVID]>
                               >.
[2025-05-29 14:30:30.878] RECV ← S1F4 Selected Equipment Status Data
                               <L,35
                                   <A,3 '001'>
                                   <A,3 '123'>
                                   <A,0 ''>
                                   <A,0 ''>
                                   <A,1 '0'>
                                   <A,1 '1'>
                                   <A,1 '0'>
                                   <A,1 '0'>
                                   <A,1 '0'>
                                   <A,2 '62'>
                                   <A,1 '0'>
                                   <A,1 '7'>
                                   <A,1 '7'>
                                   <A,1 '0'>
                                   <A,1 '1'>
                                   <A,1 '0'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '0'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '1'>
                                   <A,1 '3'>
                                   <A,1 '4'>
                                   <A,1 '5'>
                                   <A,1 '6'>
                                   <A,1 '8'>
                                   <A,2 '99'>
                               >.

# SECS-II Communication Log - Sequence Complete
# All steps completed according to SECS standard protocol