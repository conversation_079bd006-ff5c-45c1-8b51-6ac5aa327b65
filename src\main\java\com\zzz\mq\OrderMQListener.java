package com.zzz.mq;

import com.rabbitmq.client.Channel;
import com.zzz.service.ITbOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <p>
 *  系统关单服务
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-25
 **/
@Slf4j
@Component
@RabbitListener(queues = "${mqconfig.order_close_queue}")
public class OrderMQListener {

    @Autowired
    private ITbOrderService iTbOrderService;

    /**
     *
     * 消费重复消息，幂等性保证
     * 并发情况下如何保证安全
     *
     * @param orderMessage
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitHandler
    public void closeProductOrder(OrderMessage orderMessage, Message message, Channel channel) throws IOException {
        long msgTag = message.getMessageProperties().getDeliveryTag();
        try{
            boolean flag = iTbOrderService.closeOrder(orderMessage);
            if(flag){
                channel.basicAck(msgTag,false);
            }else {
                channel.basicReject(msgTag,true);
            }
        }catch (IOException e){
            log.error("定时关单失败:",orderMessage);
            channel.basicReject(msgTag,true);
        }
    }

}
