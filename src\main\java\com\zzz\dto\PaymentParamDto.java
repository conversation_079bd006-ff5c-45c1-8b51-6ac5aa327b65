package com.zzz.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentParamDto {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 外部参数。
     */
    private String outOrderNo;
    /**
     * 支付URL
     */
    private String payUrl;

    /**
     * 支付参数
     */
    private String payParams;

    /**
     * 支付类型
     */
    private String payType;
}
