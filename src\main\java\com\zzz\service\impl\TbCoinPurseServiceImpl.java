package com.zzz.service.impl;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.component.bamboo.response.ResponseData;
import com.zzz.component.max.MaxAPI;
import com.zzz.dto.CoinDto;
import com.zzz.dto.CurrencyDto;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.TbBranch;
import com.zzz.entity.TbCoinPurse;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.TbCoinBaseMapper;
import com.zzz.mapper.TbCoinPurseMapper;
import com.zzz.service.ITbBranchService;
import com.zzz.service.ITbCoinChildService;
import com.zzz.service.ITbCoinPurseService;
import com.zzz.service.ITbcurrencyService;
import com.zzz.util.CusNumberUtil;
import com.zzz.vo.CoinPurseVo;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;

/**
 * <p>
 * 零钱宝整钱包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since  2024-06-20
 */
@Service
public class TbCoinPurseServiceImpl extends ServiceImpl<TbCoinPurseMapper, TbCoinPurse> implements ITbCoinPurseService {
    @Autowired
    private TbCoinPurseMapper tbCoinPurseMapper;
    @Resource
    private TbCoinBaseMapper tbCoinBaseMapper;

    @Autowired
    private ITbCoinChildService tbCoinChildService;

    @Autowired
    private ITbcurrencyService tbcurrencyService;

    @Autowired
    private ITbBranchService tbBranchService;

    @Resource
    private MaxAPI maxAPI;

    @Override
    public CoinDto getCoinPurseBySiteId(CoinPurseVo req) {
        // 1、必传 ，数据非空校验
        if (CusNumberUtil.isNumber(req.getBranchId())) {
            throw new ServiceException(CommonErrorEnum.BRANCH_ID_IS_NULL);
        }
        if (CusNumberUtil.isNumber(req.getCurrencyId())) {
            throw new ServiceException(CommonErrorEnum.CURRENCY_ID_IS_NULL);
        }
        // 获取币种信息
        CurrencyDto currencyById = tbcurrencyService.getCurrencyById(req.getCurrencyId());
        TbBranch branchById = tbBranchService.getBranchById(currencyById.getBranchId());
        // 查询max 接口
        ResponseData responseData = maxAPI.queryCoinDetail(currencyById.getCurrency(), branchById.getSitecode());
        if (!responseData.getCode().equals("200")) {
            throw new ServiceException(Integer.valueOf(responseData.getCode()), responseData.getMessage());
        }
        // 对象转换
        return JSONUtil.toBean(responseData.getData(), new TypeReference<CoinDto>() {
        }, true);
    }

    @Override
    public TbCoinPurse getCoinByBizId(Integer walletId) {
        return tbCoinPurseMapper
            .selectOne(new LambdaQueryWrapper<TbCoinPurse>().eq(TbCoinPurse::getOutBizid, walletId));
    }
}
