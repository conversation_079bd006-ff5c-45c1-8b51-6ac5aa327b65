<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzz.mapper.TbcurrencyMapper">

    <select id="selectCurrencyPage" resultType="com.zzz.dto.CurrencyDto">
        SELECT
            MIN(cur.market_price) market_price,cur.currency,base.cur_name curname,base.pic_url url,country,base.rate_unit as rate_unit,base.min_unit as unit
        FROM
            tbcurrency cur
                LEFT JOIN tb_currency_base base ON base.cur_code = cur.currency
            WHERE base.`status` = 1 AND cur.is_del = 0 AND cur.currency != 'CNY'
            <if test="req.keywords != null and req.keywords != ''" >
                AND cur.currency LIKE concat('%',#{req.keywords},'%')
                OR cur.curname LIKE  concat('%',#{req.keywords},'%')
            </if>
        GROUP BY
            cur.curname
        ORDER BY
            base.sortno ASC
    </select>


<!--    热门币种查询-->
    <select id="getCurrencyHot" resultType="com.zzz.dto.CurrencyDto">
        SELECT
            MIN(cur.market_price) market_price,cur.currency,base.cur_name curname,base.pic_url url,country,base.rate_unit as rate_unit,base.min_unit as unit
        FROM
            tbcurrency cur
                LEFT JOIN tb_currency_base base ON base.cur_code = cur.currency
        WHERE
            base.`status` = 1
          AND base.popular = 1 AND cur.is_del = 0 GROUP BY cur.currency
        ORDER BY
            base.sortno ASC
    </select>


    <select id="getCurrencyListById" resultType="com.zzz.dto.CurrencyDto">
        SELECT
            cur.id, cur.branch_id, cur.currency, cur.curname, base.pic_url url, cur.country, cur.market_price, cur.sycn_time,
            base.rate_unit rate_unit,base.min_unit unit,base.min_amt min_unit
        FROM
            tbcurrency cur
                INNER JOIN tb_currency_base base ON base.cur_code = cur.currency
        WHERE
            cur.`status` = 1
            AND cur.is_del = 0
            <if test="bId != null and bId != ''">
               AND cur.branch_id = #{bId}
            </if>
        GROUP BY cur.currency
        ORDER BY
            base.popular DESC,base.sortno ASC
    </select>


</mapper>
