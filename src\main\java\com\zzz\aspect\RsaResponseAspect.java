package com.zzz.aspect;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zzz.response.JSONResult;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 18:15 2024/6/6
 * @ Description：rsa加密响应结果 切面
 * @ Version:     1.0
 */
@Slf4j
@Aspect
@Component
public class RsaResponseAspect {

    @Value("${server.rsa.privateKeyStr}")
    private String privateKeyStr;

    @Value("${server.rsa.publicKeyStr}")
    private String publicKeyStr;

    @Around("@annotation(com.zzz.annotation.RsaResponse)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        //暂时不设计入参，此处不取任何注解的参数值
        //RsaResponse rsaResponse = method.getAnnotation(RsaResponse.class);
        JSONResult proceed = (JSONResult) joinPoint.proceed();
        /**
         * 响应200 data数据不为NULL 对data进行rsa加密响应
         */
        if (proceed != null && proceed.getCode().equals(200) && proceed.getData() != null) {
            //对data进行rsa加密
            JSONObject data = JSONUtil.parseObj(proceed.getData());
            //私钥加密，公钥解密
            RSA rsa = new RSA(privateKeyStr, publicKeyStr);
            //使用私钥加密，前端需要用公钥解密
            byte[] encrypt = rsa.encrypt(StrUtil.bytes(JSONUtil.toJsonStr(data), CharsetUtil.CHARSET_UTF_8), KeyType.PrivateKey);
            //改变响应出去的值 base64输出
            proceed.setData(Base64.encode(encrypt));
        }
        return proceed;
    }

    public static void main(String[] args) {

        //获得私钥
        String privateKeyBase64 = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKvJbT6YT8CMTtKXBayqM4cmSSBPLTxzguRJBY+IC71PlrVKMzPwymccj5QE4Q76cPclCszuvYnR58oYw3bE2eeW1qqKetLI2ipBoXCFeOK33zZL5VZSkOQxuDzqSHs/9BS+kfMNGrN4hfdBjVC+C0l/GLDxE4Q9I1RsbhqVa5/hAgMBAAECgYA/+eEEecbhx58nUHUdi7lq8Lg1HyeSptYtXICEpr7wfu56j6qoAlnusrVKA9MpN93QPOxby+GsrQ6stjI9Tix0NfxpR00O3fyv9Q9xiGJwn2dA23z2ak+SvKLfUAKkoTrGtfz8keyJezC0VNErd0Q9GMpeUDsly2Jd+DtKplY90QJBANaC0Wz60WBPNr+MpTab6qCNMfFNAGeFsiRkoejiUOek3Mr09KezjhJkdMTFrKsVfCxCPrpcqchkt6uKleOJnEUCQQDNAy++xa5IwYO18bFEJNaXbos2BcQnCi13XKW56x1Zxgv2zhX/MEEE0FzpPT0BsC8hm+ApVhKuTi3hcXblP2TtAkEArJQ21Uy9YpSYYWdQDC1IiL7P7Wib6K2hcrIbqPdbS+JRWF/W7Y0Bwj5by2uVzVbNvfXV7a1bU0PkmPg4OwWdWQJAPQZZy/uj6VxRDHOpRJOCnQj2v1nwDX7mEcWuPy1RS0u8e/bUbWW7tOchAPyNkFcVcHepviQ041W6KoVP0uWP8QJBAI1DW+Z1toBBQvfBWzY+ZAB7g4YxMpoNs9+mBUQJ78wmgVpimd0PZI6++J6nwyzvbhC2WwuiH6Xv9ekzVUH1IEE=";
        log.info("privateKeyBase64:{}", privateKeyBase64);
        //获得公钥
        String publicKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCryW0+mE/AjE7SlwWsqjOHJkkgTy08c4LkSQWPiAu9T5a1SjMz8MpnHI+UBOEO+nD3JQrM7r2J0efKGMN2xNnnltaqinrSyNoqQaFwhXjit982S+VWUpDkMbg86kh7P/QUvpHzDRqzeIX3QY1QvgtJfxiw8ROEPSNUbG4alWuf4QIDAQAB";
        log.info("publicKeyBase64:{}", publicKeyBase64);
        //私钥加密，公钥解密
        RSA rsa = new RSA(privateKeyBase64, publicKeyBase64);
        byte[] encrypt2 = rsa.encrypt(StrUtil.bytes("我是一段测试aaaa", CharsetUtil.CHARSET_UTF_8), KeyType.PrivateKey);
        byte[] decrypt2 = rsa.decrypt(encrypt2, KeyType.PublicKey);
        String str1 = Base64.encode(encrypt2);
        log.info("私钥加密内容：{}", str1);
        String str = StrUtil.str(decrypt2, CharsetUtil.CHARSET_UTF_8);
        log.info("公钥解密内容：{}", str);
        //公钥加密，私钥解密
        byte[] encrypt3 = rsa.encrypt(StrUtil.bytes("18028786721", CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        String str2 = Base64.encode(encrypt3);
        log.info("公钥加密内容：{}", str2);
        byte[] decrypt3 = rsa.decrypt(encrypt3, KeyType.PrivateKey);
        String str3 = StrUtil.str(decrypt3, CharsetUtil.CHARSET_UTF_8);
        log.info("私钥解密内容：{}", str3);
    }
}
