package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *      库存信息
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class StockResultResponse {

    private String merchantName;
    private String merchantCode;
    private String warehouseCode;
    private String warehouseName;
    private Long itemId;
    private BigDecimal virtualStockNum;
    private BigDecimal actualVirtualAvailableStockNum;
    private String code;
    private String chineseName;
}
