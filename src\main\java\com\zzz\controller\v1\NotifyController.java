package com.zzz.controller.v1;

import com.zzz.emuns.OrderPayTypeEnum;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbOrderService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *   异步通知接口（微信，支付宝等支付成功的异步通知）
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-24
 **/
@ApiIgnore
@Api(hidden = true)
@Slf4j
@RestController
@RequestMapping("/api/v1/notify")
public class NotifyController {

    @Autowired
    private ITbOrderService iTbOrderService;

    /**
     * 微信支付成异步通知
     * 1、订单校验
     * 2、通知max 系统支付成功 ，可以提交外管局
     */
    @PostMapping("wx/notifyUrl")
    public String notifyUrlWx(HttpServletRequest request, HttpServletResponse response){
        log.info("bamboo系统数据推送：{}", JSONObject.fromObject(request.getParameterMap()).toString());

        //1. 获取报文

        //2. 验签

        if(true){
            //3.报文解析
            String plainBody = "";
            //4. 订单业务处理
            boolean isSuccess = iTbOrderService.callback(OrderPayTypeEnum.WX,plainBody);
            if(isSuccess){
                //业务处理成功，按第三方要求返回SUCCESS
                return "SUCCESS";
            }
        }
        // 返回 FAILED 第三方会 按频率进行支付成功消息推送
        return "FAILED";
    }

    /**
     * bamboo 系统数据通知。
     * @return  TODO
     */
    @PostMapping("/bamboo")
    public JSONResult bamboot(HttpServletRequest request, HttpServletResponse response){
        log.info("bamboo系统数据推送：{}", JSONObject.fromObject(request.getParameterMap()).toString());
        return JSONResult.ok();
    }
}
