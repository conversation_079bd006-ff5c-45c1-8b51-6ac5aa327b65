package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 
    * </p>
*
* <AUTHOR>
* @since 2024-06-20
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("common_info")
    @ApiModel(value="CommonInfo对象", description="")
    public class CommonInfo implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "配置名称")
    private String name;

            @ApiModelProperty(value = "分类")
    private String type;

            @ApiModelProperty(value = "配置代码")
    private String codeValue;

            @ApiModelProperty(value = "值")
    private String aclValue;

    @ApiModelProperty(value = "描述")
    private String remark;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;


}
