package com.zzz.websocket.context;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 11:54 2024/4/11
 * @ Description：websocket响应上下文
 * @ Version:     1.0
 */
@Data
@Accessors(chain = true)
public class ResWebSocket implements Serializable {

    //模块名称，对应Class 名称
    private String module;

    //方法名称
    private String method;

    //响应数据data
    private Object data;

    public static ResWebSocket create() {
        return new ResWebSocket();
    }
}
