package com.zzz.component.bamboo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <p>
 *  api 接口列表
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Component
public class ApiUrlConst {

    @Value("${api.bambooBaseUrl}")
    private String bambooBaseUrl;

    public String getBambooBaseUrl() {
        return bambooBaseUrl;
    }

    /**
     * 请求地址
     */
    //主域名
    // private static final String URL ="http://bamboo-test.travelexcn.com";

    // 创建订单
    public String getCreateOrderUrl() {
        return getBambooBaseUrl() + "/open-api/oms-api/order/create";
    }
    // 取消订单
    public String getCancelOrderUrl() {
        return getBambooBaseUrl() + "/open-api/oms-api/order/cancel";
    }

    // 查询订单详情
    public String getOrderDetailUrl() {
        return getBambooBaseUrl() + "/open-api/oms-api/order/get";
    }

    // 查询库存
    public String getQueryStockListUrl() {
        return getBambooBaseUrl() + "/open-api/back-product-web/stock/list";
    }

    // 冻结
    public String getFreezeStockUrl() {
        return getBambooBaseUrl() + "/open-api/back-product-web2/product/stock/freeze";
    }

    // 解冻库存
    public String getUnfreezeStockUrl() {
        return getBambooBaseUrl() + "/open-api/back-product-web2/product/stock/unFreeze";
    }

    // 查询商品列表
    public String getQueryProductListUrl() {
        return getBambooBaseUrl() + "/open-api/back-product-web2/product/list";
    }

    // 查询客户
    public String getQueryCustomerListUrl() {
        return getBambooBaseUrl() + "/open-api/ouser-web/customer/list";
    }

    // 查询客户结算牌价列表
    public String getQuerySettlePriceListUrl() {
        return getBambooBaseUrl() + "/open-api/back-product-web/product/settilePrice/list";
    }

    // 查询可配送日期列表
    public String getQueryDeliveryDateListUrl() {
        return getBambooBaseUrl() + "/open-api/oms-api/order/deliveryDate/list";
    }

}
