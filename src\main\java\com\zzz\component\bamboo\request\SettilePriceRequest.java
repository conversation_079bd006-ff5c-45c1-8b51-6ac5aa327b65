package com.zzz.component.bamboo.request;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-08
 **/
@Data
public class SettilePriceRequest {

    private Integer page;
    private Integer limit;

    private String customerCode;

    private String currencyCodes;

    //页码 必传
    private Integer currentPage;
    //页大小，最大值：100 必传
    private Integer itemsPerPage;

}
