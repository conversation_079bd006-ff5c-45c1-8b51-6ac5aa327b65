package com.zzz.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-24
 **/
@Data
@ApiModel("网点手续费实体")
public class TbfeesDto {

    @ApiModelProperty(value = "自增，唯一")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "所属网点ID")
    private Integer branchId;

    @ApiModelProperty(value = "手续费收取方式，超过界限时：1 按比例、2按固定金额，默认为1")
    private Integer feesType;

    @ApiModelProperty(value = "兑换金额低于limitamt时，收取手续费金额")
    private BigDecimal feesAmt;

    @ApiModelProperty(value = "续费收取界限，等于0时，全部收取feesamt金额的手续费")
    private BigDecimal limitAnt;

    @ApiModelProperty(value = "超过界限是按比例或固定金额收取手续费，LIMITAMT=0时无效")
    private BigDecimal feesRate;

    @ApiModelProperty(value = "手续费封顶值，等于0时不封顶；LIMITAMT=0时无效")
    private BigDecimal feesMax;


    @ApiModelProperty(value = "状态，1 可用、0停用")
    private Integer status;
}
