package com.zzz.service;

import com.zzz.dto.BranchPageDto;
import com.zzz.dto.BranchUrgentDto;
import com.zzz.dto.CurrencyPageDto;
import com.zzz.entity.TbBranch;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;
import com.zzz.vo.BranchPageVo;

import java.util.List;

/**
 * <p>
 * 网点 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbBranchService extends IService<TbBranch> {

    BranchPageDto getBranchPage(BranchPageVo branchPageVo);

    TbBranch getBranchById(Integer branchId);

    JSONResult<List<BranchUrgentDto>> getUrgentListByBranchid(Integer id);

    void addBranch(Integer id, String branchCode, String branchName,
                   String contactPerson, String contactTelephone, int i);

    List<TbBranch> getBranchList(Integer type);
}
