package com.zzz.component.bamboo.request;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class QueryCustomerPageRequest {

    //页码 必传
    private Integer page;
    //页大小，最大值：100 必传
    private Integer limit;
//    //     客户编码
    private String orgCode;
    //     客户名称
    private String orgName;
    //     网点名称
    private String branchName;
    //     客户注册省Code
    private String registeredProvinceCode;
    //     客户注册省name
    private String registeredProvinceName;
    //     客户注册市Code
    private String registeredCityCode;
    //     客户注册市name
    private String registeredCityName;
    //     客户注册区Code
    private String registeredRegionCode;
    //     客户注册区name
    private String registeredRegionName;
}
