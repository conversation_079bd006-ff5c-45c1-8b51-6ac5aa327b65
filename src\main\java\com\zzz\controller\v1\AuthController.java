package com.zzz.controller.v1;


import cn.dev33.satoken.stp.StpUtil;
import com.zzz.annotation.RsaResponse;
import com.zzz.constants.ProjectConstant;
import com.zzz.dto.*;
import com.zzz.emuns.RegSourceEnum;
import com.zzz.entity.ReqContextUser;
import com.zzz.entity.User;
import com.zzz.response.JSONResult;
import com.zzz.service.IUserService;
import com.zzz.util.RsaDecryptUtil;
import com.zzz.util.UserContextUtil;
import com.zzz.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "登录授权")
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    @Autowired
    private IUserService iUserService;

    @Autowired
    private RsaDecryptUtil rsaDecryptUtil;

    /**
     * 微信授权
     */
    @ApiOperation(value = "微信授权")
//    @ApiResponses({
//            @ApiResponse(code=200,message = "【重要】错误代码200为请求成功，其他均为失败判断")
//    })
    @PostMapping("/wx")
    public JSONResult<WxAuthDto> wx(@RequestBody @Valid WxAuthVo req) {
        return iUserService.wxAuth(req);
    }

    /**
     * 获取手机号-微信
     */
    @ApiOperation(value = "获取手机号-微信")
    @PostMapping("/wx/phone")
    public JSONResult<WxPhoneDto> wxPhone(@RequestBody @Valid WxPhoneVo req) {
        return iUserService.wxPhone(req);
    }

    /**
     * 手机号登录
     */
    @ApiOperation(value = "手机号登录(获取token)")
    @PostMapping("/loginPhone")
    public JSONResult<LoginPhoneDto> loginPhone(@RequestBody @Valid LoginPhoneVo req) {
        return iUserService.loginPhone(req, RegSourceEnum.WX.value());
    }

    /**
     * 第一次登录后 验证手机号
     */
    @ApiOperation(value = "验证手机号（首次登录）")
    @PostMapping("/validPhone")
    public JSONResult validPhone(@RequestBody @Valid SmsValidVo req) {
        return iUserService.validPhone(req, RegSourceEnum.WX.value());
    }

    /**
     * 模拟需要登录的接口 拿到用户信息
     */

    @ApiOperation(value = "拿登录用户信息（前端忽略）")
    @PostMapping("/test")
    public JSONResult test() {
        //1.从线程中拿，通过全局拦截器实现的
        ReqContextUser user = UserContextUtil.get();
        log.info("线程中拿的User信息：{}", user);
        //1.从saToken中拿，实际上就是从redis拿，saToken把对应的信息存入了redis
        ReqContextUser user1 = (ReqContextUser) StpUtil.getSession().get(ProjectConstant.USER);
        log.info("saToken中拿的User信息：{}", user1);
        return JSONResult.ok();
    }

    /**
     * 验证网站：https://www.bchrt.com/tools/rsa/
     * @param req
     * @return
     */
    @RsaResponse
    @ApiOperation(value = "rsa响应加密（前端忽略）")
    @PostMapping("/test1")
    public JSONResult test1(@RequestBody @Valid TestRsa req) {
        log.info("------req属性解密前：{}",req);
        rsaDecryptUtil.process(req);
        log.info("------req属性解密后：{}",req);
        User u = new User();
        u.setPhone("1312312312");
        u.setOpenId("sssssssssssss");
        return JSONResult.ok(u);
    }

    /**
     *
     */
    @ApiOperation(value = "手机验证码登录（如果没有账号，自动创建账号，并返回token）")
    @PostMapping("/loginH5")
    public JSONResult<LoginPhoneDto> loginH5(@RequestBody @Valid LoginH5Vo req) {
        return iUserService.loginH5(req);
    }


    /**
     * 支付宝小程序 手机号 用户信息登录；
     */

    /**
     * 支付宝授权
     */
    @ApiOperation(value = "支付宝授权openid-支付宝")
//    @ApiResponses({
//            @ApiResponse(code=200,message = "【重要】错误代码200为请求成功，其他均为失败判断")
//    })
    @PostMapping("/alipay")
    public JSONResult<AlipayAuthDto> alipay(@RequestBody @Valid AlipayAuthVo req) {
        return iUserService.alipayAuth(req);
    }

    /**
     * 获取手机号-支付宝
     */
    @ApiOperation(value = "获取手机号-支付宝")
    @PostMapping("/alipay/phone")
    public JSONResult<AlipayPhoneDto> alipayPhone(@RequestBody @Valid AlipayPhoneVo req) {
        return iUserService.alipayPhone(req);
    }

    /**
     * 手机号登录
     */
    @ApiOperation(value = "手机号登录(获取token)-支付宝")
    @PostMapping("/alipay/loginPhone")
    public JSONResult<LoginPhoneDto> loginPhoneAliPay(@RequestBody @Valid LoginPhoneVo req) {
        return iUserService.loginPhone(req, RegSourceEnum.ALIPAY.value());
    }

    /**
     * 第一次登录后 验证手机号
     */
    @ApiOperation(value = "验证手机号（首次登录）-支付宝")
    @PostMapping("/alipay/validPhone")
    public JSONResult validPhoneAliPay(@RequestBody @Valid SmsValidVo req) {
        return iUserService.validPhone(req, RegSourceEnum.ALIPAY.value());
    }

}
