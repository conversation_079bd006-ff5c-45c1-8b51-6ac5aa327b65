package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-09
 **/
@Data
@ApiModel("H5手机验证码登录参数")
public class LoginH5Vo {

    @ApiModelProperty(name = "phone", value = "手机号", required = true,example = "13666666666")
    @NotBlank
    private String phone;

    @ApiModelProperty(name = "code", value = "验证码（暂固定：654321）", required = true,example = "654321")
    @NotBlank
    private String code;

    @ApiModelProperty(name = "typeConstant", value = "短信类型（前端暂时自己区分，如：loginH5）", required = true,example = "loginH5")
    @NotBlank
    private String typeConstant;
}
