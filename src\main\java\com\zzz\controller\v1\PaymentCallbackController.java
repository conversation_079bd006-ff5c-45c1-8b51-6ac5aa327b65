package com.zzz.controller.v1;

import com.zzz.emuns.OrderPayTypeEnum;
import com.zzz.response.JSONResult;
import com.zzz.service.ITbOrderService;
import com.zzz.service.IUmsPayService;
import net.sf.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付回调控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/payment")
@Api(tags = "支付回调接口")
public class PaymentCallbackController {

    @Autowired
    private ITbOrderService tbOrderService;

    @Autowired
    private IUmsPayService umsPayService;

    /**
     * UMS支付回调接口
     */
    @ApiOperation("UMS支付回调")
    @PostMapping("/notify/ums")
    public String umsPayNotify(HttpServletRequest request) {
        try {
            log.info("收到UMS支付回调");

            // 获取所有请求参数
            Map<String, String> params = new HashMap<>();
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                String paramValue = request.getParameter(paramName);
                params.put(paramName, paramValue);
            }

            log.info("UMS支付回调参数: {}", params);

            // 处理支付回调
            boolean result = umsPayService.handlePayCallback(params);

            // 调用订单服务处理回调
            if (result) {
                // 构建回调数据
                String orderNo = params.get("attachedData");
                String merOrderId = params.get("merOrderId");
                String totalAmount = params.get("totalAmount");
                String status = params.get("status");

                // 构建回调JSON
                JSONObject callbackJson = new JSONObject();
                callbackJson.put("orderNo", orderNo);
                callbackJson.put("merOrderId", merOrderId);
                callbackJson.put("totalAmount", totalAmount);
                callbackJson.put("status", status);
                callbackJson.put("payTime", params.get("payTime"));
                callbackJson.put("payType", params.get("payType"));

                // 调用订单服务处理回调
                tbOrderService.callback(OrderPayTypeEnum.BANK, callbackJson.toString());
                return "SUCCESS";
            } else {
                return "FAIL";
            }
        } catch (Exception e) {
            log.error("处理UMS支付回调异常", e);
            return "FAIL";
        }
    }

    /**
     * 支付页面跳转接口
     */
    @ApiOperation("支付页面跳转")
    @GetMapping("/return")
    public JSONResult<String> payReturn(@RequestParam("merOrderId") String merOrderId) {
        log.info("支付页面跳转, merOrderId: {}", merOrderId);
        return JSONResult.ok("支付处理中，请稍后查看订单状态");
    }
}
