package com.zzz.component.bamboo.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Data
public class DistributionOrgInfoResponse {

 /**
 * orgCode	String	0	客户编码
 * orgName	String	成功	客户名称
 * merchantName	String		所属商家
 * currencyCode	String		币种代码
 * warehouseCode	String		发货仓库
 * deliveryCycleDays	Integer		提供周期天数
 * remark	String		备注
 * cancelOrderFee	BigDecimal		取消订单手续费百分比
 * status	Integer		状态 1 正常 2 冻结业务
 * enterpriseName	String		企业名称
 * businessLicenceNo	String		营业执照号码
 * businessLicenceUrl	String		营业执照的快照的URL地址
 * businessScope	String		营业范围
 * businessTimeStart	String		营业时间开始
 * businessTimeEnd	String		营业时间截止
 * registeredProvinceCode	String		注册的省code
 * registeredProvinceName	String		注册的省名
 * registeredCityCode	String		注册的市code
 * registeredCityName	String		注册的市名
 * registeredRegionCode	String		注册的区code
 * registeredRegionName	String		注册的区名
 * registeredDetailAddress	String		注册的详细地址
 * branchList	List<OrgBranchInfoVO>		网点列表
 */
 private String orgCode;
 private String orgName;
 private String merchantName;
 private String currencyCode;
 private String warehouseCode;
 private Integer deliveryCycleDays;
 private String remark;
 private BigDecimal cancelOrderFee;
 private Integer status;
 private String enterpriseName;
 private String businessLicenceNo;
 private String businessLicenceUrl;
 private String businessScope;
 private String businessTimeStart;
 private String businessTimeEnd;
 private String registeredProvinceCode;
 private String registeredProvinceName;
 private String registeredCityCode;
 private String registeredCityName;
 private String registeredRegionCode;
 private String registeredRegionName;
 private String registeredDetailAddres;
 private List<OrgBranchInfo> branchList;


 private String id;
 private String createUserid;
 private String createTime;
 private String updateUserid;
 private String updateTime;
 private String createUsername;
 private String updateUsername;
 private String companyId;
 private String isDeleted;
 private String orgType;
 private String contactName;
 private String contactMobileNo;
 private String merchantId;
 private String loginUser;
 private String orgId;
 private String contactEmail;
 private String dataSource;
 private String dataSourceStr;
 private String bussinessTypeStr;
 private String classificationId;
 private String classificationName;
 private String merchantCode;
 private String contactProvinceName;
 private String contactCityName;
 private String contactRegionName;
 private String contactDetailAddress;
 private String contactProvinceCode;
 private String contactCityCode;
 private String contactRegionCode;
 private String innerOrgId;
 private String isInnerMerchant;
 private String innerOrgName;
 private String parentSupplierId;
 private String departmentId;
 private String departmentCode;
 private String departmentName;
 private String createTimeStr;
 private String orgTypeStr;
 private String registeredDetailAddress;
 private String enterpriseTel;
 private String statusString;
 private String invoicingSequence;
 private String invoicingSequenceStr;
 private String businessBrandType;
 private String businessBrandCodeList;
 private String businessBrandIdLists;
 private String businessCategoryType;
 private String businessCategoryCodeList;
 private String businessCategoryIdLists;
 private String warehouseName;
 private String warehouseId;
}
