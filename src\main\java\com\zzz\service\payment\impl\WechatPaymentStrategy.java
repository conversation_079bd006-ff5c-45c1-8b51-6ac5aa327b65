package com.zzz.service.payment.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zzz.config.ums.UmsPayConfig;
import com.zzz.constants.PaymentConstants;
import com.zzz.dto.PaymentParamDto;
import com.zzz.entity.TbOrder;
import com.zzz.exception.ServiceException;
import com.zzz.service.payment.PaymentStrategy;
import com.zzz.util.HttpRequestUtil;
import com.zzz.util.UmsPayUtil;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 微信支付策略实现
 */
@Slf4j
@Service
public class WechatPaymentStrategy implements PaymentStrategy {

    @Autowired
    private UmsPayConfig umsPayConfig;

    @Override
    public String getPaymentType() {
        return PaymentConstants.PaymentType.WECHAT;
    }

    @Override
    public PaymentParamDto createPayment(TbOrder order, String openId) {
        try {
            // 生成商户订单号
            String merOrderId = UmsPayUtil.generateMerOrderId(umsPayConfig.getSourceCode(), 7);

            // 构建微信统一下单接口请求体
            Map<String, Object> reqBody = UmsPayUtil.buildWxUnifiedOrderRequestBody(
                    merOrderId,
                    umsPayConfig.getMid(),
                    umsPayConfig.getTid(),
                    umsPayConfig.getSubMid(),
                    order.getOrderPayAmount(),
                    "外币兑换-订单号: " + order.getOrderNumber(),
                    umsPayConfig.getNotifyUrl(),
                    order.getOrderNumber(),
                    openId, merOrderId, merOrderId);

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建返回参数
            Map<String, Object> result = new HashMap<>();
            result.put("orderNo", order.getOrderNumber());

            // 发送微信统一下单请求
            try {
                // 构建完整的请求URL
                String fullUrl = umsPayConfig.getWxPayUrl();

                // 构建请求头
                String authorization = "OPEN-BODY-SIG AppId=" + "\"" + umsPayConfig.getAppId() + "\"" + ",Timestamp=" + "\""
                        + timestamp + "\"" + ",Nonce=" + "\"" + nonce + "\"" + ",Signature=" + "\"" + signature + "\"";

                log.info("微信统一下单请求URL: {}", fullUrl);
                log.info("微信统一下单请求头: {}", authorization);
                log.info("微信统一下单请求体: {}", reqBodyJson);

                // 发送请求并获取响应
                String response = HttpRequestUtil.sendPostRequest(fullUrl, authorization, reqBodyJson);
                log.info("微信统一下单响应: {}", response);

                // 解析响应
                if (response != null) {
                    JSONObject responseJson = JSONObject.fromObject(response);
                    if (responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"))) {
                        // 下单成功，获取支付参数
                        log.info("微信统一下单成功，订单号: {}", order.getOrderNumber());

                        // 保存外部交易号
                        String targetOrderId = responseJson.optString("merOrderId", "");
                        result.put("targetOrderId", targetOrderId);

                        // 获取jsPayRequest，这是给小程序调用的支付参数
                        if (responseJson.containsKey("miniPayRequest")) {
                            // 直接使用jsPayRequest作为支付参数
                            // 这个参数包含了小程序调用wx.requestPayment()所需的所有参数
                            JSONObject jsPayRequest = responseJson.getJSONObject("miniPayRequest");
                            log.info("微信支付参数jsPayRequest: {}", jsPayRequest);

                            // 构建支付参数DTO
                            return PaymentParamDto.builder()
                                    .orderNo(order.getOrderNumber())
                                    .outOrderNo(targetOrderId)
                                    .payParams(jsPayRequest.toString())
                                    .payType(PaymentConstants.PaymentType.WECHAT)
                                    .build();
                        } else {
                            log.error("微信统一下单成功，但未返回jsPayRequest参数，订单号: {}", order.getOrderNumber());
                            result.put("errMsg", "微信统一下单成功，但未返回jsPayRequest参数");
                        }
                    } else {
                        // 下单失败
                        String errMsg = responseJson.containsKey("errMsg") ? responseJson.getString("errMsg") : "未知错误";
                        log.error("微信统一下单失败: {}", errMsg);
                        result.put("errMsg", errMsg);
                    }
                }
            } catch (Exception e) {
                log.error("发送微信统一下单请求异常", e);
                result.put("errMsg", "发送微信统一下单请求异常: " + e.getMessage());
            }

            // 如果到这里，说明下单失败或者没有获取到jsPayRequest参数
            // 返回错误信息
            return PaymentParamDto.builder()
                    .orderNo(order.getOrderNumber())
                    .payParams(JSONObject.fromObject(result).toString())
                    .payType(PaymentConstants.PaymentType.WECHAT)
                    .build();
        } catch (Exception e) {
            log.error("微信支付下单异常", e);
            throw new ServiceException("微信支付下单失败: " + e.getMessage());
        }
    }

    @Override
    public boolean queryPaymentStatus(String orderNo, String outOrderNo) {
        log.info("开始查询微信支付状态，订单号: {}, 外部订单号: {}", orderNo, outOrderNo);
        try {
            // 构建查询请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());

            // 优先使用外部订单号查询
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号查询: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号查询: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建请求头
            String authorization = "OPEN-BODY-SIG AppId=" + "\"" + umsPayConfig.getAppId() + "\"" + ",Timestamp=" + "\""
                    + timestamp + "\"" + ",Nonce=" + "\"" + nonce + "\"" + ",Signature=" + "\"" + signature + "\"";

            log.info("微信支付查询请求URL: {}", umsPayConfig.getQueryUrl());
            log.info("微信支付查询请求头: {}", authorization);
            log.info("微信支付查询请求体: {}", reqBodyJson);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getQueryUrl(), authorization, reqBodyJson);
            log.info("微信支付查询响应: {}", response);

            // 解析响应
            if (response != null) {
                JSONObject responseJson = JSONObject.fromObject(response);
                if (responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"))) {
                    // 查询成功
                    String status = responseJson.optString("status", "");
                    log.info("微信支付查询成功，支付状态: {}", status);
                    boolean isPaid = "PAID".equals(status) || "SUCCESS".equals(status);
                    log.info("微信支付状态判断结果: {}", isPaid ? "已支付" : "未支付");
                    return isPaid;
                } else {
                    String errCode = responseJson.optString("errCode", "");
                    String errMsg = responseJson.optString("errMsg", "");
                    log.error("微信支付查询失败，错误码: {}, 错误信息: {}", errCode, errMsg);
                }
            } else {
                log.error("微信支付查询响应为空");
            }

            return false;
        } catch (Exception e) {
            log.error("微信支付查询异常，订单号: {}, 外部订单号: {}", orderNo, outOrderNo, e);
            return false;
        }
    }

    @Override
    public boolean closePayment(String orderNo, String outOrderNo) {
        log.info("开始关闭微信支付，订单号: {}, 外部订单号: {}", orderNo, outOrderNo);
        try {
            // 构建关闭请求体
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());

            // 优先使用外部订单号关闭
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号关闭: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号关闭: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);
            String signature = UmsPayUtil.getSignature(
                    umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey(),
                    timestamp,
                    nonce,
                    reqBodyJson);

            // 构建请求头
            String authorization = "OPEN-BODY-SIG appId=" + umsPayConfig.getAppId() +
                    ",timestamp=" + timestamp +
                    ",nonce=" + nonce +
                    ",signature=" + signature;

            log.info("微信支付关闭请求URL: {}", umsPayConfig.getCloseUrl());
            log.info("微信支付关闭请求头: {}", authorization);
            log.info("微信支付关闭请求体: {}", reqBodyJson);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getCloseUrl(), authorization, reqBodyJson);
            log.info("微信支付关闭响应: {}", response);

            // 解析响应
            if (response != null) {
                JSONObject responseJson = JSONObject.fromObject(response);
                boolean isSuccess = responseJson.containsKey("errCode") && "SUCCESS".equals(responseJson.getString("errCode"));
                if (isSuccess) {
                    log.info("微信支付关闭成功，订单号: {}", orderNo);
                } else {
                    String errCode = responseJson.optString("errCode", "");
                    String errMsg = responseJson.optString("errMsg", "");
                    log.error("微信支付关闭失败，错误码: {}, 错误信息: {}", errCode, errMsg);
                }
                return isSuccess;
            } else {
                log.error("微信支付关闭响应为空");
            }

            return false;
        } catch (Exception e) {
            log.error("微信支付关闭异常，订单号: {}, 外部订单号: {}", orderNo, outOrderNo, e);
            return false;
        }
    }

    @Override
    public boolean refund(String orderNo, String outOrderNo, BigDecimal refundAmount, BigDecimal totalAmount, String refundReason) {
        log.info("开始微信退款，订单号: {}, 外部订单号: {}, 退款金额: {}, 总金额: {}, 退款原因: {}",
                orderNo, outOrderNo, refundAmount, totalAmount, refundReason);
        try {
            // 生成退款订单号
            String refundOrderId = UmsPayUtil.generateMerOrderId(umsPayConfig.getSourceCode(), 7);
            log.info("生成退款订单号: {}", refundOrderId);

            // 构建退款请求体 - 参考Refund.java的RefundBody结构
            Map<String, Object> reqBody = new HashMap<>();
            reqBody.put("requestTimestamp", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            reqBody.put("mid", umsPayConfig.getMid());
            reqBody.put("tid", umsPayConfig.getTid());
            reqBody.put("instMid", PaymentConstants.InstMid.WECHAT_MINI);
            reqBody.put("refundAmount", refundAmount.multiply(new BigDecimal("100")).intValue());
            reqBody.put("refundOrderId", refundOrderId);
            reqBody.put("refundDesc", refundReason);

            log.info("微信退款配置信息 - 商户号: {}, 终端号: {}, AppId: {}, AppKey: {}",
                    umsPayConfig.getMid(), umsPayConfig.getTid(), umsPayConfig.getAppId(),
                    umsPayConfig.getAppKey().substring(0, 8) + "****");

            // 优先使用外部订单号退款
            if (outOrderNo != null && !outOrderNo.isEmpty()) {
                reqBody.put("targetOrderId", outOrderNo);
                log.info("使用外部订单号退款: {}", outOrderNo);
            } else {
                reqBody.put("merOrderId", orderNo);
                log.info("使用内部订单号退款: {}", orderNo);
            }

            // 转换为JSON字符串
            String reqBodyJson = JSONObject.fromObject(reqBody).toString();

            // 生成签名 - 参考Refund.java的getAuthorization方法
            String timestamp = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            String nonce = RandomUtil.randomString(16);

            // 使用与Refund.java相同的签名方式
            byte[] data = reqBodyJson.getBytes("utf-8");
            InputStream is = new ByteArrayInputStream(data);
            String bodySha256 = DigestUtils.sha256Hex(is);
            String signatureData = umsPayConfig.getAppId() + timestamp + nonce + bodySha256;

            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(umsPayConfig.getAppKey().getBytes("utf-8"), "HmacSHA256"));
            byte[] localSignature = mac.doFinal(signatureData.getBytes("utf-8"));
            String signature = Base64.encodeBase64String(localSignature);

            // 构建请求头 - 使用与Refund.java相同的格式
            String authorization = "OPEN-BODY-SIG AppId=\"" + umsPayConfig.getAppId() +
                    "\", Timestamp=\"" + timestamp +
                    "\", Nonce=\"" + nonce +
                    "\", Signature=\"" + signature + "\"";

            log.info("微信退款请求URL: {}", umsPayConfig.getRefundUrl());
            log.info("微信退款请求头: {}", authorization);
            log.info("微信退款请求体: {}", reqBodyJson);
            log.info("微信退款签名数据: {}", signatureData);

            // 发送请求并获取响应
            String response = HttpRequestUtil.sendPostRequest(umsPayConfig.getRefundUrl(), authorization, reqBodyJson);
            log.info("微信支付退款响应: {}", response);

            // 解析响应
            if (response != null && !response.trim().isEmpty()) {
                JSONObject responseJson = JSONObject.fromObject(response);

                // 检查响应状态
                if (responseJson.containsKey("errCode")) {
                    String errCode = responseJson.getString("errCode");
                    if ("SUCCESS".equals(errCode)) {
                        log.info("微信退款成功，订单号: {}, 退款订单号: {}, 响应: {}", orderNo, refundOrderId, response);
                        return true;
                    } else {
                        String errMsg = responseJson.optString("errMsg", "未知错误");
                        log.error("微信退款失败，订单号: {}, 错误码: {}, 错误信息: {}", orderNo, errCode, errMsg);
                        return false;
                    }
                } else {
                    log.error("微信退款响应格式异常，缺少errCode字段，响应: {}", response);
                    return false;
                }
            } else {
                log.error("微信退款响应为空或无效");
                return false;
            }

        } catch (Exception e) {
            log.error("微信支付退款异常，订单号: {}, 外部订单号: {}, 退款金额: {}", orderNo, outOrderNo, refundAmount, e);
            return false;
        }
    }
}
