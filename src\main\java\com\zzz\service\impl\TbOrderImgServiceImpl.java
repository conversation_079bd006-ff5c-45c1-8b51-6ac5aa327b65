package com.zzz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.entity.TbOrderImg;
import com.zzz.mapper.TbOrderImgMapper;
import com.zzz.service.ITbOrderImgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 订单图片表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2024-10-13
 */
@Service
public class TbOrderImgServiceImpl extends ServiceImpl<TbOrderImgMapper, TbOrderImg> implements ITbOrderImgService {
    @Resource
    private TbOrderImgMapper tbOrderImgMapper;

    /**
     * 新增/修改max水单地址
     * @param icedownloadurl 水单下载地址，二维码
     * @param iceimgurl      完整水单图片地址
     * @param icepdfurl      PDF存放地址
     * <AUTHOR> to 2024/10/13
     */
    @Override
    public Integer addOrUpdateMaxIce(String orderNumber, String icedownloadurl, String iceimgurl, String icepdfurl) {
        TbOrderImg tbOrderImg = lambdaQuery().eq(TbOrderImg::getOrderNumber, orderNumber).one();
        boolean isAdd = false;
        if (null == tbOrderImg) {
            tbOrderImg = new TbOrderImg();
            tbOrderImg.setOrderNumber(orderNumber);
            isAdd = true;
        }
        tbOrderImg.setIcedownloadurl(icedownloadurl);
        tbOrderImg.setIceimgurl(iceimgurl);
        tbOrderImg.setIcepdfurl(icepdfurl);
        if (isAdd) {
            return tbOrderImgMapper.insert(tbOrderImg);
        } else {
            return tbOrderImgMapper.updateById(tbOrderImg);
        }
    }
}
