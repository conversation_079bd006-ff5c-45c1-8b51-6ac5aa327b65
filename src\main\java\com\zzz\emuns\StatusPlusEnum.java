package com.zzz.emuns;

/**
 * puls 通用状态
 * <AUTHOR> to 2024/7/18
 */
public enum StatusPlusEnum {
    /**
     * 0：停用 1：启用
     */

    DISABLED(0, "停用"),
    ENABLE(1, "启用"),
    ;

    private final Integer value;
    private final String label;

    private StatusPlusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    /**
     * 根据指定value解析成枚举常量
     */
    public static StatusPlusEnum resolve(Integer value, StatusPlusEnum dv) {
        for (StatusPlusEnum s : values()) {
            if (s.value.equals(value)) {
                return s;
            }
        }
        return dv;
    }

    public static StatusPlusEnum resolve(Integer value) {
        return resolve(value, null);
    }

    /**
     * 判断 value 值是否是枚举里的值
     */
    public boolean matches(Integer value) {
        return this.value.equals(value);
    }

    public Integer value() {
        return value;
    }

    public String label() {
        return label;
    }
}
