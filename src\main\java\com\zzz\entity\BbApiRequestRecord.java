package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 请求记录表（Bamboo）
    * </p>
*
* <AUTHOR>
* @since 2024-07-12
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("bb_api_request_record")
    @ApiModel(value="BbApiRequestRecord对象", description="请求记录表（Bamboo）")
    public class BbApiRequestRecord implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "接口名称")
    private String name;

            @ApiModelProperty(value = "接口代码")
    private String apiCode;

            @ApiModelProperty(value = "接口地址")
    private String url;

            @ApiModelProperty(value = "请求参数")
    private String requestParams;

            @ApiModelProperty(value = "响应参数")
    private String responseParams;

            @ApiModelProperty(value = "请求时间")
    private LocalDateTime createTime;

            @ApiModelProperty(value = "响应时间")
    private LocalDateTime responseTime;


}
