package com.zzz.websocket;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.concurrent.TimeUnit;

/**
 * java连接websocket发送消息实例
 */
@Slf4j
public class MyClient extends WebSocketClient {

    private final Object LOCK = new Object();

    public MyClient(URI serverUri) {
        super(serverUri);
    }

    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info("开始进行连接...");
    }

    @Override
    public void onMessage(String s) {
        log.info("收到服务端的消息：{}", s);
        synchronized (LOCK) {
            LOCK.notify();
        }
    }

    public void doSend(String message) {
        log.info("发送到服务端的消息：{}", message);
        this.send(message);
        synchronized (LOCK) {
            try {
                LOCK.wait(20000);
            } catch (InterruptedException e) {
                log.error("发送消息，进行等待异常", e);
            }
        }
    }

    @Override
    public void onClose(int i, String s, boolean b) {
        log.info("关闭连接...");
        this.releaseLock();
    }

    @Override
    public void onError(Exception e) {
        log.error("连接异常", e);
        this.releaseLock();
    }

    private void releaseLock() {
        synchronized (LOCK) {
            LOCK.notify();
        }
    }

    public static void main(String[] args) {
        /*String url = "http://127.0.0.1:8081/login?username={0}&password={1}";
        //模拟登陆
        HttpResponse response = HttpUtil.createGet(MessageFormat.format(url, "client1", "client1")).execute();
        String token = response.body();

        HttpResponse response2 = HttpUtil.createGet(MessageFormat.format(url, "client2", "client2")).execute();
        String token2 = response2.body();*/

        String token3 = "aaaaaaaaaa";

        final String WS_SERVER_URL = "ws://127.0.0.1:8099/websocket";
        //创建客户端
        MyClient client = null;
        MyClient client2 = null;
        MyClient client3 = null;
        try {
            client = new MyClient(new URI(WS_SERVER_URL + "?token=" + token3));
            client2 = new MyClient(new URI(WS_SERVER_URL + "?token=" + token3));
            client3 = new MyClient(new URI(WS_SERVER_URL + "?token=" + token3));

            client.connectBlocking(10, TimeUnit.SECONDS);
            client2.connectBlocking(10, TimeUnit.SECONDS);
            client3.connectBlocking(10, TimeUnit.SECONDS);

            for (int i = 0; i < 10; i++) {
                client.doSend("你好帅" + i);
            }

            //sleep 模拟接收服务端发过来的消息
            Thread.sleep(90000000L);
        } catch (Exception e) {
            log.error("连接服务端异常", e);
        } finally {
            if (null != client) {
                client.close();
            }
            if (null != client2) {
                client2.close();
            }
            if (null != client3) {
                client3.close();
            }
        }
    }
}
