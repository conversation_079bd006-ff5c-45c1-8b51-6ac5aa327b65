package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import com.baomidou.mybatisplus.annotation.TableField;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 商品表（Bamboo）
    * </p>
*
* <AUTHOR>
* @since 2024-07-12
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("bb_product")
    @ApiModel(value="BbProduct对象", description="商品表（Bamboo）")
    public class BbProduct implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "biz_id", type = IdType.AUTO)
    private Integer bizId;

    private Long id;

    private BigDecimal freezeStockNum;

    private BigDecimal availableStockNum;

    private BigDecimal realStockNum;

    private BigDecimal virtualAvailableStockNum;

    private Long merchantId;

    private String merchantCode;

    private String merchantName;

    private Long storeId;

    private String storeName;

    private String storeCode;

    private Long warehouseId;

    private String warehouseCode;

    private String warehouseName;

    private Long mpId;

    private String chineseName;

    private String code;

    private Integer type;

    private String typeStr;

    private String channelCode;

    private Long categoryId;

    private String categoryName;

    private String fullIdPath;

    private Integer typeOfProduct;

    private Integer canSale;

    private String mainUnitName;

    private String mainPictureUrl;

    private Integer frontCanSale;

    private String currencyCode;

    private String currencyFaceValue;

    private String currencyFaceAmount;

    private BigDecimal settlePrice;

    private BigDecimal settleCurrencyCode;

    private String settleCurrencyName;


}
