package com.zzz.component;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zzz.component.bamboo.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * <p>
 *  请求工具类
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-07-07
 **/
@Slf4j
public class HttpUtils {

    private static final String  APPKEY = "250709d0ebbb401395a2360cec612aed";

    private static final String  ACCESSTOKEN = "c05331d6cc339f766c373048ae44730c";

    private static final String  APPSECRET = "b8a370498ec57bba0935d665e8f3eb34";

    /**
     * post 请求
     */
    public static ResponseData doPost(String url, String bodyString){

        log.info("url={}，bodyString={}",url,bodyString);
        String key = "accessToken="+ACCESSTOKEN+"&appKey="+APPKEY+bodyString+APPSECRET;
        log.info("key={}",key);
        String sign = org.springframework.util.DigestUtils.md5DigestAsHex(key.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        log.info("sign={}",sign);
        try{
            if(url.indexOf("?") > -1){
                url = url+"&appKey="+APPKEY+"&accessToken="+ACCESSTOKEN+"&sign="+sign;
            }else{
                url = url+"?appKey="+APPKEY+"&accessToken="+ACCESSTOKEN+"&sign="+sign;
            }

           HttpRequest post = HttpUtil.createPost(
                   url
           );
           post.header("OPEN-API","ACCESS_TOKEN");
           post.body(bodyString);
           HttpResponse execute = post.execute();
            log.info("url={}，response={}",url,execute.body());
            JSONObject jsonObject = JSONObject.parseObject(execute.body());
            String msg = jsonObject.containsKey("msg") ?  jsonObject.getString("msg") : jsonObject.getString("message");

            if(execute.getStatus() == HttpStatus.HTTP_OK){
               return ResponseData.builder()
                       .code(jsonObject.getString("code"))
                       .message(msg)
                       .data(jsonObject.getString("data"))
                       .build();
           }
           return ResponseData.builder()
                   .code(jsonObject.getString("code"))
                   .message( msg)
                   .build();
        }catch (Exception e){
           log.info("bamboo接口请求异常：{}",e.getMessage());
        }
        return null;
    }

    /**
     * get 请求
     */
    /**
     * post 请求
     */
    public static ResponseData doGet(String url,String bodyString){
        log.info("url={}，bodyString={}",url,bodyString);
        String key = "accessToken="+ACCESSTOKEN+"&appKey="+APPKEY+bodyString+APPSECRET;
        String sign = org.springframework.util.DigestUtils.md5DigestAsHex(key.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        log.info("sign={}",sign);
        try{
            if(url.indexOf("?") > -1){
                url = url+"&appKey="+APPKEY+"&accessToken="+ACCESSTOKEN+"&sign="+sign;
            }else{
                url = url+"?appKey="+APPKEY+"&accessToken="+ACCESSTOKEN+"&sign="+sign;
            }

            HttpRequest post = HttpUtil.createGet(
                    url
            );
            post.header("OPEN-API","ACCESS_TOKEN");
            post.body(bodyString);
            HttpResponse execute = post.execute();
            log.info("url={}，response={}",url,execute.body());
            JSONObject jsonObject = JSONObject.parseObject(execute.body());
            String msg = jsonObject.containsKey("msg") ?  jsonObject.getString("msg") : jsonObject.getString("message");

            if(execute.getStatus() == HttpStatus.HTTP_OK){
                return ResponseData.builder()
                        .code(jsonObject.getString("code"))
                        .message(msg)
                        .data(jsonObject.getString("data"))
                        .build();
            }
            return ResponseData.builder()
                    .code(jsonObject.getString("code"))
                    .message(msg)
                    .build();
        }catch (Exception e){
            log.info("bamboo接口请求异常：{}",e.getMessage());
        }
        return null;
    }





    public static void main(String[] args) {
        //测试
        String bodyString = "{\"page\":1,\"size\":10}";
        ResponseData responseData = HttpUtils.doPost("http://localhost:8102/api/v1/openapi/query_site_page",bodyString);
        System.out.println(responseData);


//        String bodyString = "{\"currentPage\":1,\"itemsPerPage\":10,\"orgCode\":\"BOCSH\"}";
//        ResponseData responseData = BambooHttpUtils.doPost(ApiUrlConst.QUERY_PRODUCT_LIST,bodyString);
//        System.out.println(responseData);

//                String bodyString = "{\"customerCode\":\"ZGYHSH001\"}";
//        ResponseData responseData = BambooHttpUtils.doGet(ApiUrlConst.QUERY_DELIVERYDATE_LIST, bodyString);
//        System.out.println(responseData);

//        HttpRequest post = HttpUtil.createPost(
//                ApiUrlConst.QUERY_PRODUCT_LIST+"?appKey=250709d0ebbb401395a2360cec612aed&accessToken=c05331d6cc339f766c373048ae44730c&sign=CEC6FF46ACB6CF80DE49776A6D905C54"
//        );
//        String bodyString = "{\"currentPage\":1,\"itemsPerPage\":10,\"orgCode\":\"BOCSH\"}";
//        post.header("OPEN-API","ACCESS_TOKEN");
//        post.body(bodyString);
//        HttpResponse execute = post.execute();
//        System.out.println(execute.body());
//        System.out.println(execute.getStatus());
    }

}
