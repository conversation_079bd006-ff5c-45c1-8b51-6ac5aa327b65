package com.zzz.service;

import com.zzz.component.bamboo.response.ProductResultResponse;
import com.zzz.entity.BbProduct;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 商品表（Bamboo） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IBbProductService extends IService<BbProduct> {

    void addProduct(ProductResultResponse item);

    BbProduct getProductByCid(Integer id);
}
