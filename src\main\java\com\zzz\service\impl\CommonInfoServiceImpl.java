package com.zzz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.dto.CommonInfoDto;
import com.zzz.emuns.CommonEnum;
import com.zzz.emuns.CommonErrorEnum;
import com.zzz.entity.CommonInfo;
import com.zzz.exception.ServiceException;
import com.zzz.mapper.CommonInfoMapper;
import com.zzz.service.ICommonInfoService;
import com.zzz.util.SpringBeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since  2024-06-20
 */
@Service
public class CommonInfoServiceImpl extends ServiceImpl<CommonInfoMapper, CommonInfo> implements ICommonInfoService {

    @Autowired
    private CommonInfoMapper commonInfoMapper;

    @Override
    public List<CommonInfo> getBaseInfoByCode(CommonEnum code) {
        List<CommonInfo> list = commonInfoMapper
            .selectList(new LambdaQueryWrapper<CommonInfo>().eq(CommonInfo::getType, code.name()));
        // 配置信息不存在
        if (list.isEmpty()) {
            throw new ServiceException(CommonErrorEnum.COMMON_IS_NULL);
        }
        return list;
    }

    @Override
    public List<CommonInfoDto> queryAgreementList() {
        // TODO 支付相关协议列表
        List<CommonInfo> list = commonInfoMapper.selectList(new LambdaQueryWrapper<CommonInfo>().in(CommonInfo::getType,
                CommonEnum.EXCHANGE_NITICE_P.name(), CommonEnum.STEALTH_CONTENT_P.name()));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return SpringBeanUtil.copyProperties(list, CommonInfoDto.class);
    }

    @Override
    public CommonInfoDto getAgreementDetail(Integer id) {
        CommonInfo commonInfo = commonInfoMapper.selectById(id);
        return SpringBeanUtil.copyProperties(commonInfo, CommonInfoDto.class);
    }
}
