package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.dto.BannerDto;
import com.zzz.emuns.DeleteStatusEnum;
import com.zzz.emuns.StatusPlusEnum;
import com.zzz.entity.TbBanner;
import com.zzz.mapper.TbBannerMapper;
import com.zzz.service.ITbBannerService;
import com.zzz.util.SpringBeanUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 小程序轮播表 服务实现类
 * </p>
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
public class TbBannerServiceImpl extends ServiceImpl<TbBannerMapper, TbBanner> implements ITbBannerService {

    @Resource
    private TbBannerMapper tbBannerMapper;

    @Override
    public List<BannerDto> getBannerList() {
        List<TbBanner> list = tbBannerMapper.selectList(Wrappers.<TbBanner>lambdaQuery()
                .eq(TbBanner::getIsDel, DeleteStatusEnum.NORMAL.value)
                .eq(TbBanner::getStatus, StatusPlusEnum.ENABLE.value())
                .orderByAsc(TbBanner::getSort));

        return SpringBeanUtil.copyProperties(list, BannerDto.class);
    }
}
