package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @Version 1.0
 * @since 2024-06-21
 **/
@Data
@ApiModel("优惠券列表查询参数")
public class CouponPageVo {

    @ApiModelProperty("用户ID，用户登录成功后必传，只查询用户可领取优惠券")
    private Integer userId;

    @ApiModelProperty(value="页数" ,required = true,example = "10")
    private Integer size;

    @ApiModelProperty(value="页码",required = true,example = "1")
    private Integer page;
}
