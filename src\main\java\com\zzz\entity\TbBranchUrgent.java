package com.zzz.entity;

    import java.math.BigDecimal;
    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import java.time.LocalDate;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 网点日期加急费用设置
    * </p>
*
* <AUTHOR>
* @since 2024-06-22
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_branch_urgent")
    @ApiModel(value="TbBranchUrgent对象", description="网点日期加急费用设置")
    public class TbBranchUrgent implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "自增ID")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "网点ID")
    private Integer branchId;

            @ApiModelProperty(value = "加急费，单位元")
    private BigDecimal fee;

            @ApiModelProperty(value = "加急时间：2024-06-01")
    private LocalDate date;

            @ApiModelProperty(value = "设置时间")
    private LocalDateTime createDate;

            @ApiModelProperty(value = "有效状态，0有效，1失效；")
    private Integer status;

            @ApiModelProperty(value = "备注")
    private String remark;


}
