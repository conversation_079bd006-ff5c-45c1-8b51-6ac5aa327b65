package com.zzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zzz.dto.BranchDto;
import com.zzz.entity.TbBranch;
import com.zzz.vo.BranchPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 网点 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TbBranchMapper extends BaseMapper<TbBranch> {

    /**
     * 分页列表查询
     * <AUTHOR> to 2024/9/1
     */
    IPage<BranchDto> listPage(@Param("page") IPage<TbBranch> page,
                              @Param("vo") BranchPageVo vo);

}
