package com.zzz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 16:09 2024/6/3
 * @ Description：手机号登录 请求入参
 * @ Version:     1.0
 */
@Data
@ApiModel("手机号登录参数")
public class LoginPhoneVo implements Serializable {

    @ApiModelProperty(name = "phone", value = "用户注册手机号", required = true,example = "13666666666")
    @NotBlank
    private String phone;
}
