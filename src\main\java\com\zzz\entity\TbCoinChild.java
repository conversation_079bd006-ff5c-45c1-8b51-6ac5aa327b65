package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 
    * </p>
*
* <AUTHOR>
* @since 2024-06-23
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("tb_coin_child")
    @ApiModel(value="TbCoinChild对象", description="")
    public class TbCoinChild implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "主键，唯一")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "零钱包ID")
    private Integer coinId;

            @ApiModelProperty(value = "面额")
    private Integer aclValue;

            @ApiModelProperty(value = "数量")
    private Integer num;

            @ApiModelProperty(value = "备注")
    private String remark;

            @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;


}
