package com.zzz.service;

import com.zzz.dto.UserCouponPageDto;
import com.zzz.entity.TbCouponMrg;
import com.zzz.entity.TbCouponReceive;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zzz.response.JSONResult;
import com.zzz.vo.UserCouponPageVo;

import java.util.List;

/**
 * <p>
 * 优惠券领取表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface ITbCouponReceiveService extends IService<TbCouponReceive> {

    UserCouponPageDto getUserCouponPage(UserCouponPageVo userCouponPageVo);

    JSONResult doReceive(Integer coupenId);

    void unLockById(Integer couponId);

    List<TbCouponReceive> getUserRecCouponList(Integer id);

    TbCouponMrg getCouponByRecId(Integer couponId);

    void useById(Integer couponId);

    void lockById(Integer couponId);
}
