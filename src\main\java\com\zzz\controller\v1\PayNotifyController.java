package com.zzz.controller.v1;

import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.zzz.constants.PaymentConstants;
import com.zzz.emuns.OrderPayStatusEnum;
import com.zzz.emuns.OrderStatusEnum;
import com.zzz.entity.TbOrder;
import com.zzz.service.ITbOrderService;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付结果通知处理
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pay/notify")
public class PayNotifyController {

    @Autowired
    private ITbOrderService iTbOrderService;

    /**
     * 支付结果通知
     * @param notifyData 通知数据
     * @return 处理结果
     */
    @PostMapping("/wechat")
    public String wechatNotify(@RequestBody Map<String, Object> notifyData) {
        log.info("微信支付结果通知: {}", notifyData);

        try {
            // 解析通知数据
            String merOrderId = (String) notifyData.get("merOrderId");
            String status = (String) notifyData.get("status");
            String targetOrderId = (String) notifyData.get("targetOrderId");

            // 查询订单
            TbOrder order = iTbOrderService.getOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber, merOrderId)
            );

            if (order == null) {
                log.error("微信支付结果通知: 订单不存在, merOrderId={}", merOrderId);
                return "FAIL";
            }

            // 更新订单状态
            if ("PAID".equals(status) || "SUCCESS".equals(status)) {
                // 支付成功
                order.setPayStatus(OrderPayStatusEnum.PAY.value);
                order.setOrderState(OrderStatusEnum.WAIT_DELIVER.value);
                order.setPayTime(LocalDateTime.now());
                order.setUpdateTime(LocalDateTime.now());
                order.setOutOrderNo(targetOrderId);
                iTbOrderService.updateById(order);

                // 处理支付成功后的业务逻辑
                // ...

                return "SUCCESS";
            } else {
                // 支付失败
                order.setPayStatus(OrderPayStatusEnum.PAY_FAIL.value);
                order.setUpdateTime(LocalDateTime.now());
                iTbOrderService.updateById(order);

                return "SUCCESS";
            }
        } catch (Exception e) {
            log.error("微信支付结果通知处理异常", e);
            return "FAIL";
        }
    }

    /**
     * 支付宝支付结果通知
     * @param notifyData 通知数据
     * @return 处理结果
     */
    @PostMapping("/alipay")
    public String alipayNotify(@RequestBody Map<String, Object> notifyData) {
        log.info("支付宝支付结果通知: {}", notifyData);

        try {
            // 解析通知数据
            String merOrderId = (String) notifyData.get("merOrderId");
            String status = (String) notifyData.get("status");
            String targetOrderId = (String) notifyData.get("targetOrderId");

            // 查询订单
            TbOrder order = iTbOrderService.getOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber, merOrderId)
            );

            if (order == null) {
                log.error("支付宝支付结果通知: 订单不存在, merOrderId={}", merOrderId);
                return "FAIL";
            }

            // 更新订单状态
            if ("PAID".equals(status) || "SUCCESS".equals(status)) {
                // 支付成功
                order.setPayStatus(OrderPayStatusEnum.PAY.value);
                order.setOrderState(OrderStatusEnum.WAIT_DELIVER.value);
                order.setPayTime(LocalDateTime.now());
                order.setUpdateTime(LocalDateTime.now());
                order.setOutOrderNo(targetOrderId);
                order.setPayType(PaymentConstants.PaymentType.ALIPAY);
                iTbOrderService.updateById(order);

                // 处理支付成功后的业务逻辑
                // ...

                return "SUCCESS";
            } else {
                // 支付失败
                order.setPayStatus(OrderPayStatusEnum.PAY_FAIL.value);
                order.setUpdateTime(LocalDateTime.now());
                iTbOrderService.updateById(order);

                return "SUCCESS";
            }
        } catch (Exception e) {
            log.error("支付宝支付结果通知处理异常", e);
            return "FAIL";
        }
    }

    /**
     * 云闪付支付结果通知
     * @param notifyData 通知数据
     * @return 处理结果
     */
    @PostMapping("/unionpay")
    public String unionpayNotify(@RequestBody Map<String, Object> notifyData) {
        log.info("云闪付支付结果通知: {}", notifyData);

        try {
            // 解析通知数据
            String merOrderId = (String) notifyData.get("merOrderId");
            String status = (String) notifyData.get("status");
            String targetOrderId = (String) notifyData.get("targetOrderId");

            // 查询订单
            TbOrder order = iTbOrderService.getOne(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<TbOrder>()
                            .eq(TbOrder::getOrderNumber, merOrderId)
            );

            if (order == null) {
                log.error("云闪付支付结果通知: 订单不存在, merOrderId={}", merOrderId);
                return "FAIL";
            }

            // 更新订单状态
            if ("PAID".equals(status) || "SUCCESS".equals(status)) {
                // 支付成功
                order.setPayStatus(OrderPayStatusEnum.PAY.value);
                order.setOrderState(OrderStatusEnum.WAIT_DELIVER.value);
                order.setPayTime(LocalDateTime.now());
                order.setUpdateTime(LocalDateTime.now());
                order.setOutOrderNo(targetOrderId);
                order.setPayType(PaymentConstants.PaymentType.UNIONPAY);
                iTbOrderService.updateById(order);

                // 处理支付成功后的业务逻辑
                // ...

                return "SUCCESS";
            } else {
                // 支付失败
                order.setPayStatus(OrderPayStatusEnum.PAY_FAIL.value);
                order.setUpdateTime(LocalDateTime.now());
                iTbOrderService.updateById(order);

                return "SUCCESS";
            }
        } catch (Exception e) {
            log.error("云闪付支付结果通知处理异常", e);
            return "FAIL";
        }
    }
}
