package com.zzz.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ Author     ：zqw.
 * @ Date       ：Created in 15:40 2024/3/4
 * @ Description： RSA 解密请求属性
 * @ Version:     1.0
 */
@Retention(RetentionPolicy.RUNTIME)//运行时生效
@Target(ElementType.FIELD)//作用在属性上
public @interface RsaRequest {
}
