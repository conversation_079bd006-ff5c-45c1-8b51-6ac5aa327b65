package com.zzz.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryPersonalMaxPurchaseLimitVo {
    /**
     *  兑换类别：1结汇、2购汇
     */
    @ApiModelProperty("额度类型")
    private String icetype;
    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String personName;

    /**
     * 国籍编码
     */
    @ApiModelProperty("国籍编码")
    private String country;
    /**
     * 证件编码
     */
    @ApiModelProperty("证件编码")
    private String doctype;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String docno;
    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码")
    private String sitecode;
    /**
     * 门店柜筒编码
     */
    @ApiModelProperty("门店柜筒编码")
    private String strboxcode;
    /**
     * 币种
     */
    @ApiModelProperty("币种")
    private String currency;
}
