package com.zzz.entity;

    import com.baomidou.mybatisplus.annotation.TableName;
    import com.baomidou.mybatisplus.annotation.IdType;
    import com.baomidou.mybatisplus.annotation.TableId;
    import java.time.LocalDateTime;
    import java.io.Serializable;
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
    import lombok.Data;
    import lombok.EqualsAndHashCode;
    import lombok.experimental.Accessors;

/**
* <p>
    * 用户来源信息表
    * </p>
*
* <AUTHOR>
* @since 2024-11-09
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @TableName("user_open_info")
    @ApiModel(value="UserOpenInfo对象", description="用户来源信息表")
    public class UserOpenInfo implements Serializable {

    private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "主键ID")
            @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

            @ApiModelProperty(value = "用户id，与表 user 中id关联")
    private Integer userId;

            @ApiModelProperty(value = "注册渠道 1 微信小程序 2 支付宝小程序 3 H5")
    private Integer reg;

            @ApiModelProperty(value = "昵称（所有渠道通用，暂未用到）")
    private String nickname;

            @ApiModelProperty(value = "头像地址（所有渠道通用，暂未用到）")
    private String avatar;

            @ApiModelProperty(value = "小程序用户唯一标识。包括但不限于微信、支付宝小程序，H5为随机值")
    private String openId;

            @ApiModelProperty(value = "是否删除(NULL:已删除,0:未删除)")
    private Boolean isDel;

            @ApiModelProperty(value = "添加时间")
    private LocalDateTime addTime;

            @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;


}
