package com.zzz.util;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;

/**
 * packageName com.zzz.service.impl
 *
 * <AUTHOR>
 * @version JDK 11
 * @date 2024/6/17
 */
@Configuration
public class UploadToS3Util {
    public static final String BUCKET_NAME = "plus";
    public static final String PREFIX_NAME = "data/gis/temp/";
    /**
     * s3上传文件
     * @param uploadKey  bucket中的存储文件名
     * @param file 待上传的文件流
     */
    public static boolean uploadToS3(MultipartFile file,String uploadKey, S3Client s3Client) {
        try {
            if (file == null) {
                return false;
            }
            //添加文件夹dev（文件夹其实就是一个前缀）
            String prefix = PREFIX_NAME;
            String folderKey = prefix.concat(uploadKey);
            PutObjectRequest putObjectRequest = PutObjectRequest.builder().bucket(BUCKET_NAME).key(folderKey).build();
            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(file.getBytes()));
            return true;
        }catch (S3Exception | IOException e){
            System.err.println(e.getMessage());
            return false;
        }
    }
    /**
     * s3文件下载
     * @param
     */
    public static void DownloadFromS3(S3Client s3Client, String path) throws IOException {
        // S3上存储的key
        String key = path;
        GetObjectRequest objectRequest = GetObjectRequest
                .builder()
                .key(key)
                .bucket(BUCKET_NAME)
                .build();
        if (path.contains("/")){
            makeDir("D:/"+path);
        }
       // String fileName = path.substring(path.lastIndexOf("/")+1);
        InputStream inputStream = null;
        FileOutputStream fos = null;
        try{
            ResponseBytes<GetObjectResponse> objectBytes = s3Client.getObjectAsBytes(objectRequest);
            inputStream = objectBytes.asInputStream();
            fos = new FileOutputStream("D:/"+path);
            byte[] b = new byte[1024];
            int length;
            while((length= inputStream.read(b))>0){
                fos.write(b,0,length);
            }
        }catch (Exception e){
            e.getMessage();
        }finally {
            if (inputStream!=null){
                inputStream.close();
            }
            if (fos!=null){
                fos.close();
            }
        }
    }

    public static void makeDir(String filePath) {
        if (filePath.lastIndexOf('/') > 0) {
            String dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
        }
    }

    /**
     * s3删除文件
     * @param uploadKey
     * @return
     */


    /**
     * 获取桶里所有的文件
     */
    public static List<Object> ListBucketObjects(S3Client s3Client, String prefix) {
        List<Object> list = new ArrayList<>();
        try {
            ListObjectsRequest listObjects = ListObjectsRequest
                    .builder()
                    .bucket(BUCKET_NAME)
                    .prefix(PREFIX_NAME+prefix)
                    .build();
            ListObjectsResponse res = s3Client.listObjects(listObjects);
            List<S3Object> objects = res.contents();

            for (ListIterator iterVals = objects.listIterator(); iterVals.hasNext(); ) {

                //JSONObject job = new JSONObject();
                S3Object myValue = (S3Object) iterVals.next();
                list.add(myValue.key());
               // System.out.print("\n The name of the key is " + myValue.key());
               // job.put("name",myValue.key());
               // System.out.print("\n The object is " + calKb(myValue.size()) + " KBs");
                //job.put("size",calKb(myValue.size())+"  KBs");
               // System.out.print("\n The owner is " + myValue.owner());
                //jar.add(job);
            }
        } catch (S3Exception e) {
            System.err.println(e.awsErrorDetails().errorMessage());
            list.add(e.awsErrorDetails().errorMessage());
            System.exit(1);
        }
        return list;
    }
    //文件大小
    private static long calKb(Long val) {
        return val/1024;
    }
    
    public static void rtkDownloadFromS3(S3Client s3Client, String path) throws IOException {
        // S3上存储的key
        String key =PREFIX_NAME.concat(path);
        GetObjectRequest objectRequest = GetObjectRequest
                .builder()
                .key(key)
                .bucket(BUCKET_NAME)
                .build();
        makeDir("D:/rtk/");
        // String fileName = path.substring(path.lastIndexOf("/")+1);
        InputStream inputStream = null;
        FileOutputStream fos = null;
        try{
            ResponseBytes<GetObjectResponse> objectBytes = s3Client.getObjectAsBytes(objectRequest);
            inputStream = objectBytes.asInputStream();
            fos = new FileOutputStream("D:/rtk/"+path);
            byte[] b = new byte[1024];
            int length;
            while((length= inputStream.read(b))>0){
                fos.write(b,0,length);
            }
        }catch (Exception e){
            e.getMessage();
        }finally {
            if (inputStream!=null){
                inputStream.close();
            }
            if (fos!=null){
                fos.close();
            }
        }
    }
}


