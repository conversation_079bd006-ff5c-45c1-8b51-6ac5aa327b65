package com.zzz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zzz.component.bamboo.response.OrgBranchInfo;
import com.zzz.entity.BbOrgBranch;
import com.zzz.mapper.BbOrgBranchMapper;
import com.zzz.service.IBbOrgBranchService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zzz.service.ITbBranchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 客户网点表（Bamboo） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class BbOrgBranchServiceImpl extends ServiceImpl<BbOrgBranchMapper, BbOrgBranch> implements IBbOrgBranchService {

    @Autowired
    private BbOrgBranchMapper bbOrgBranchMapper;

    @Autowired
    private ITbBranchService tbBranchService;

    @Override
    @Transactional
    public void addOrgBranch(BbOrgBranch orgBranchInfo,Integer status) {
        int insert = bbOrgBranchMapper.insert(orgBranchInfo);
        if(status == 1){
            //增加系统内部网点数据
            tbBranchService.addBranch(
                    orgBranchInfo.getId(),
                    orgBranchInfo.getBranchCode(),
                    orgBranchInfo.getBranchName(),
                    orgBranchInfo.getContactPerson(),orgBranchInfo.getContactTelephone(),
                    1
            );
        }
    }

    @Override
    public List<BbOrgBranch> getList() {
        return bbOrgBranchMapper.selectList(null);
    }

    @Override
    public List<BbOrgBranch> getListByPid(Integer id) {
        return bbOrgBranchMapper.selectList(new LambdaQueryWrapper<BbOrgBranch>().eq(BbOrgBranch::getCusId,id));
    }
}
